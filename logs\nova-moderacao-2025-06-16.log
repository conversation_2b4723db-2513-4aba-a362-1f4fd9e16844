[16/06/2025 00:20:27] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:20:27] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:20:27] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:20:27] INFO: ▣ 27 comandos carregados
[16/06/2025 00:20:27] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:20:27] INFO: ▣ Eventos carregados
[16/06/2025 00:20:27] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:20:27] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:20:27] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:20:27] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:20:27] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:20:27] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:23:44] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:23:44] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:23:44] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:23:44] INFO: ▣ 27 comandos carregados
[16/06/2025 00:23:44] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:23:44] INFO: ▣ Eventos carregados
[16/06/2025 00:23:44] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:23:44] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:23:44] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:23:44] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:23:44] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:23:44] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:23:44] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:23:44] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:23:44] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:24:53] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:24:53] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:24:54] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:24:54] INFO: ▣ 27 comandos carregados
[16/06/2025 00:24:54] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:24:54] INFO: ▣ Eventos carregados
[16/06/2025 00:24:54] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:24:54] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:24:54] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:24:54] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:24:54] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:24:54] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:24:54] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:24:54] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:24:54] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:26:28] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:26:28] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:26:28] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:26:28] INFO: ▣ 27 comandos carregados
[16/06/2025 00:26:28] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:26:28] INFO: ▣ Eventos carregados
[16/06/2025 00:26:28] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:26:28] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:26:28] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:26:28] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:26:28] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:26:28] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:26:28] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:26:28] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:26:28] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:27:27] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:27:27] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:27:27] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:27:27] INFO: ▣ 27 comandos carregados
[16/06/2025 00:27:27] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:27:27] INFO: ▣ Eventos carregados
[16/06/2025 00:27:27] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:27:27] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:27:27] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:27:27] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:27:27] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:27:27] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:27:27] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:27:27] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:27:27] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:33:36] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:33:36] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:33:36] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:33:36] INFO: ▣ 27 comandos carregados
[16/06/2025 00:33:36] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:33:36] INFO: ▣ Eventos carregados
[16/06/2025 00:33:36] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:33:36] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:33:36] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:33:36] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:33:36] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:33:36] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:33:36] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:33:36] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:33:36] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:34:24] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:34:24] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:34:24] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:34:24] INFO: ▣ 27 comandos carregados
[16/06/2025 00:34:24] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:34:24] INFO: ▣ Eventos carregados
[16/06/2025 00:34:24] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:34:24] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:34:24] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:34:24] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:34:24] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:34:24] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:34:24] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:34:24] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:34:24] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:35:05] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:35:05] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:35:05] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:35:05] INFO: ▣ 27 comandos carregados
[16/06/2025 00:35:05] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:35:05] INFO: ▣ Eventos carregados
[16/06/2025 00:35:05] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:35:05] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:35:05] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:35:05] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:35:05] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:35:05] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:35:05] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:35:05] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:35:05] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:36:08] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:36:08] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:36:08] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:36:09] INFO: ▣ 27 comandos carregados
[16/06/2025 00:36:09] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:36:09] INFO: ▣ Eventos carregados
[16/06/2025 00:36:09] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:36:09] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:36:09] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:36:09] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:36:09] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:36:09] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:36:09] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:36:09] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:36:09] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:37:04] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:37:04] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:37:04] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:37:04] INFO: ▣ 27 comandos carregados
[16/06/2025 00:37:04] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:37:04] INFO: ▣ Eventos carregados
[16/06/2025 00:37:04] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:37:04] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:37:04] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:37:04] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:37:04] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:37:04] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:37:04] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:37:04] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:37:04] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:37:04] INFO: ▣ Servidor web inicializado
[16/06/2025 00:37:05] INFO: 🔄 Registrando comandos slash...
[16/06/2025 00:37:06] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 00:37:06] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 00:37:06] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 00:37:06] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 00:37:06] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 00:37:06] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.8651331}
[16/06/2025 00:44:41] INFO: Limpeza de logs concluída: 0 arquivos removidos
[16/06/2025 00:46:46] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:46:46] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:46:46] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:46:46] INFO: ▣ 27 comandos carregados
[16/06/2025 00:46:46] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:46:46] INFO: ▣ Eventos carregados
[16/06/2025 00:46:46] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:46:46] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:46:46] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:46:46] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:46:46] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:46:46] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:46:46] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:46:46] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:46:46] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:47:23] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:47:23] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:47:23] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:47:23] INFO: ▣ 27 comandos carregados
[16/06/2025 00:47:23] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:47:23] INFO: ▣ Eventos carregados
[16/06/2025 00:47:23] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:47:23] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:47:23] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:47:23] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:47:23] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:47:23] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:47:23] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:47:23] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:47:23] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:48:12] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:48:12] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:48:12] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:48:12] INFO: ▣ 27 comandos carregados
[16/06/2025 00:48:12] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:48:12] INFO: ▣ Eventos carregados
[16/06/2025 00:48:12] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:48:12] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:48:12] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:48:12] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:48:12] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:48:12] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:48:12] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:48:12] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:48:12] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:48:39] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:48:39] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:48:39] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:48:39] INFO: ▣ 27 comandos carregados
[16/06/2025 00:48:39] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:48:39] INFO: ▣ Eventos carregados
[16/06/2025 00:48:39] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:48:39] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:48:39] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:48:39] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:48:39] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:48:39] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:48:39] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:48:39] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:48:39] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:50:00] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:50:00] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:50:00] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:50:00] INFO: ▣ 27 comandos carregados
[16/06/2025 00:50:00] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:50:00] INFO: ▣ Eventos carregados
[16/06/2025 00:50:00] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:50:00] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:50:00] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:50:00] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:50:00] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:50:00] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:50:00] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:50:00] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:50:00] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:50:00] INFO: ▣ Servidor web inicializado
[16/06/2025 00:50:01] INFO: 🔄 Registrando comandos slash...
[16/06/2025 00:50:02] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 00:50:02] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 00:50:02] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 00:50:02] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 00:50:02] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 00:50:02] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.5725063}
[16/06/2025 00:53:38] ERROR: Erro ao configurar mensagem de verificação: | Meta: {"error":"Cannot read properties of undefined (reading 'channel_id')","stack":"TypeError: Cannot read properties of undefined (reading 'channel_id')\n    at VerificationSystem.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\VerificationSystem.js:76:61)\n    at RealTimeIntegration.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:599:38)\n    at RealTimeIntegration.processVerificationSettings (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:440:32)\n    at RealTimeIntegration.processSection (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:156:41)\n    at RealTimeIntegration.processConfigChange (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:85:39)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:762:74\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"}
[16/06/2025 00:56:24] INFO: ▣ Sistema de logs inicializado
[16/06/2025 00:56:24] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 00:56:24] INFO: ▣ Banco de dados inicializado
[16/06/2025 00:56:25] INFO: ▣ 27 comandos carregados
[16/06/2025 00:56:25] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 00:56:25] INFO: ▣ Eventos carregados
[16/06/2025 00:56:25] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 00:56:25] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 00:56:25] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 00:56:25] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 00:56:25] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 00:56:25] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 00:56:25] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 00:56:25] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 00:56:25] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 00:56:25] INFO: ▣ Servidor web inicializado
[16/06/2025 00:56:26] INFO: 🔄 Registrando comandos slash...
[16/06/2025 00:56:26] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 00:56:26] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 00:56:26] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 00:56:26] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 00:56:26] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 00:56:26] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":5.0736927}
[16/06/2025 01:05:51] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:05:51] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:05:51] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:05:51] INFO: ▣ 27 comandos carregados
[16/06/2025 01:05:51] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:05:51] INFO: ▣ Eventos carregados
[16/06/2025 01:05:51] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:05:51] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:05:51] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:05:51] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:05:51] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:05:51] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:05:51] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:05:51] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:05:51] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:06:45] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:06:45] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:06:45] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:06:45] INFO: ▣ 27 comandos carregados
[16/06/2025 01:06:45] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:06:45] INFO: ▣ Eventos carregados
[16/06/2025 01:06:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:06:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:06:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:06:45] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:06:45] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:06:45] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:06:45] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:06:45] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:06:45] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:07:36] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:07:36] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:07:36] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:07:37] INFO: ▣ 27 comandos carregados
[16/06/2025 01:07:37] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:07:37] INFO: ▣ Eventos carregados
[16/06/2025 01:07:37] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:07:37] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:07:37] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:07:37] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:07:37] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:07:37] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:07:37] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:07:37] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:07:37] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:07:37] INFO: ▣ Servidor web inicializado
[16/06/2025 01:07:38] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:07:39] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:07:39] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:07:39] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:07:39] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:07:39] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:07:39] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.5453077}
[16/06/2025 01:11:09] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:11:09] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:11:09] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:11:09] INFO: ▣ 27 comandos carregados
[16/06/2025 01:11:09] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:11:09] INFO: ▣ Eventos carregados
[16/06/2025 01:11:09] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:11:09] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:11:09] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:11:09] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:11:09] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:11:09] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:11:09] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:11:09] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:11:09] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:11:55] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:11:55] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:11:55] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:11:55] INFO: ▣ 27 comandos carregados
[16/06/2025 01:11:55] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:11:56] INFO: ▣ Eventos carregados
[16/06/2025 01:11:56] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:11:56] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:11:56] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:11:56] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:11:56] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:11:56] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:11:56] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:11:56] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:11:56] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:11:56] INFO: ▣ Servidor web inicializado
[16/06/2025 01:11:57] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:11:58] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:11:58] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:11:58] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:11:58] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:11:58] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:11:58] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.6974952}
[16/06/2025 01:13:57] INFO: COMANDO: deploy-local executado | Meta: {"command":"deploy-local","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[16/06/2025 01:15:16] ERROR: Erro ao configurar mensagem de verificação: | Meta: {"error":"Cannot read properties of undefined (reading 'channel_id')","stack":"TypeError: Cannot read properties of undefined (reading 'channel_id')\n    at VerificationSystem.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\VerificationSystem.js:76:61)\n    at RealTimeIntegration.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:599:38)\n    at RealTimeIntegration.processVerificationSettings (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:440:32)\n    at RealTimeIntegration.processSection (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:156:41)\n    at RealTimeIntegration.processConfigChange (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:85:39)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:812:74\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"}
[16/06/2025 01:15:47] ERROR: Erro ao configurar mensagem de verificação: | Meta: {"error":"Cannot read properties of undefined (reading 'channel_id')","stack":"TypeError: Cannot read properties of undefined (reading 'channel_id')\n    at VerificationSystem.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\VerificationSystem.js:76:61)\n    at RealTimeIntegration.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:599:38)\n    at RealTimeIntegration.processVerificationSettings (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:440:32)\n    at RealTimeIntegration.processSection (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:156:41)\n    at RealTimeIntegration.processConfigChange (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:85:39)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:812:74\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"}
[16/06/2025 01:16:35] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:16:35] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:16:35] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:16:35] INFO: ▣ 27 comandos carregados
[16/06/2025 01:16:35] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:16:35] INFO: ▣ Eventos carregados
[16/06/2025 01:16:35] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:16:35] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:16:35] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:16:35] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:16:35] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:16:35] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:16:35] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:16:35] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:16:35] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:16:35] INFO: ▣ Servidor web inicializado
[16/06/2025 01:16:37] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:16:37] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:16:37] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:16:37] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:16:37] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:16:37] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:16:37] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.5855828}
[16/06/2025 01:17:29] ERROR: Erro ao configurar mensagem de verificação: | Meta: {"error":"Cannot read properties of undefined (reading 'channel_id')","stack":"TypeError: Cannot read properties of undefined (reading 'channel_id')\n    at VerificationSystem.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\VerificationSystem.js:76:61)\n    at RealTimeIntegration.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:599:38)\n    at RealTimeIntegration.processVerificationSettings (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:440:32)\n    at RealTimeIntegration.processSection (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:156:41)\n    at RealTimeIntegration.processConfigChange (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:85:39)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:812:74\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"}
[16/06/2025 01:17:51] ERROR: Erro ao configurar mensagem de verificação: | Meta: {"error":"Cannot read properties of undefined (reading 'channel_id')","stack":"TypeError: Cannot read properties of undefined (reading 'channel_id')\n    at VerificationSystem.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\VerificationSystem.js:76:61)\n    at RealTimeIntegration.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:599:38)\n    at RealTimeIntegration.processVerificationSettings (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:440:32)\n    at RealTimeIntegration.processSection (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:156:41)\n    at RealTimeIntegration.processConfigChange (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:85:39)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:812:74\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"}
[16/06/2025 01:18:30] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:18:30] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:18:30] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:18:30] INFO: ▣ 27 comandos carregados
[16/06/2025 01:18:30] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:18:31] INFO: ▣ Eventos carregados
[16/06/2025 01:18:31] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:18:31] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:18:31] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:18:31] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:18:31] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:18:31] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:18:31] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:18:31] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:18:31] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:18:31] INFO: ▣ Servidor web inicializado
[16/06/2025 01:18:32] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:18:32] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:18:32] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:18:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:18:32] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:18:32] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:18:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.5682787}
[16/06/2025 01:20:20] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:20:20] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:20:20] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:20:20] INFO: ▣ 27 comandos carregados
[16/06/2025 01:20:20] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:20:20] INFO: ▣ Eventos carregados
[16/06/2025 01:20:20] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:20:20] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:20:20] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:20:20] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:20:20] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:20:20] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:20:20] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:20:20] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:20:20] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:20:20] INFO: ▣ Servidor web inicializado
[16/06/2025 01:20:22] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:20:22] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:20:22] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:20:22] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:20:22] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:20:22] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:20:22] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":5.0986153}
[16/06/2025 01:21:26] ERROR: Erro ao configurar mensagem de verificação: | Meta: {"error":"Cannot read properties of undefined (reading 'channel_id')","stack":"TypeError: Cannot read properties of undefined (reading 'channel_id')\n    at VerificationSystem.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\VerificationSystem.js:76:61)\n    at RealTimeIntegration.setupVerificationMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:599:38)\n    at RealTimeIntegration.processVerificationSettings (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:440:32)\n    at RealTimeIntegration.processSection (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:156:41)\n    at RealTimeIntegration.processConfigChange (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:85:39)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:812:74\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"}
[16/06/2025 01:26:36] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:26:36] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:26:36] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:26:36] INFO: ▣ 27 comandos carregados
[16/06/2025 01:26:36] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:26:36] INFO: ▣ Eventos carregados
[16/06/2025 01:26:36] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:26:36] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:26:36] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:26:36] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:26:36] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:26:36] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:27:22] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:27:22] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:27:22] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:27:22] INFO: ▣ 27 comandos carregados
[16/06/2025 01:27:22] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:27:22] INFO: ▣ Eventos carregados
[16/06/2025 01:27:22] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:27:22] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:27:22] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:27:22] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:27:22] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:27:22] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:27:22] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:27:22] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:27:22] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:27:22] INFO: ▣ Servidor web inicializado
[16/06/2025 01:27:23] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:27:24] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:27:24] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:27:24] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:27:24] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:27:24] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:27:24] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.7441513}
[16/06/2025 01:29:55] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:29:55] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:29:55] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:29:55] INFO: ▣ 27 comandos carregados
[16/06/2025 01:29:55] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:29:55] INFO: ▣ Eventos carregados
[16/06/2025 01:29:55] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:29:55] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:29:55] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:29:55] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:29:55] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:29:55] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:29:55] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:29:55] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:29:55] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:29:55] INFO: ▣ Servidor web inicializado
[16/06/2025 01:29:57] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:29:57] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:29:57] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:29:57] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:29:57] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:29:57] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:29:57] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.9654511}
[16/06/2025 01:33:23] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:33:23] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:33:23] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:33:23] INFO: ▣ 27 comandos carregados
[16/06/2025 01:33:23] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:33:23] INFO: ▣ Eventos carregados
[16/06/2025 01:33:23] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:33:23] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:33:23] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:33:23] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:33:23] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:33:23] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:33:23] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:33:23] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:33:23] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:33:23] INFO: ▣ Servidor web inicializado
[16/06/2025 01:33:24] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:33:25] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:33:25] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:33:25] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:33:25] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:33:25] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:33:25] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.9936837}
[16/06/2025 01:35:42] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:35:42] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:35:42] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:35:42] INFO: ▣ 27 comandos carregados
[16/06/2025 01:35:42] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:35:43] INFO: ▣ Eventos carregados
[16/06/2025 01:35:43] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:35:43] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:35:43] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:35:43] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:35:43] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:35:43] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:35:43] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:35:43] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:35:43] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:35:43] INFO: ▣ Servidor web inicializado
[16/06/2025 01:35:44] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:35:45] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:35:45] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:35:45] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:35:45] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:35:45] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:35:45] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":5.0210865}
[16/06/2025 01:36:04] ERROR: Uncaught Exception: | Meta: {"error":"SQLITE_ERROR: table verification_config has no column named anti_bot","stack":"Error: SQLITE_ERROR: table verification_config has no column named anti_bot"}
[16/06/2025 01:37:29] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:37:29] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:37:29] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:37:30] INFO: ▣ 27 comandos carregados
[16/06/2025 01:37:30] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:37:30] INFO: ▣ Eventos carregados
[16/06/2025 01:37:30] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:37:30] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:37:30] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:37:30] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:37:30] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:37:30] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:37:30] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:37:30] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:37:30] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:37:30] INFO: ▣ Servidor web inicializado
[16/06/2025 01:37:31] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:37:32] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:37:32] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:37:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:37:32] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:37:32] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:37:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.8815128}
[16/06/2025 01:41:34] INFO: ▣ Iniciando encerramento gracioso...
[16/06/2025 01:43:21] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:43:21] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:43:21] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:43:21] INFO: ▣ 27 comandos carregados
[16/06/2025 01:43:21] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:43:21] INFO: ▣ Eventos carregados
[16/06/2025 01:43:21] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:43:21] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:43:21] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:43:21] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:43:21] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:43:21] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:43:21] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:43:21] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:43:21] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:43:21] INFO: ▣ Servidor web inicializado
[16/06/2025 01:43:23] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:43:23] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:43:23] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:43:23] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:43:23] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:43:23] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:43:23] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.6917282}
[16/06/2025 01:47:18] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:47:18] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:47:18] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:47:18] INFO: ▣ 27 comandos carregados
[16/06/2025 01:47:18] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:47:18] INFO: ▣ Eventos carregados
[16/06/2025 01:47:18] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:47:18] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:47:18] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:47:18] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:47:18] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:47:18] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:47:18] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:47:18] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:47:18] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:47:19] INFO: ▣ Servidor web inicializado
[16/06/2025 01:47:20] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:47:21] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:47:21] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:47:21] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:47:21] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:47:21] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:47:21] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.7670222}
[16/06/2025 01:51:45] INFO: COMANDO: clear executado | Meta: {"command":"clear","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[16/06/2025 01:52:07] INFO: ▣ Sistema de logs inicializado
[16/06/2025 01:52:07] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 01:52:07] INFO: ▣ Banco de dados inicializado
[16/06/2025 01:52:07] INFO: ▣ 27 comandos carregados
[16/06/2025 01:52:07] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 01:52:07] INFO: ▣ Eventos carregados
[16/06/2025 01:52:07] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 01:52:07] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 01:52:07] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 01:52:07] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 01:52:07] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 01:52:07] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 01:52:07] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 01:52:07] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 01:52:07] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 01:52:07] INFO: ▣ Servidor web inicializado
[16/06/2025 01:52:09] INFO: 🔄 Registrando comandos slash...
[16/06/2025 01:52:09] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 01:52:09] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 01:52:09] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 01:52:09] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 01:52:09] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 01:52:09] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.590015}
[16/06/2025 02:00:17] INFO: ▣ Sistema de logs inicializado
[16/06/2025 02:00:17] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 02:00:17] INFO: ▣ Banco de dados inicializado
[16/06/2025 02:00:18] INFO: ▣ 27 comandos carregados
[16/06/2025 02:00:18] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 02:00:18] INFO: ▣ Eventos carregados
[16/06/2025 02:00:18] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 02:00:18] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 02:00:18] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 02:00:18] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 02:00:18] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 02:00:18] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 02:00:18] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 02:00:18] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 02:00:18] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 02:00:18] INFO: ▣ Servidor web inicializado
[16/06/2025 02:00:19] INFO: 🔄 Registrando comandos slash...
[16/06/2025 02:00:20] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 02:00:20] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 02:00:20] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 02:00:20] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 02:00:20] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 02:00:20] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.622638}
[16/06/2025 13:17:21] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:17:21] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:17:21] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:17:22] INFO: ▣ 27 comandos carregados
[16/06/2025 13:17:22] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:17:22] INFO: ▣ Eventos carregados
[16/06/2025 13:17:22] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:17:22] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:17:22] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:17:22] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:17:22] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:17:22] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:17:22] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:17:22] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:17:22] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:17:23] INFO: ▣ Servidor web inicializado
[16/06/2025 13:17:25] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:17:26] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 13:17:26] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:17:26] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:17:26] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:17:26] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:17:26] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":42.0353035}
[16/06/2025 13:20:17] INFO: COMANDO: clear executado | Meta: {"command":"clear","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[16/06/2025 13:23:55] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:23:55] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:23:55] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:23:55] INFO: ▣ 27 comandos carregados
[16/06/2025 13:23:55] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:23:55] INFO: ▣ Eventos carregados
[16/06/2025 13:23:55] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:23:55] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:23:55] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:23:55] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:23:55] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:23:55] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:23:55] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:23:55] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:23:55] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:23:55] INFO: ▣ Servidor web inicializado
[16/06/2025 13:23:57] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:23:57] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 13:23:57] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:23:57] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:23:57] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:23:57] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:23:57] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":5.1020528}
[16/06/2025 13:24:47] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:24:47] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:24:47] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:24:47] INFO: ▣ 27 comandos carregados
[16/06/2025 13:24:47] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:24:47] INFO: ▣ Eventos carregados
[16/06/2025 13:24:47] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:24:47] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:24:47] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:24:47] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:24:47] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:24:47] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:24:47] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:24:47] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:24:47] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:24:47] INFO: ▣ Servidor web inicializado
[16/06/2025 13:24:49] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:24:49] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 13:24:49] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:24:49] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:24:49] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:24:49] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:24:49] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.747224}
[16/06/2025 13:29:46] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:29:46] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:29:46] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:29:46] INFO: ▣ 27 comandos carregados
[16/06/2025 13:29:46] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:29:46] INFO: ▣ Eventos carregados
[16/06/2025 13:29:46] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:29:46] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:29:46] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:29:46] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:29:46] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:29:46] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:29:46] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:29:46] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:29:46] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:29:46] INFO: ▣ Servidor web inicializado
[16/06/2025 13:29:48] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:29:48] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 13:29:48] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:29:48] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:29:48] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:29:48] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:29:48] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":5.1813879}
[16/06/2025 13:30:50] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:30:50] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:30:50] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:30:50] INFO: ▣ 27 comandos carregados
[16/06/2025 13:30:50] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:30:51] INFO: ▣ Eventos carregados
[16/06/2025 13:30:51] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:30:51] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:30:51] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:30:51] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:30:51] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:30:51] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:30:51] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:30:51] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:30:51] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:30:51] INFO: ▣ Servidor web inicializado
[16/06/2025 13:30:52] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:30:53] INFO: ✅ 27 comandos slash registrados globalmente
[16/06/2025 13:30:53] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:30:53] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:30:53] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:30:53] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:30:53] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.8521139}
[16/06/2025 13:32:12] ERROR: Erro no botão de verificação verify_user: | Meta: {"user":"558672715243061269","guild":"1381755403326455838","error":"Unknown interaction","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ButtonInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async handleVerificationButton (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:758:24)\n    at async handleButton (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:307:13)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:20:13)"}
[16/06/2025 13:32:21] INFO: ▣ Iniciando encerramento gracioso...
[16/06/2025 13:32:30] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:32:30] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:32:31] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:32:31] INFO: ▣ 28 comandos carregados
[16/06/2025 13:32:31] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:32:31] INFO: ▣ Eventos carregados
[16/06/2025 13:32:31] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:32:31] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:32:31] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:32:31] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:32:31] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:32:31] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:32:31] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:32:31] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:32:31] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:32:31] INFO: ▣ Servidor web inicializado
[16/06/2025 13:32:32] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:32:33] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 13:32:33] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:32:33] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:32:33] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:32:33] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:32:33] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.5028272}
[16/06/2025 13:34:12] ERROR: Erro no botão de verificação verify_user: | Meta: {"user":"558672715243061269","guild":"1381755403326455838","error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ButtonInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async handleVerificationButton (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:758:24)\n    at async handleButton (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:307:13)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:20:13)"}
[16/06/2025 13:37:46] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:37:46] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:37:46] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:37:46] INFO: ▣ 28 comandos carregados
[16/06/2025 13:37:46] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:37:46] INFO: ▣ Eventos carregados
[16/06/2025 13:37:46] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:37:46] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:37:46] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:37:46] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:37:46] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:37:46] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:37:46] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:37:46] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:37:46] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:37:47] INFO: ▣ Servidor web inicializado
[16/06/2025 13:37:48] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:37:49] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 13:37:49] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:37:49] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:37:49] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:37:49] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:37:49] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":5.0540093}
[16/06/2025 13:38:51] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:38:51] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:38:51] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:38:51] INFO: ▣ 28 comandos carregados
[16/06/2025 13:38:51] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:38:51] INFO: ▣ Eventos carregados
[16/06/2025 13:38:51] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:38:51] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:38:51] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:38:51] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:38:51] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:38:51] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:38:51] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:38:51] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:38:51] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:38:51] INFO: ▣ Servidor web inicializado
[16/06/2025 13:38:52] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:38:53] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 13:38:53] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:38:53] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:38:53] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:38:53] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:38:53] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.7601328}
[16/06/2025 13:39:48] ERROR: Erro no botão de verificação verify_user: | Meta: {"user":"558672715243061269","guild":"1381755403326455838","error":"Unknown interaction","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ButtonInteraction.deferReply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:123:22)\n    at async handleVerificationButton (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:700:13)\n    at async handleButton (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:307:13)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:20:13)"}
[16/06/2025 13:39:56] ERROR: Erro no comando deploy-local: | Meta: {"user":"558672715243061269","guild":"1381755403326455838","options":[],"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\deploy-local.js:90:17)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:127:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[16/06/2025 13:39:57] ERROR: Erro ao enviar mensagem de erro: | Meta: {"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:176:17)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[16/06/2025 13:39:57] WARN: COMANDO: deploy-local falhou | Meta: {"command":"deploy-local","user":"558672715243061269","guild":"1381755403326455838","success":false,"error":"Interaction has already been acknowledged."}
[16/06/2025 13:39:57] INFO: COMANDO: deploy-local executado | Meta: {"command":"deploy-local","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[16/06/2025 13:40:04] WARN: Comando não encontrado: fix-verification | Meta: {"user":"558672715243061269","guild":"1381755403326455838"}
[16/06/2025 13:40:04] ERROR: Uncaught Exception: | Meta: {"error":"Unknown interaction","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:52:16)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[16/06/2025 13:40:05] INFO: COMANDO: fix-verification executado | Meta: {"command":"fix-verification","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[16/06/2025 13:43:13] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:43:13] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:43:13] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:43:13] INFO: ▣ 28 comandos carregados
[16/06/2025 13:43:13] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:43:13] INFO: ▣ Eventos carregados
[16/06/2025 13:43:13] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:43:13] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:43:13] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:43:13] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:43:13] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:43:13] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:43:13] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:43:13] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:43:13] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:43:13] INFO: ▣ Servidor web inicializado
[16/06/2025 13:43:15] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:43:15] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 13:43:15] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:43:15] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:43:15] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:43:15] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:43:15] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.7451175}
[16/06/2025 13:44:44] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:44:44] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:44:44] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:44:45] INFO: ▣ 28 comandos carregados
[16/06/2025 13:44:45] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:44:45] INFO: ▣ Eventos carregados
[16/06/2025 13:44:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:44:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:44:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:44:45] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:44:45] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:44:45] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:44:45] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:44:45] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:44:45] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:44:45] INFO: ▣ Servidor web inicializado
[16/06/2025 13:44:46] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:44:47] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 13:44:47] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:44:47] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:44:47] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:44:47] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:44:47] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":5.2431524}
[16/06/2025 13:50:57] INFO: ▣ Iniciando encerramento gracioso...
[16/06/2025 13:51:23] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:51:23] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:51:23] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:51:23] INFO: ▣ 28 comandos carregados
[16/06/2025 13:51:23] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:51:23] INFO: ▣ Eventos carregados
[16/06/2025 13:51:23] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:51:23] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:51:23] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:51:23] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:51:23] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:51:23] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:51:23] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:51:23] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:51:23] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:51:23] INFO: ▣ Servidor web inicializado
[16/06/2025 13:51:25] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:51:25] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 13:51:25] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:51:25] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:51:25] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:51:25] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:51:25] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":5.076442}
[16/06/2025 13:52:44] INFO: ▣ Sistema de logs inicializado
[16/06/2025 13:52:44] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 13:52:44] INFO: ▣ Banco de dados inicializado
[16/06/2025 13:52:44] INFO: ▣ 28 comandos carregados
[16/06/2025 13:52:44] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 13:52:44] INFO: ▣ Eventos carregados
[16/06/2025 13:52:44] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 13:52:44] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 13:52:44] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 13:52:44] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 13:52:44] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 13:52:44] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 13:52:44] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 13:52:44] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 13:52:44] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 13:52:44] INFO: ▣ Servidor web inicializado
[16/06/2025 13:52:46] INFO: 🔄 Registrando comandos slash...
[16/06/2025 13:52:46] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 13:52:46] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 13:52:46] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 13:52:46] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 13:52:47] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 13:52:47] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":5.3914663}
[16/06/2025 13:55:50] INFO: COMANDO: deploy-local executado | Meta: {"command":"deploy-local","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[16/06/2025 14:04:04] INFO: ▣ Sistema de logs inicializado
[16/06/2025 14:04:04] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 14:04:04] INFO: ▣ Banco de dados inicializado
[16/06/2025 14:04:04] INFO: ▣ 28 comandos carregados
[16/06/2025 14:04:04] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 14:04:04] INFO: ▣ Eventos carregados
[16/06/2025 14:04:04] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 14:04:04] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 14:04:04] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 14:04:04] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 14:04:04] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 14:04:04] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 14:04:04] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 14:04:04] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 14:04:04] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 14:04:05] INFO: ▣ Servidor web inicializado
[16/06/2025 14:04:05] ERROR: Unhandled Rejection: | Meta: {"error":"this.db.all is not a function","stack":"TypeError: this.db.all is not a function\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\database\\SecureDatabaseManager.js:131:25\n    at new Promise (<anonymous>)\n    at SecureDatabaseManager.executeQuery (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\database\\SecureDatabaseManager.js:116:16)\n    at SecureDatabaseManager.initialize (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\database\\SecureDatabaseManager.js:358:20)\n    at new WebServer (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:43:27)\n    at initializeBot (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:121:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[16/06/2025 14:06:01] INFO: ▣ Sistema de logs inicializado
[16/06/2025 14:06:01] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 14:06:01] INFO: ▣ Banco de dados inicializado
[16/06/2025 14:06:01] INFO: ▣ 28 comandos carregados
[16/06/2025 14:06:01] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 14:06:01] INFO: ▣ Eventos carregados
[16/06/2025 14:06:01] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 14:06:01] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 14:06:01] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 14:06:01] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 14:06:01] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 14:06:01] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 14:06:01] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 14:06:01] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 14:06:01] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 14:06:01] INFO: ▣ Servidor web inicializado
[16/06/2025 14:06:04] INFO: 🔄 Registrando comandos slash...
[16/06/2025 14:06:05] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 14:06:05] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 14:06:05] INFO: Configuração padrão criada para: biblioteca dos canudos (907346457143705681)
[16/06/2025 14:06:05] INFO: ✅ Verificação concluída: 2 configurados, 1 novos
[16/06/2025 14:06:05] INFO: 📋 Cache de configurações carregado para 2 servidores
[16/06/2025 14:06:05] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 14:06:05] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":6.3274996}
[16/06/2025 14:08:29] INFO: ▣ Iniciando encerramento gracioso...
[16/06/2025 14:09:09] INFO: ▣ Sistema de logs inicializado
[16/06/2025 14:09:09] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 14:09:10] INFO: ▣ Banco de dados inicializado
[16/06/2025 14:09:10] INFO: ▣ 28 comandos carregados
[16/06/2025 14:09:10] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 14:09:10] INFO: ▣ Eventos carregados
[16/06/2025 14:09:10] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 14:09:10] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 14:09:10] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 14:09:10] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 14:09:10] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 14:09:10] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 14:09:10] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 14:09:10] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 14:09:10] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 14:09:10] INFO: ▣ Servidor web inicializado
[16/06/2025 14:09:12] INFO: 🔄 Registrando comandos slash...
[16/06/2025 14:09:13] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 14:09:13] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 14:09:13] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 14:09:13] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 14:09:13] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 14:09:13] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":5.953514}
[16/06/2025 14:11:09] INFO: ▣ Sistema de logs inicializado
[16/06/2025 14:11:09] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 14:11:09] INFO: ▣ Banco de dados inicializado
[16/06/2025 14:11:09] INFO: ▣ 28 comandos carregados
[16/06/2025 14:11:09] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 14:11:09] INFO: ▣ Eventos carregados
[16/06/2025 14:11:09] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 14:11:09] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 14:11:09] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 14:11:09] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 14:11:09] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 14:11:09] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 14:11:09] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 14:11:09] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 14:11:09] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 14:11:09] INFO: ▣ Servidor web inicializado
[16/06/2025 14:11:11] INFO: 🔄 Registrando comandos slash...
[16/06/2025 14:11:12] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 14:11:12] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 14:11:12] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 14:11:12] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 14:11:12] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 14:11:12] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":5.5459423}
[16/06/2025 14:25:00] ERROR: Erro no evento guildMemberAdd: | Meta: {"error":"Cannot read properties of undefined (reading 'getGuildConfig')","stack":"TypeError: Cannot read properties of undefined (reading 'getGuildConfig')\n    at Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\guildMemberAdd.js:10:49)\n    at Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:195:58)\n    at Client.emit (node:events:530:35)\n    at module.exports [as GUILD_MEMBER_ADD] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\handlers\\GUILD_MEMBER_ADD.js:17:14)\n    at WebSocketManager.handlePacket (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:351:31)\n    at WebSocketManager.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:235:12)\n    at WebSocketManager.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)\n    at WebSocketShard.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\ws\\dist\\index.js:1190:51)\n    at WebSocketShard.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)\n    at WebSocketShard.onMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\ws\\dist\\index.js:1007:14)"}
[16/06/2025 14:26:05] ERROR: Unhandled Rejection: | Meta: {"error":"Cannot read properties of null (reading 'id')","stack":"TypeError: Cannot read properties of null (reading 'id')\n    at AnalyticsSystem.trackCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\AnalyticsSystem.js:592:50)\n    at Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\AnalyticsSystem.js:42:22)\n    at Client.emit (node:events:530:35)\n    at InteractionCreateAction.handle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\actions\\InteractionCreate.js:97:12)\n    at module.exports [as INTERACTION_CREATE] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\handlers\\INTERACTION_CREATE.js:4:36)\n    at WebSocketManager.handlePacket (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:351:31)\n    at WebSocketManager.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:235:12)\n    at WebSocketManager.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)\n    at WebSocketShard.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\ws\\dist\\index.js:1190:51)\n    at WebSocketShard.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)"}
[16/06/2025 14:29:51] INFO: ▣ Sistema de logs inicializado
[16/06/2025 14:29:51] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 14:29:51] INFO: ▣ Banco de dados inicializado
[16/06/2025 14:29:51] INFO: ▣ 28 comandos carregados
[16/06/2025 14:29:51] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 14:29:51] INFO: ▣ Eventos carregados
[16/06/2025 14:29:51] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 14:29:51] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 14:29:51] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 14:29:51] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 14:29:51] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 14:29:51] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 14:29:51] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 14:29:51] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 14:29:51] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 14:29:51] INFO: ▣ Servidor web inicializado
[16/06/2025 14:29:53] INFO: 🔄 Registrando comandos slash...
[16/06/2025 14:29:54] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 14:29:54] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 14:29:54] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 14:29:54] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 14:29:54] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 14:29:54] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":6.019061}
[16/06/2025 14:39:53] ERROR: Unhandled Rejection: | Meta: {"error":"SQLite3 can only bind numbers, strings, bigints, buffers, and null","stack":"TypeError: SQLite3 can only bind numbers, strings, bigints, buffers, and null\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\database\\DatabaseManager.js:653:18\n    at new Promise (<anonymous>)\n    at DatabaseManager.logModerationAction (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\database\\DatabaseManager.js:646:16)\n    at Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warn.js:142:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:127:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[16/06/2025 14:47:16] INFO: ▣ Sistema de logs inicializado
[16/06/2025 14:47:16] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 14:47:16] INFO: ▣ Banco de dados inicializado
[16/06/2025 14:47:17] INFO: ▣ 28 comandos carregados
[16/06/2025 14:47:17] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 14:47:17] INFO: ▣ Eventos carregados
[16/06/2025 14:47:17] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 14:47:17] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 14:47:17] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 14:47:17] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 14:47:17] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 14:47:17] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 14:47:17] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 14:47:17] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 14:47:17] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 14:47:17] INFO: ▣ Servidor web inicializado
[16/06/2025 14:47:19] INFO: 🔄 Registrando comandos slash...
[16/06/2025 14:47:20] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 14:47:20] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 14:47:20] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 14:47:20] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 14:47:20] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 14:47:20] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":6.5476321}
[16/06/2025 14:55:49] INFO: COMANDO: clear executado | Meta: {"command":"clear","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[16/06/2025 15:12:37] INFO: COMANDO: deploy-local executado | Meta: {"command":"deploy-local","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[16/06/2025 15:12:54] INFO: COMANDO: warnings executado | Meta: {"command":"warnings","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[16/06/2025 15:13:13] INFO: COMANDO: warn executado | Meta: {"command":"warn","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[16/06/2025 15:22:38] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1202721227999944777","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:24:17)"}
[16/06/2025 15:22:41] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1202721227999944777","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:24:17)"}
[16/06/2025 15:22:49] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1202721227999944777","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:24:17)"}
[16/06/2025 15:22:53] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1202721227999944777","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:24:17)"}
[16/06/2025 15:31:03] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1202721227999944777","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:24:17)"}
[16/06/2025 15:31:10] WARN: IA baniu xkssad: Discurso de ódio ou ameaças graves detectadas pela IA
[16/06/2025 15:31:10] ERROR: Erro ao executar ação da IA: | Meta: {"error":"guildConfig is not defined","stack":"ReferenceError: guildConfig is not defined\n    at executeAIAction (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:636:61)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:461:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[16/06/2025 15:31:10] ERROR: Unhandled Rejection: | Meta: {"error":"SQLite3 can only bind numbers, strings, bigints, buffers, and null","stack":"TypeError: SQLite3 can only bind numbers, strings, bigints, buffers, and null\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\database\\DatabaseManager.js:653:18\n    at new Promise (<anonymous>)\n    at DatabaseManager.logModerationAction (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\database\\DatabaseManager.js:646:16)\n    at executeAIAction (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:621:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:461:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[16/06/2025 15:33:42] INFO: ▣ Sistema de logs inicializado
[16/06/2025 15:33:42] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 15:33:42] INFO: ▣ Banco de dados inicializado
[16/06/2025 15:33:42] INFO: ▣ 28 comandos carregados
[16/06/2025 15:33:42] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 15:33:42] INFO: ▣ Eventos carregados
[16/06/2025 15:33:42] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 15:33:42] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 15:33:42] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 15:33:42] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 15:33:42] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 15:33:42] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 15:33:42] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 15:33:42] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 15:33:42] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 15:33:42] INFO: ▣ Servidor web inicializado
[16/06/2025 15:33:44] INFO: 🔄 Registrando comandos slash...
[16/06/2025 15:33:45] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 15:33:45] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 15:33:45] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 15:33:45] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 15:33:45] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 15:33:45] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":5.7648355}
[16/06/2025 16:00:08] INFO: ▣ Sistema de logs inicializado
[16/06/2025 16:00:08] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 16:00:08] INFO: ▣ Banco de dados inicializado
[16/06/2025 16:00:08] INFO: ▣ 28 comandos carregados
[16/06/2025 16:00:08] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 16:00:08] INFO: ▣ Eventos carregados
[16/06/2025 16:00:08] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 16:00:08] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 16:00:08] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 16:00:08] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 16:00:08] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 16:00:09] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 16:00:09] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 16:00:09] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 16:00:09] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 16:00:09] INFO: ▣ Servidor web inicializado
[16/06/2025 16:00:10] INFO: 🔄 Registrando comandos slash...
[16/06/2025 16:00:11] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 16:00:11] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 16:00:11] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 16:00:11] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 16:00:11] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 16:00:11] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":5.9112074}
[16/06/2025 16:39:29] INFO: ▣ Sistema de logs inicializado
[16/06/2025 16:39:29] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 16:39:29] INFO: ▣ Banco de dados inicializado
[16/06/2025 16:39:29] INFO: ▣ 28 comandos carregados
[16/06/2025 16:39:29] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 16:39:29] INFO: ▣ Eventos carregados
[16/06/2025 16:39:29] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 16:39:29] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 16:39:29] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 16:39:29] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 16:39:29] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 16:39:29] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 16:39:29] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 16:39:29] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 16:39:29] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 16:39:30] INFO: ▣ Servidor web inicializado
[16/06/2025 16:39:31] INFO: 🔄 Registrando comandos slash...
[16/06/2025 16:39:32] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 16:39:32] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 16:39:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 16:39:32] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 16:39:32] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 16:39:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":6.0714705}
[16/06/2025 16:40:29] INFO: ▣ Sistema de logs inicializado
[16/06/2025 16:40:29] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 16:40:29] INFO: ▣ Banco de dados inicializado
[16/06/2025 16:40:29] INFO: ▣ 28 comandos carregados
[16/06/2025 16:40:29] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 16:40:29] INFO: ▣ Eventos carregados
[16/06/2025 16:40:29] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 16:40:29] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 16:40:29] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 16:40:29] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 16:40:29] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 16:40:29] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 16:40:29] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 16:40:29] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 16:40:29] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 16:40:29] INFO: ▣ Servidor web inicializado
[16/06/2025 16:40:31] INFO: 🔄 Registrando comandos slash...
[16/06/2025 16:40:32] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 16:40:32] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 16:40:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 16:40:32] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 16:40:32] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 16:40:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":5.4562524}
[16/06/2025 16:41:31] INFO: ▣ Sistema de logs inicializado
[16/06/2025 16:41:31] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 16:41:31] INFO: ▣ Banco de dados inicializado
[16/06/2025 16:41:31] INFO: ▣ 28 comandos carregados
[16/06/2025 16:41:31] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 16:41:31] INFO: ▣ Eventos carregados
[16/06/2025 16:41:31] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 16:41:31] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 16:41:31] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 16:41:31] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 16:41:31] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 16:41:31] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 16:41:31] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 16:41:31] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 16:41:31] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 16:41:31] INFO: ▣ Servidor web inicializado
[16/06/2025 16:41:33] INFO: 🔄 Registrando comandos slash...
[16/06/2025 16:41:34] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 16:41:34] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 16:41:34] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 16:41:34] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 16:41:34] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 16:41:34] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":6.2743851}
[16/06/2025 16:46:45] INFO: ▣ Sistema de logs inicializado
[16/06/2025 16:46:45] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 16:46:45] INFO: ▣ Banco de dados inicializado
[16/06/2025 16:46:45] INFO: ▣ 28 comandos carregados
[16/06/2025 16:46:45] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 16:46:45] INFO: ▣ Eventos carregados
[16/06/2025 16:46:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 16:46:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 16:46:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 16:46:45] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 16:46:45] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 16:46:45] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 16:46:45] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 16:46:45] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 16:46:45] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 16:46:45] INFO: ▣ Servidor web inicializado
[16/06/2025 16:46:47] INFO: 🔄 Registrando comandos slash...
[16/06/2025 16:46:48] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 16:46:48] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 16:46:48] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 16:46:48] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 16:46:48] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 16:46:48] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":6.5902602}
[16/06/2025 16:54:08] INFO: ▣ Sistema de logs inicializado
[16/06/2025 16:54:08] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 16:54:08] INFO: ▣ Banco de dados inicializado
[16/06/2025 16:54:09] INFO: ▣ 28 comandos carregados
[16/06/2025 16:54:09] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 16:54:09] INFO: ▣ Eventos carregados
[16/06/2025 16:54:09] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 16:54:09] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 16:54:09] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 16:54:09] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 16:54:09] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 16:54:09] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 16:54:09] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 16:54:09] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 16:54:09] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 16:54:09] INFO: ▣ Servidor web inicializado
[16/06/2025 16:54:11] INFO: 🔄 Registrando comandos slash...
[16/06/2025 16:54:12] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 16:54:12] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 16:54:12] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 16:54:12] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 16:54:12] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 16:54:12] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":6.1412998}
[16/06/2025 16:55:56] INFO: ▣ Sistema de logs inicializado
[16/06/2025 16:55:57] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 16:55:57] INFO: ▣ Banco de dados inicializado
[16/06/2025 16:55:57] INFO: ▣ 28 comandos carregados
[16/06/2025 16:55:57] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 16:55:57] INFO: ▣ Eventos carregados
[16/06/2025 16:55:57] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 16:55:57] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 16:55:57] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 16:55:57] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 16:55:57] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 16:55:57] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 16:55:57] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 16:55:57] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 16:55:57] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 16:55:57] INFO: ▣ Servidor web inicializado
[16/06/2025 16:55:59] INFO: 🔄 Registrando comandos slash...
[16/06/2025 16:55:59] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 16:55:59] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 16:55:59] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 16:55:59] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 16:55:59] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 16:55:59] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":5.6923128}
[16/06/2025 16:59:10] INFO: ▣ Sistema de logs inicializado
[16/06/2025 16:59:10] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 16:59:10] INFO: ▣ Banco de dados inicializado
[16/06/2025 16:59:10] INFO: ▣ 28 comandos carregados
[16/06/2025 16:59:10] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 16:59:10] INFO: ▣ Eventos carregados
[16/06/2025 16:59:10] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 16:59:10] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 16:59:10] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 16:59:10] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 16:59:10] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 16:59:10] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 16:59:10] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 16:59:10] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 16:59:10] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 16:59:10] INFO: ▣ Servidor web inicializado
[16/06/2025 16:59:12] INFO: 🔄 Registrando comandos slash...
[16/06/2025 16:59:13] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 16:59:13] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 16:59:13] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 16:59:13] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 16:59:13] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 16:59:13] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":5.9376302}
[16/06/2025 17:04:59] INFO: ▣ Sistema de logs inicializado
[16/06/2025 17:04:59] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 17:04:59] INFO: ▣ Banco de dados inicializado
[16/06/2025 17:04:59] INFO: ▣ 28 comandos carregados
[16/06/2025 17:04:59] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 17:04:59] INFO: ▣ Eventos carregados
[16/06/2025 17:04:59] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 17:04:59] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 17:04:59] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 17:04:59] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 17:04:59] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 17:04:59] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 17:04:59] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 17:04:59] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 17:04:59] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 17:04:59] INFO: ▣ Servidor web inicializado
[16/06/2025 17:05:01] INFO: 🔄 Registrando comandos slash...
[16/06/2025 17:05:02] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 17:05:02] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 17:05:02] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 17:05:02] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 17:05:02] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 17:05:02] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":6.1888347}
[16/06/2025 17:07:41] INFO: ▣ Sistema de logs inicializado
[16/06/2025 17:07:41] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 17:07:41] INFO: ▣ Banco de dados inicializado
[16/06/2025 17:07:41] INFO: ▣ 28 comandos carregados
[16/06/2025 17:07:41] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 17:07:41] INFO: ▣ Eventos carregados
[16/06/2025 17:07:41] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 17:07:41] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 17:07:41] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 17:07:41] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 17:07:41] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 17:07:41] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 17:07:41] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 17:07:41] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 17:07:41] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 17:07:41] INFO: ▣ Servidor web inicializado
[16/06/2025 17:07:43] INFO: 🔄 Registrando comandos slash...
[16/06/2025 17:07:44] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 17:07:44] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 17:07:44] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 17:07:44] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 17:07:44] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 17:07:44] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":6.5525501}
[16/06/2025 17:08:49] INFO: ▣ Sistema de logs inicializado
[16/06/2025 17:08:49] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 17:08:49] INFO: ▣ Banco de dados inicializado
[16/06/2025 17:08:49] INFO: ▣ 28 comandos carregados
[16/06/2025 17:08:49] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 17:08:49] INFO: ▣ Eventos carregados
[16/06/2025 17:08:49] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 17:08:49] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 17:08:49] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 17:08:49] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 17:08:49] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 17:08:49] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 17:08:49] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 17:08:49] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 17:08:49] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 17:08:49] INFO: ▣ Servidor web inicializado
[16/06/2025 17:08:51] INFO: 🔄 Registrando comandos slash...
[16/06/2025 17:08:56] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 17:08:56] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 17:08:56] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 17:08:56] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 17:08:56] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 17:08:56] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":10.8465217}
[16/06/2025 17:35:01] INFO: ▣ Sistema de logs inicializado
[16/06/2025 17:35:01] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 17:35:01] INFO: ▣ Banco de dados inicializado
[16/06/2025 17:35:01] INFO: ▣ 28 comandos carregados
[16/06/2025 17:35:01] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 17:35:01] INFO: ▣ Eventos carregados
[16/06/2025 17:35:01] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 17:35:01] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 17:35:01] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 17:35:01] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 17:35:01] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 17:35:01] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 17:35:01] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 17:35:01] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 17:35:01] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 17:35:01] INFO: ▣ Servidor web inicializado
[16/06/2025 17:35:04] INFO: 🔄 Registrando comandos slash...
[16/06/2025 17:35:04] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 17:35:04] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 17:35:04] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 17:35:04] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 17:35:04] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 17:35:04] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":5.7457722}
[16/06/2025 17:35:28] INFO: ▣ Iniciando encerramento gracioso...
[16/06/2025 17:46:44] INFO: ▣ Sistema de logs inicializado
[16/06/2025 17:46:44] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 17:46:45] INFO: ▣ Banco de dados inicializado
[16/06/2025 17:46:45] INFO: ▣ 28 comandos carregados
[16/06/2025 17:46:45] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 17:46:45] INFO: ▣ Eventos carregados
[16/06/2025 17:46:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 17:46:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 17:46:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 17:46:45] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 17:46:45] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 17:46:45] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 17:46:45] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 17:46:45] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 17:46:45] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 17:46:45] INFO: ▣ Servidor web inicializado
[16/06/2025 17:46:47] INFO: 🔄 Registrando comandos slash...
[16/06/2025 17:46:48] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 17:46:48] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 17:46:48] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 17:46:48] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 17:46:48] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 17:46:48] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":5.679097}
[16/06/2025 18:46:48] INFO: Limpeza de logs concluída: 0 arquivos removidos
[16/06/2025 19:46:48] INFO: Limpeza de logs concluída: 0 arquivos removidos
[16/06/2025 20:17:03] INFO: ▣ Sistema de logs inicializado
[16/06/2025 20:17:03] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 20:17:04] INFO: ▣ Banco de dados inicializado
[16/06/2025 20:17:04] INFO: ▣ 28 comandos carregados
[16/06/2025 20:17:04] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 20:17:04] INFO: ▣ Eventos carregados
[16/06/2025 20:17:04] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 20:17:04] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 20:17:04] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 20:17:04] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 20:17:04] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 20:17:04] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 20:17:04] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 20:17:04] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 20:17:04] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 20:17:04] INFO: ▣ Servidor web inicializado
[16/06/2025 20:17:06] INFO: 🔄 Registrando comandos slash...
[16/06/2025 20:17:06] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 20:17:06] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 20:17:06] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 20:17:06] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 20:17:06] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 20:17:06] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":5.4583248}
[16/06/2025 21:17:06] INFO: Limpeza de logs concluída: 0 arquivos removidos
[16/06/2025 22:17:06] INFO: Limpeza de logs concluída: 0 arquivos removidos
[16/06/2025 22:54:01] INFO: ▣ Sistema de logs inicializado
[16/06/2025 22:54:01] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 22:54:01] INFO: ▣ Banco de dados inicializado
[16/06/2025 22:54:01] INFO: ▣ 28 comandos carregados
[16/06/2025 22:54:01] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 22:54:01] INFO: ▣ Eventos carregados
[16/06/2025 22:54:01] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 22:54:01] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 22:54:01] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 22:54:01] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 22:54:01] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 22:54:01] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 22:54:01] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 22:54:01] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 22:54:01] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 22:54:01] INFO: ▣ Servidor web inicializado
[16/06/2025 22:54:01] ERROR: Uncaught Exception: | Meta: {"error":"listen EADDRINUSE: address already in use :::3000","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:635:24)\n    at WebServer.start (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:177:18)\n    at initializeBot (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:122:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[16/06/2025 22:55:06] INFO: ▣ Sistema de logs inicializado
[16/06/2025 22:55:06] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 22:55:06] INFO: ▣ Banco de dados inicializado
[16/06/2025 22:55:07] INFO: ▣ 28 comandos carregados
[16/06/2025 22:55:07] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 22:55:07] INFO: ▣ Eventos carregados
[16/06/2025 22:55:07] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 22:55:07] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 22:55:07] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 22:55:07] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 22:55:07] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 22:55:07] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 22:55:07] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 22:55:07] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 22:55:07] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 22:55:07] INFO: ▣ Servidor web inicializado
[16/06/2025 22:55:07] ERROR: Uncaught Exception: | Meta: {"error":"listen EADDRINUSE: address already in use :::3000","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:635:24)\n    at WebServer.start (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:177:18)\n    at initializeBot (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:123:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[16/06/2025 22:56:20] INFO: ▣ Sistema de logs inicializado
[16/06/2025 22:56:20] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 22:56:20] INFO: ▣ Banco de dados inicializado
[16/06/2025 22:56:20] INFO: ▣ 28 comandos carregados
[16/06/2025 22:56:20] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 22:56:20] INFO: ▣ Eventos carregados
[16/06/2025 22:56:20] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 22:56:20] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 22:56:20] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 22:56:20] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 22:56:20] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 22:56:20] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 22:56:20] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 22:56:20] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 22:56:20] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 22:56:20] INFO: ▣ Servidor web inicializado
[16/06/2025 22:56:20] ERROR: Uncaught Exception: | Meta: {"error":"listen EADDRINUSE: address already in use :::3000","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:635:24)\n    at WebServer.start (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:177:18)\n    at initializeBot (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:123:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[16/06/2025 23:13:02] INFO: ▣ Sistema de logs inicializado
[16/06/2025 23:13:02] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 23:13:02] INFO: ▣ Banco de dados inicializado
[16/06/2025 23:13:02] INFO: ▣ 28 comandos carregados
[16/06/2025 23:13:02] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 23:13:02] INFO: ▣ Eventos carregados
[16/06/2025 23:13:02] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 23:13:02] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 23:13:02] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 23:13:02] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 23:13:02] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 23:13:02] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 23:13:02] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 23:13:02] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 23:13:02] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 23:13:02] INFO: ▣ Servidor web inicializado
[16/06/2025 23:13:02] ERROR: Uncaught Exception: | Meta: {"error":"listen EADDRINUSE: address already in use :::3000","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:635:24)\n    at WebServer.start (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:177:18)\n    at initializeBot (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:122:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[16/06/2025 23:14:36] INFO: ▣ Sistema de logs inicializado
[16/06/2025 23:14:36] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 23:14:36] INFO: ▣ Banco de dados inicializado
[16/06/2025 23:14:37] INFO: ▣ 28 comandos carregados
[16/06/2025 23:14:37] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 23:14:37] INFO: ▣ Eventos carregados
[16/06/2025 23:14:37] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 23:14:37] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 23:14:37] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 23:14:37] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 23:14:37] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 23:14:37] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 23:14:37] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 23:14:37] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 23:14:37] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 23:14:37] INFO: ▣ Servidor web inicializado
[16/06/2025 23:14:37] ERROR: Uncaught Exception: | Meta: {"error":"listen EADDRINUSE: address already in use :::3000","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:635:24)\n    at WebServer.start (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:177:18)\n    at initializeBot (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:122:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[16/06/2025 23:23:53] INFO: ▣ Sistema de logs inicializado
[16/06/2025 23:23:53] INFO: ▣ Gerenciador de configurações inicializado
[16/06/2025 23:23:53] INFO: ▣ Banco de dados inicializado
[16/06/2025 23:23:53] INFO: ▣ 28 comandos carregados
[16/06/2025 23:23:53] INFO: ▣ Gerenciador de estados de comandos inicializado
[16/06/2025 23:23:53] INFO: ▣ Eventos carregados
[16/06/2025 23:23:53] INFO: 🛡️ Sistema Anti-Raid inicializado
[16/06/2025 23:23:53] INFO: 🤖 Sistema de Auto-Moderação inicializado
[16/06/2025 23:23:53] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[16/06/2025 23:23:53] INFO: ▣ Sistemas de moderação inicializados
[16/06/2025 23:23:53] INFO: ▣ Sistema de Analytics de Moderação inicializado
[16/06/2025 23:23:53] INFO: ▣ Sistema de Backup inicializado
[16/06/2025 23:23:53] INFO: ▣ Inicializando Sistema de Verificação...
[16/06/2025 23:23:53] INFO: ▣ Sistema de Verificação inicializado com sucesso
[16/06/2025 23:23:53] INFO: ▣ Sistema de Verificação inicializado
[16/06/2025 23:23:53] INFO: ▣ Servidor web inicializado
[16/06/2025 23:23:55] INFO: 🔄 Registrando comandos slash...
[16/06/2025 23:23:56] INFO: ✅ 28 comandos slash registrados globalmente
[16/06/2025 23:23:56] INFO: 🔍 Verificando configurações dos servidores...
[16/06/2025 23:23:56] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[16/06/2025 23:23:56] INFO: 📋 Cache de configurações carregado para 3 servidores
[16/06/2025 23:23:56] INFO: ✅ Tarefas periódicas inicializadas
[16/06/2025 23:23:56] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":5.5876384}
