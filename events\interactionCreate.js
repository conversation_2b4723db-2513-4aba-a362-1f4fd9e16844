/**
 * ========================================
 * EVENTO: INTERACTION CREATE
 * Manipula todas as interações (comandos, botões, etc.)
 * ========================================
 */

const { Events, EmbedBuilder, Collection } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        // Comandos Slash
        if (interaction.isChatInputCommand()) {
            await handleSlashCommand(interaction, client);
        }
        
        // Botões
        else if (interaction.isButton()) {
            await handleButton(interaction, client);
        }
        
        // Menus de Seleção
        else if (interaction.isStringSelectMenu()) {
            await handleSelectMenu(interaction, client);
        }
        
        // Modais
        else if (interaction.isModalSubmit()) {
            await handleModal(interaction, client);
        }
        
        // Context Menus (comandos de contexto)
        else if (interaction.isContextMenuCommand()) {
            await handleContextMenu(interaction, client);
        }
    }
};

/**
 * Manipula comandos slash
 */
async function handleSlashCommand(interaction, client) {
    const command = client.commands.get(interaction.commandName);

    if (!command) {
        client.logger.warn(`Comando não encontrado: ${interaction.commandName}`, {
            user: interaction.user.id,
            guild: interaction.guild?.id
        });
        
        return await interaction.reply({
            content: '❌ Comando não encontrado!',
            ephemeral: true
        });
    }

    try {
        // Verificar cooldown
        const cooldownResult = checkCooldown(interaction, client, command);
        if (cooldownResult.onCooldown) {
            return await interaction.reply({
                content: `⏰ Aguarde ${cooldownResult.timeLeft} antes de usar este comando novamente.`,
                ephemeral: true
            });
        }

        // Verificar se o comando pode ser usado em DM
        if (!interaction.guild && command.data.dm_permission === false) {
            return await interaction.reply({
                content: '❌ Este comando não pode ser usado em mensagens diretas!',
                ephemeral: true
            });
        }

        // Verificar se o comando está habilitado no servidor
        if (interaction.guild && client.commandStateManager) {
            const isEnabled = client.commandStateManager.isCommandEnabled(
                interaction.guild.id,
                interaction.commandName
            );

            if (!isEnabled) {
                const EmbedStyles = require('../utils/EmbedStyles');
                const embedStyles = new EmbedStyles();

                const disabledEmbed = embedStyles.createWarningEmbed(
                    'Comando Desabilitado',
                    `${embedStyles.format.bold('Este comando foi desabilitado pelos administradores.')}\n\n${embedStyles.icons.info} **Comando:** \`/${interaction.commandName}\`\n${embedStyles.icons.settings} **Status:** Desabilitado\n\n${embedStyles.format.italic('Entre em contato com os administradores se precisar usar este comando.')}`
                );

                return await interaction.reply({
                    embeds: [disabledEmbed],
                    ephemeral: true
                });
            }
        }

        // Verificar permissões do usuário
        if (interaction.guild && command.data.default_member_permissions) {
            const userPermissions = interaction.member.permissions;
            const requiredPermissions = command.data.default_member_permissions;
            
            if (!userPermissions.has(requiredPermissions)) {
                return await interaction.reply({
                    content: '❌ Você não tem permissão para usar este comando!',
                    ephemeral: true
                });
            }
        }

        // Verificar permissões do bot
        if (interaction.guild && command.botPermissions) {
            const botPermissions = interaction.guild.members.me.permissions;
            const missingPermissions = command.botPermissions.filter(perm => !botPermissions.has(perm));
            
            if (missingPermissions.length > 0) {
                return await interaction.reply({
                    content: `❌ Eu não tenho as seguintes permissões necessárias: ${missingPermissions.join(', ')}`,
                    ephemeral: true
                });
            }
        }

        // Executar comando
        const startTime = Date.now();
        await command.execute(interaction);
        const executionTime = Date.now() - startTime;

        // Log do comando
        client.logger.command(
            interaction.commandName,
            interaction.user.id,
            interaction.guild?.id,
            true
        );

        // Log de performance se demorou muito
        if (executionTime > 3000) {
            client.logger.performance(
                `Comando ${interaction.commandName}`,
                executionTime,
                {
                    user: interaction.user.id,
                    guild: interaction.guild?.id
                }
            );
        }

        // Atualizar cooldown
        setCooldown(interaction, client, command);

    } catch (error) {
        client.logger.error(`Erro no comando ${interaction.commandName}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id,
            options: interaction.options.data
        });

        const errorEmbed = new EmbedBuilder()
            .setColor('#e74c3c')
            .setTitle('❌ Erro no Comando')
            .setDescription('Ocorreu um erro ao executar este comando. O erro foi registrado e será investigado.')
            .addFields({
                name: '🆔 ID do Erro',
                value: `\`${Date.now()}\``,
                inline: true
            })
            .setTimestamp()
            .setFooter({ text: 'Nova Moderação Bot' });

        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        } catch (followUpError) {
            client.logger.error('Erro ao enviar mensagem de erro:', followUpError);
        }

        // Log do comando com erro
        client.logger.command(
            interaction.commandName,
            interaction.user.id,
            interaction.guild?.id,
            false,
            error.message
        );
    }
}

/**
 * Manipula cliques em botões
 */
async function handleButton(interaction, client) {
    const customId = interaction.customId;

    try {
        // Botão de voltar na ajuda
        if (customId === 'help_back') {
            // Recriar o embed principal de ajuda
            const { EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

            const helpEmbed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('📚 Nodex | Moderação - Ajuda')
                .setDescription(`Olá! Eu sou o **Nodex | Moderação**, seu assistente de moderação inteligente!\n\n🇧🇷 **100% em Português Brasileiro**\n🤖 **IA Integrada para moderação inteligente**\n🛡️ **Sistema anti-raid avançado**`)
                .addFields(
                    {
                        name: '🔧 Como Usar',
                        value: `• **Comandos Slash:** Digite \`/\` e escolha um comando\n• **Configuração:** Use \`/config\` para configurar o bot`,
                        inline: false
                    },
                    {
                        name: '📋 Categorias de Comandos',
                        value: '🔨 **Moderação** - Comandos para moderar o servidor\n⚙️ **Configuração** - Configurações do bot\n📊 **Informações** - Estatísticas e informações\n🎫 **Utilidades** - Comandos úteis diversos',
                        inline: false
                    },
                    {
                        name: '🚀 Recursos Principais',
                        value: '• Auto-moderação inteligente\n• Sistema anti-raid\n• Logs detalhados\n• Dashboard web\n• IA integrada',
                        inline: false
                    }
                )
                .setThumbnail(client.user.displayAvatarURL())
                .setTimestamp()
                .setFooter({
                    text: 'Nodex | Moderação • Use o menu abaixo para navegar',
                    iconURL: client.user.displayAvatarURL()
                });

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('help_category_select')
                .setPlaceholder('📂 Selecione uma categoria para ver os comandos')
                .addOptions([
                    {
                        label: 'Moderação',
                        description: 'Comandos para moderar o servidor',
                        value: 'moderation',
                        emoji: '🔨'
                    },
                    {
                        label: 'Configuração',
                        description: 'Configurações do bot',
                        value: 'config',
                        emoji: '⚙️'
                    },
                    {
                        label: 'Informações',
                        description: 'Estatísticas e informações',
                        value: 'info',
                        emoji: '📊'
                    },
                    {
                        label: 'Utilidades',
                        description: 'Comandos úteis diversos',
                        value: 'utility',
                        emoji: '🎫'
                    }
                ]);

            const buttons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('Dashboard Web')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`http://localhost:${process.env.WEB_PORT || 3000}/dashboard`)
                        .setEmoji('🌐'),
                    new ButtonBuilder()
                        .setLabel('Suporte')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`http://localhost:${process.env.WEB_PORT || 3000}/support`)
                        .setEmoji('🆘'),
                    new ButtonBuilder()
                        .setLabel('Documentação')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`http://localhost:${process.env.WEB_PORT || 3000}/docs`)
                        .setEmoji('📖')
                );

            const row1 = new ActionRowBuilder().addComponents(selectMenu);
            const row2 = buttons;

            await interaction.update({
                embeds: [helpEmbed],
                components: [row1, row2]
            });
        }

        // Botões de configuração
        else if (customId.startsWith('config_')) {
            await handleConfigButton(interaction, client);
        }

        // Botões de moderação
        else if (customId.startsWith('mod_')) {
            await handleModerationButton(interaction, client);
        }





        // Botões de verificação (IMPLEMENTADO - SEGUINDO PADRÃO DOS MÓDULOS FUNCIONAIS)
        else if (customId.startsWith('verify_')) {
            await handleVerificationButton(interaction, client);
        }

        // Botão não reconhecido
        else {
            await interaction.reply({
                content: '❌ Botão não reconhecido!',
                ephemeral: true
            });
        }

    } catch (error) {
        client.logger.error(`Erro no botão ${customId}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id
        });

        await interaction.reply({
            content: '❌ Ocorreu um erro ao processar esta ação.',
            ephemeral: true
        }).catch(() => {});
    }
}

/**
 * Manipula botões de configuração
 */
async function handleConfigButton(interaction, client) {
    const action = interaction.customId.replace('config_', '');
    const guild = interaction.guild;

    // Verificar permissões
    if (!interaction.member.permissions.has('Administrator')) {
        return await interaction.reply({
            content: '❌ Apenas administradores podem alterar configurações!',
            ephemeral: true
        });
    }

    let guildConfig = client.database.getGuildConfig(guild.id) || {};

    switch (action) {
        case 'automod_toggle':
            guildConfig.auto_mod_enabled = !guildConfig.auto_mod_enabled;
            client.database.saveGuildConfig(guild.id, guildConfig);
            
            await interaction.reply({
                content: `🤖 Auto-moderação ${guildConfig.auto_mod_enabled ? 'ativada' : 'desativada'}!`,
                ephemeral: true
            });
            break;

        case 'antiraid_toggle':
            guildConfig.anti_raid_enabled = !guildConfig.anti_raid_enabled;
            client.database.saveGuildConfig(guild.id, guildConfig);
            
            await interaction.reply({
                content: `🛡️ Anti-raid ${guildConfig.anti_raid_enabled ? 'ativado' : 'desativado'}!`,
                ephemeral: true
            });
            break;

        case 'advanced':
            // Mostrar menu de configurações avançadas
            await showAdvancedConfigMenu(interaction, client);
            break;

        default:
            await interaction.reply({
                content: '❌ Ação de configuração não reconhecida!',
                ephemeral: true
            });
    }
}

/**
 * Manipula menus de seleção
 */
async function handleSelectMenu(interaction, client) {
    const customId = interaction.customId;
    const values = interaction.values;

    try {
        // Menu de ajuda
        if (customId === 'help_category_select') {
            const category = values[0];
            const { handleCategorySelection } = require('../commands/geral/help.js');
            await handleCategorySelection(interaction, category);
        }
        else if (customId === 'config_advanced_menu') {
            await handleAdvancedConfigSelection(interaction, client, values);
        }
        else if (customId === 'mod_action_menu') {
            await handleModerationActionSelection(interaction, client, values);
        }
        else {
            await interaction.reply({
                content: '❌ Menu não reconhecido!',
                ephemeral: true
            });
        }

    } catch (error) {
        client.logger.error(`Erro no menu ${customId}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id,
            values: values
        });

        await interaction.reply({
            content: '❌ Ocorreu um erro ao processar sua seleção.',
            ephemeral: true
        }).catch(() => {});
    }
}

/**
 * Manipula modais
 */
async function handleModal(interaction, client) {
    const customId = interaction.customId;

    try {
        if (customId === 'reason_modal') {
            await handleReasonModal(interaction, client);
        }
        else if (customId === 'config_modal') {
            await handleConfigModal(interaction, client);
        }
        else {
            await interaction.reply({
                content: '❌ Modal não reconhecido!',
                ephemeral: true
            });
        }

    } catch (error) {
        client.logger.error(`Erro no modal ${customId}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id
        });

        await interaction.reply({
            content: '❌ Ocorreu um erro ao processar o formulário.',
            ephemeral: true
        }).catch(() => {});
    }
}

/**
 * Manipula botões de verificação (NOVO - SEGUINDO PADRÃO DOS MÓDULOS FUNCIONAIS)
 */
async function handleVerificationButton(interaction, client) {
    const customId = interaction.customId;

    try {
        if (customId === 'verify_user') {
            console.log(`🛡️ [VERIFICATION] Usuário ${interaction.user.tag} clicou no botão de verificação`);

            // Verificar se o sistema de verificação está disponível
            if (!client.verificationSystem) {
                return await interaction.reply({
                    content: '❌ Sistema de verificação não disponível!',
                    ephemeral: true
                });
            }

            // Buscar configuração de verificação
            const config = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ? AND enabled = 1
            `).get(interaction.guild.id);

            if (!config) {
                return await interaction.reply({
                    content: '❌ Sistema de verificação não configurado neste servidor!',
                    ephemeral: true
                });
            }

            // Verificar se o usuário já está verificado
            const existingVerification = client.database.db.prepare(`
                SELECT verified FROM member_verification
                WHERE guild_id = ? AND user_id = ?
            `).get(interaction.guild.id, interaction.user.id);

            if (existingVerification?.verified) {
                return await interaction.reply({
                    content: '✅ Você já está verificado neste servidor!',
                    ephemeral: true
                });
            }

            // Processar verificação
            const member = interaction.member;
            const guild = interaction.guild;

            // Obter cargo de verificado
            const verifiedRole = guild.roles.cache.get(config.verified_role_id);
            if (!verifiedRole) {
                return await interaction.reply({
                    content: '❌ Cargo de verificado não encontrado! Contate um administrador.',
                    ephemeral: true
                });
            }

            // Adicionar cargo de verificado
            await member.roles.add(verifiedRole, 'Verificação automática via botão');

            // Remover cargo de não verificado se existir
            if (config.unverified_role_id) {
                const unverifiedRole = guild.roles.cache.get(config.unverified_role_id);
                if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
                    await member.roles.remove(unverifiedRole, 'Usuário verificado');
                }
            }

            // Registrar verificação no banco
            client.database.db.prepare(`
                INSERT OR REPLACE INTO member_verification
                (guild_id, user_id, verified, verification_method, verified_at)
                VALUES (?, ?, 1, 'button', CURRENT_TIMESTAMP)
            `).run(guild.id, member.id);

            // Log da verificação
            client.database.db.prepare(`
                INSERT INTO verification_logs
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'completed', 'button', ?, CURRENT_TIMESTAMP)
            `).run(guild.id, member.id, JSON.stringify({
                success: true,
                roleAdded: verifiedRole.name
            }));

            console.log(`✅ [VERIFICATION] ${member.user.tag} verificado com sucesso no servidor ${guild.name}`);

            // Responder ao usuário
            const { EmbedBuilder } = require('discord.js');
            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Verificação Concluída!')
                .setDescription(`Parabéns! Você foi verificado com sucesso no servidor **${guild.name}**.`)
                .addFields(
                    { name: '🎯 Cargo Obtido', value: `@${verifiedRole.name}`, inline: true },
                    { name: '🕐 Verificado em', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                )
                .setColor('#00ff7f')
                .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                .setTimestamp();

            await interaction.reply({
                embeds: [successEmbed],
                ephemeral: true
            });

            // Enviar mensagem de boas-vindas se configurada
            if (config.welcome_message) {
                try {
                    await member.send({
                        embeds: [new EmbedBuilder()
                            .setTitle(`🎉 Bem-vindo(a) ao ${guild.name}!`)
                            .setDescription(config.welcome_message)
                            .setColor('#00ff7f')
                            .setFooter({ text: 'Nodex | Moderação' })
                        ]
                    });
                } catch (error) {
                    console.log(`⚠️ [VERIFICATION] Não foi possível enviar DM de boas-vindas para ${member.user.tag}`);
                }
            }

        } else {
            await interaction.reply({
                content: '❌ Botão de verificação não reconhecido!',
                ephemeral: true
            });
        }

    } catch (error) {
        client.logger.error(`Erro no botão de verificação ${customId}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id
        });

        await interaction.reply({
            content: '❌ Ocorreu um erro durante a verificação. Tente novamente ou contate um administrador.',
            ephemeral: true
        }).catch(() => {});
    }
}

/**
 * Manipula comandos de contexto
 */
async function handleContextMenu(interaction, client) {
    const command = client.commands.get(interaction.commandName);

    if (!command) {
        return await interaction.reply({
            content: '❌ Comando de contexto não encontrado!',
            ephemeral: true
        });
    }

    try {
        await command.execute(interaction);
        
        client.logger.command(
            interaction.commandName,
            interaction.user.id,
            interaction.guild?.id,
            true
        );

    } catch (error) {
        client.logger.error(`Erro no comando de contexto ${interaction.commandName}:`, error);

        await interaction.reply({
            content: '❌ Ocorreu um erro ao executar esta ação.',
            ephemeral: true
        }).catch(() => {});
    }
}

/**
 * Verifica cooldown de comandos
 */
function checkCooldown(interaction, client, command) {
    if (!command.cooldown) return { onCooldown: false };

    const cooldowns = client.cooldowns;
    const commandName = command.data.name;

    if (!cooldowns.has(commandName)) {
        cooldowns.set(commandName, new Collection());
    }

    const now = Date.now();
    const timestamps = cooldowns.get(commandName);
    const cooldownAmount = command.cooldown * 1000;

    if (timestamps.has(interaction.user.id)) {
        const expirationTime = timestamps.get(interaction.user.id) + cooldownAmount;

        if (now < expirationTime) {
            const timeLeft = Math.ceil((expirationTime - now) / 1000);
            return {
                onCooldown: true,
                timeLeft: `${timeLeft} segundo(s)`
            };
        }
    }

    return { onCooldown: false };
}

/**
 * Define cooldown para um comando
 */
function setCooldown(interaction, client, command) {
    if (!command.cooldown) return;

    const cooldowns = client.cooldowns;
    const commandName = command.data.name;
    const timestamps = cooldowns.get(commandName);
    
    timestamps.set(interaction.user.id, Date.now());
    
    // Remover cooldown após expirar
    setTimeout(() => {
        timestamps.delete(interaction.user.id);
    }, command.cooldown * 1000);
}

/**
 * Funções auxiliares para botões específicos
 */
async function handleModerationButton(interaction, client) {
    // Implementar lógica de botões de moderação
    await interaction.reply({
        content: '🔧 Funcionalidade de moderação em desenvolvimento!',
        ephemeral: true
    });
}



async function handleVerificationButton(interaction, client) {
    const customId = interaction.customId;

    try {
        console.log(`🔘 [BUTTON] Processando botão: "${customId}" para ${interaction.user.tag}`);

        if (customId === 'verify_user') {
            console.log(`🛡️ [VERIFICATION] Iniciando verificação para ${interaction.user.tag}`);

            // Defer reply immediately to prevent timeout
            await interaction.deferReply({ ephemeral: true });

            // Verificar se o sistema de verificação está disponível
            if (!client.verificationSystem) {
                return await interaction.editReply({
                    content: '❌ Sistema de verificação não disponível!'
                });
            }

            // Buscar configuração de verificação
            const config = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ? AND enabled = 1
            `).get(interaction.guild.id);

            console.log(`🔍 [VERIFICATION] Config encontrada:`, {
                enabled: config?.enabled,
                method: config?.method,
                channel_id: config?.channel_id,
                verified_role_id: config?.verified_role_id
            });

            if (!config) {
                return await interaction.editReply({
                    content: '❌ Sistema de verificação não configurado neste servidor!'
                });
            }

            // Verificar se o usuário pode tentar verificação (versão simplificada)
            console.log(`🔍 [VERIFICATION] Verificando se usuário pode tentar verificação...`);

            try {
                // Verificação simplificada - apenas verificar se já está verificado
                const existingVerification = client.database.db.prepare(`
                    SELECT verified FROM member_verification
                    WHERE guild_id = ? AND user_id = ?
                `).get(interaction.guild.id, interaction.user.id);

                console.log(`🔍 [VERIFICATION] Verificação existente:`, existingVerification);

                if (existingVerification?.verified) {
                    console.log(`✅ [VERIFICATION] Usuário já está verificado`);
                    return await interaction.editReply({
                        content: '✅ Você já está verificado neste servidor!'
                    });
                }

                console.log(`🔍 [VERIFICATION] Usuário pode tentar verificação`);
            } catch (checkError) {
                console.error(`❌ [VERIFICATION] Erro na verificação de tentativas:`, checkError);
                // Continuar mesmo com erro - permitir tentativa
            }

            // Processar verificação baseado no método configurado
            const member = interaction.member;
            const guild = interaction.guild;
            const method = config.method || 'reaction';

            console.log(`🛡️ [VERIFICATION] Método configurado: "${method}"`);
            console.log(`🛡️ [VERIFICATION] Member: ${member?.user?.tag}`);
            console.log(`🛡️ [VERIFICATION] Guild: ${guild?.name}`);

            // Validar cargo de verificado
            console.log(`🛡️ [VERIFICATION] Validando cargo de verificado: ${config.verified_role_id}`);
            if (!config.verified_role_id) {
                console.log(`❌ [VERIFICATION] Cargo de verificado não configurado`);
                return await interaction.editReply({
                    content: '❌ Cargo de verificado não configurado! Contate um administrador.'
                });
            }

            const verifiedRole = guild.roles.cache.get(config.verified_role_id);
            console.log(`🛡️ [VERIFICATION] Cargo encontrado: ${verifiedRole ? verifiedRole.name : 'NÃO ENCONTRADO'}`);
            if (!verifiedRole) {
                console.log(`❌ [VERIFICATION] Cargo de verificado não encontrado no servidor`);
                return await interaction.editReply({
                    content: `❌ Cargo de verificado não encontrado! Contate um administrador.`
                });
            }

            console.log(`🛡️ [VERIFICATION] Iniciando switch case para método: "${method}"`);

            // Processar baseado no método EXATO do banco de dados
            switch (method) {
                case 'reaction':
                    console.log(`✅ [VERIFICATION] Processando verificação simples`);
                    await client.verificationSystem.completeVerification(member, config, 'reaction', 'button_click');

                    const { EmbedBuilder } = require('discord.js');
                    const successEmbed = new EmbedBuilder()
                        .setTitle('✅ Verificação Concluída!')
                        .setDescription(`Parabéns! Você foi verificado com sucesso.`)
                        .addFields(
                            { name: '🎯 Cargo Obtido', value: `@${verifiedRole.name}`, inline: true },
                            { name: '🕐 Verificado em', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                        )
                        .setColor('#00ff7f')
                        .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                        .setTimestamp();

                    await interaction.editReply({ embeds: [successEmbed] });
                    break;

                case 'captcha_math':
                    console.log(`🧮 [VERIFICATION] Processando captcha matemático`);
                    await handleCaptchaVerification(interaction, client, config, 'captcha_math');
                    break;

                case 'captcha_text':
                    console.log(`📝 [VERIFICATION] Processando captcha de texto`);
                    await handleCaptchaVerification(interaction, client, config, 'captcha_text');
                    break;

                case 'captcha_emoji':
                    console.log(`😀 [VERIFICATION] Processando captcha de emoji`);
                    try {
                        await handleCaptchaVerification(interaction, client, config, 'captcha_emoji');
                        console.log(`✅ [VERIFICATION] Captcha de emoji processado com sucesso`);
                    } catch (emojiError) {
                        console.error(`❌ [VERIFICATION] Erro específico no captcha de emoji:`, emojiError);
                        await interaction.editReply({
                            content: '❌ Erro ao processar captcha de emoji. Tente novamente.'
                        });
                    }
                    break;

                case 'manual':
                    console.log(`👤 [VERIFICATION] Processando verificação manual`);
                    await handleManualVerification(interaction, client, config);
                    break;

                case 'combined':
                    console.log(`🔗 [VERIFICATION] Processando verificação combinada`);
                    await handleCombinedVerification(interaction, client, config);
                    break;

                default:
                    console.warn(`⚠️ [VERIFICATION] Método desconhecido: "${method}". Usando fallback.`);
                    await client.verificationSystem.completeVerification(member, config, 'reaction', 'fallback');
                    await interaction.editReply({
                        content: '✅ Verificação concluída! (Método padrão aplicado)'
                    });
                    break;
            }

        } else if (customId.includes('captcha_emoji')) {
            // Processar resposta de captcha emoji (qualquer variação)
            console.log(`🎯 [EMOJI CAPTCHA] Processando resposta: ${customId}`);
            await handleEmojiCaptchaResponse(interaction, client, customId);

        } else if (customId.includes('combined') && customId.includes('accept')) {
            // Processar aceitação de regras na verificação combinada
            console.log(`🔗 [COMBINED] Processando aceitação de regras`);
            await handleCombinedRulesAcceptance(interaction, client, true);

        } else if (customId.includes('combined') && customId.includes('reject')) {
            // Processar rejeição de regras na verificação combinada
            console.log(`🔗 [COMBINED] Processando rejeição de regras`);
            await handleCombinedRulesAcceptance(interaction, client, false);

        } else if (customId.includes('combined') && customId.includes('captcha')) {
            // Processar captcha da verificação combinada
            console.log(`🔗 [COMBINED] Processando captcha combinado`);
            await handleCombinedCaptchaResponse(interaction, client, customId);

        } else if (customId.includes('combined') && customId.includes('confirm')) {
            // Processar confirmação final da verificação combinada
            console.log(`🔗 [COMBINED] Processando confirmação final`);
            await handleCombinedFinalConfirmation(interaction, client);

        } else {
            // Sistema de fallback simplificado
            console.log(`❌ [BUTTON] Botão não reconhecido: "${customId}"`);

            // Tentar fallback baseado em palavras-chave
            if (customId.includes('verify') || customId.includes('captcha') || customId.includes('combined')) {
                console.log(`🔄 [FALLBACK] Tentando verificação de emergência`);
                try {
                    const config = client.database.db.prepare(`
                        SELECT * FROM verification_config WHERE guild_id = ? AND enabled = 1
                    `).get(interaction.guild.id);

                    if (config) {
                        await client.verificationSystem.completeVerification(
                            interaction.member,
                            config,
                            'emergency',
                            'button_fallback'
                        );

                        await interaction.editReply({
                            content: '✅ Verificação processada com sucesso!\n\n⚠️ **Aviso:** Foi usado um método de emergência. Se você continuar tendo problemas, use `/verificar-emergencia`.'
                        });
                        return;
                    }
                } catch (error) {
                    console.error(`❌ [FALLBACK] Erro no fallback:`, error);
                }
            }

            // Se chegou aqui, o botão realmente não foi reconhecido
            await interaction.editReply({
                content: `❌ Botão não reconhecido!\n\n🚨 **Solução:** Use o comando \`/verificar-emergencia\` para se verificar.\n\n\`\`\`Botão: ${customId}\nUsuário: ${interaction.user.tag}\`\`\``
            });
        }

    } catch (error) {
        client.logger.error(`Erro no botão de verificação ${customId}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id
        });

        const errorResponse = {
            content: '❌ Ocorreu um erro durante a verificação. Tente novamente ou contate um administrador.'
        };

        if (interaction.deferred) {
            await interaction.editReply(errorResponse).catch(() => {});
        } else {
            await interaction.reply({ ...errorResponse, ephemeral: true }).catch(() => {});
        }
    }
}



async function showAdvancedConfigMenu(interaction, client) {
    // Implementar menu de configurações avançadas
    await interaction.reply({
        content: '⚙️ Menu de configurações avançadas em desenvolvimento!',
        ephemeral: true
    });
}

async function handleAdvancedConfigSelection(interaction, client, values) {
    // Implementar seleção de configurações avançadas
    await interaction.reply({
        content: `⚙️ Configuração selecionada: ${values.join(', ')}`,
        ephemeral: true
    });
}

async function handleModerationActionSelection(interaction, client, values) {
    // Implementar seleção de ações de moderação
    await interaction.reply({
        content: `🔨 Ação de moderação selecionada: ${values.join(', ')}`,
        ephemeral: true
    });
}

async function handleReasonModal(interaction, client) {
    // Implementar modal de motivo
    await interaction.reply({
        content: '📝 Modal de motivo processado!',
        ephemeral: true
    });
}

async function handleConfigModal(interaction, client) {
    // Implementar modal de configuração
    await interaction.reply({
        content: '⚙️ Modal de configuração processado!',
        ephemeral: true
    });
}

/**
 * Lidar com verificação por captcha
 */
async function handleCaptchaVerification(interaction, client, config, method) {
    const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

    try {
        let captchaData;
        let embed;

        if (method === 'captcha_math') {
            // Gerar captcha matemático
            captchaData = client.verificationSystem.generateMathCaptcha();

            embed = new EmbedBuilder()
                .setTitle('🧮 Verificação - Captcha Matemático')
                .setDescription(`Para se verificar, resolva a seguinte operação matemática:`)
                .addFields({
                    name: '📊 Operação',
                    value: `\`\`\`${captchaData.question}\`\`\``,
                    inline: false
                })
                .setColor('#ffa500')
                .setFooter({ text: 'Digite apenas o número da resposta' })
                .setTimestamp();

        } else if (method === 'captcha_text') {
            // Gerar captcha de texto
            const words = ['NODEX', 'VERIFICAR', 'SEGURO', 'DISCORD', 'SERVIDOR'];
            const word = words[Math.floor(Math.random() * words.length)];
            captchaData = { word, answer: word };

            embed = new EmbedBuilder()
                .setTitle('📝 Verificação - Captcha de Texto')
                .setDescription(`Para se verificar, digite exatamente a palavra mostrada abaixo:`)
                .addFields({
                    name: '🔤 Palavra',
                    value: `\`\`\`${word}\`\`\``,
                    inline: false
                })
                .setColor('#ffa500')
                .setFooter({ text: 'Digite a palavra exatamente como mostrada' })
                .setTimestamp();

        } else if (method === 'captcha_emoji') {
            // Gerar captcha de emoji
            const emojis = ['🎯', '🔥', '⭐', '🎮', '🚀', '💎', '🎪', '🎨'];
            const targetEmoji = emojis[Math.floor(Math.random() * emojis.length)];
            const shuffledEmojis = [...emojis].sort(() => Math.random() - 0.5).slice(0, 4);
            if (!shuffledEmojis.includes(targetEmoji)) {
                shuffledEmojis[0] = targetEmoji;
            }

            captchaData = { targetEmoji, options: shuffledEmojis };

            embed = new EmbedBuilder()
                .setTitle('😀 Verificação - Captcha de Emoji')
                .setDescription(`Para se verificar, clique no emoji correto:`)
                .addFields({
                    name: '🎯 Emoji Alvo',
                    value: `Clique no emoji: **${targetEmoji}**`,
                    inline: false
                })
                .setColor('#ffa500')
                .setFooter({ text: 'Clique no botão com o emoji correto' })
                .setTimestamp();
        }

        // Salvar dados do captcha temporariamente
        if (!client.pendingVerifications) {
            client.pendingVerifications = new Map();
        }

        client.pendingVerifications.set(interaction.user.id, {
            guildId: interaction.guild.id,
            captchaData,
            method,
            config,
            timestamp: Date.now()
        });

        // Criar botões para emoji captcha
        if (method === 'captcha_emoji') {
            console.log(`🎯 [EMOJI CAPTCHA] Criando botões para emojis:`, captchaData.options);

            const row = new ActionRowBuilder();
            captchaData.options.forEach((emoji, index) => {
                const customId = `captcha_emoji_${emoji}`;
                console.log(`🎯 [EMOJI CAPTCHA] Criando botão ${index + 1}: customId="${customId}", emoji="${emoji}"`);

                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId(customId)
                        .setEmoji(emoji)
                        .setStyle(ButtonStyle.Secondary)
                );
            });

            console.log(`🎯 [EMOJI CAPTCHA] Enviando resposta com ${captchaData.options.length} botões`);

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

            console.log(`✅ [EMOJI CAPTCHA] Captcha de emoji enviado com sucesso`);
        } else {
            await interaction.editReply({
                embeds: [embed]
            });
        }

        // Configurar timeout para captcha
        setTimeout(() => {
            if (client.pendingVerifications?.has(interaction.user.id)) {
                client.pendingVerifications.delete(interaction.user.id);
                console.log(`⏰ [VERIFICATION] Captcha expirado para ${interaction.user.tag}`);
            }
        }, (config.timeout_minutes || 30) * 60 * 1000);

    } catch (error) {
        console.error('❌ [VERIFICATION] Erro no captcha:', error);
        await interaction.editReply({
            content: '❌ Erro ao gerar captcha. Tente novamente.'
        });
    }
}

/**
 * Lidar com verificação manual
 */
async function handleManualVerification(interaction, client, config) {
    const { EmbedBuilder } = require('discord.js');

    try {
        // Registrar solicitação de verificação manual
        client.database.db.prepare(`
            INSERT OR REPLACE INTO member_verification
            (guild_id, user_id, verified, verification_method, last_attempt)
            VALUES (?, ?, 0, 'manual_pending', CURRENT_TIMESTAMP)
        `).run(interaction.guild.id, interaction.user.id);

        // Log da solicitação
        client.database.db.prepare(`
            INSERT INTO verification_logs
            (guild_id, user_id, action, method, details, timestamp)
            VALUES (?, ?, 'manual_requested', 'manual', ?, CURRENT_TIMESTAMP)
        `).run(interaction.guild.id, interaction.user.id, JSON.stringify({
            requested_by: interaction.user.tag,
            pending_approval: true
        }));

        const embed = new EmbedBuilder()
            .setTitle('👤 Verificação Manual Solicitada')
            .setDescription('Sua solicitação de verificação foi enviada para os moderadores.')
            .addFields(
                { name: '⏳ Status', value: 'Aguardando aprovação manual', inline: true },
                { name: '👥 Próximo Passo', value: 'Um moderador irá revisar sua solicitação', inline: true }
            )
            .setColor('#ffa500')
            .setFooter({ text: 'Aguarde a aprovação dos moderadores' })
            .setTimestamp();

        await interaction.editReply({
            embeds: [embed]
        });

        // Notificar moderadores (se canal de logs estiver configurado)
        const guildConfig = await client.database.getGuildConfig(interaction.guild.id);
        if (guildConfig?.log_channel_id) {
            const logChannel = interaction.guild.channels.cache.get(guildConfig.log_channel_id);
            if (logChannel) {
                const modEmbed = new EmbedBuilder()
                    .setTitle('🔔 Nova Solicitação de Verificação Manual')
                    .setDescription(`${interaction.user} solicitou verificação manual.`)
                    .addFields(
                        { name: '👤 Usuário', value: `${interaction.user.tag} (${interaction.user.id})`, inline: true },
                        { name: '📅 Conta Criada', value: `<t:${Math.floor(interaction.user.createdTimestamp / 1000)}:R>`, inline: true }
                    )
                    .setColor('#ff9500')
                    .setTimestamp();

                await logChannel.send({ embeds: [modEmbed] });
            }
        }

    } catch (error) {
        console.error('❌ [VERIFICATION] Erro na verificação manual:', error);
        await interaction.editReply({
            content: '❌ Erro ao processar verificação manual. Tente novamente.'
        });
    }
}

/**
 * Lidar com verificação combinada
 */
async function handleCombinedVerification(interaction, client, config) {
    const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

    try {
        console.log(`🔗 [COMBINED VERIFICATION] Iniciando para ${interaction.user.tag}`);

        // Etapa 1: Aceitar regras
        const rulesEmbed = new EmbedBuilder()
            .setTitle('🔗 Verificação Combinada - Etapa 1/3')
            .setDescription('**Primeiro, você deve aceitar as regras do servidor:**')
            .addFields(
                {
                    name: '📋 Regras do Servidor',
                    value: config.rules_text || 'Leia e aceite as regras do servidor para continuar.',
                    inline: false
                },
                {
                    name: '⏳ Próximos Passos',
                    value: '1️⃣ **Aceitar regras** ← Você está aqui\n2️⃣ Resolver captcha\n3️⃣ Confirmação final',
                    inline: false
                }
            )
            .setColor('#ffa500')
            .setFooter({ text: 'Etapa 1 de 3 - Aceite as regras para continuar' })
            .setTimestamp();

        const rulesRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`combined_accept_rules_${interaction.user.id}`)
                    .setLabel('✅ Aceito as Regras')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId(`combined_reject_rules_${interaction.user.id}`)
                    .setLabel('❌ Não Aceito')
                    .setStyle(ButtonStyle.Danger)
            );

        await interaction.editReply({
            embeds: [rulesEmbed],
            components: [rulesRow]
        });

        // Salvar estado da verificação combinada
        if (!client.pendingVerifications) {
            client.pendingVerifications = new Map();
        }

        client.pendingVerifications.set(interaction.user.id, {
            guildId: interaction.guild.id,
            config,
            method: 'combined',
            step: 1,
            timestamp: Date.now(),
            rulesAccepted: false,
            captchaSolved: false
        });

        // Timeout para verificação combinada (tempo maior)
        setTimeout(() => {
            if (client.pendingVerifications?.has(interaction.user.id)) {
                const verification = client.pendingVerifications.get(interaction.user.id);
                if (verification.method === 'combined') {
                    client.pendingVerifications.delete(interaction.user.id);
                    console.log(`⏰ [COMBINED VERIFICATION] Timeout para ${interaction.user.tag}`);
                }
            }
        }, (config.timeout_minutes || 30) * 60 * 1000);

    } catch (error) {
        console.error('❌ [VERIFICATION] Erro na verificação combinada:', error);
        await interaction.editReply({
            content: '❌ Erro ao processar verificação combinada. Tente novamente.'
        });
    }
}

/**
 * Processar aceitação/rejeição de regras na verificação combinada
 */
async function handleCombinedRulesAcceptance(interaction, client, accepted) {
    const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

    try {
        await interaction.deferReply({ ephemeral: true });

        if (!accepted) {
            // Usuário rejeitou as regras
            const rejectEmbed = new EmbedBuilder()
                .setTitle('❌ Regras Rejeitadas')
                .setDescription('Você deve aceitar as regras para continuar no servidor.')
                .addFields({
                    name: '📞 Próximo Passo',
                    value: 'Entre em contato com um moderador se tiver dúvidas sobre as regras.',
                    inline: false
                })
                .setColor('#ff4757')
                .setFooter({ text: 'Verificação cancelada' })
                .setTimestamp();

            return await interaction.editReply({ embeds: [rejectEmbed] });
        }

        // Verificar se há verificação pendente
        if (!client.pendingVerifications?.has(interaction.user.id)) {
            return await interaction.editReply({
                content: '❌ Nenhuma verificação pendente encontrada!'
            });
        }

        const verification = client.pendingVerifications.get(interaction.user.id);
        verification.rulesAccepted = true;
        verification.step = 2;

        // Etapa 2: Captcha
        const captchaEmojis = ['🎯', '🔥', '⭐', '🎮', '🚀', '💎'];
        const targetEmoji = captchaEmojis[Math.floor(Math.random() * captchaEmojis.length)];
        const shuffledEmojis = [...captchaEmojis].sort(() => Math.random() - 0.5).slice(0, 4);
        if (!shuffledEmojis.includes(targetEmoji)) {
            shuffledEmojis[0] = targetEmoji;
        }

        verification.captchaData = { targetEmoji, options: shuffledEmojis };
        client.pendingVerifications.set(interaction.user.id, verification);

        const captchaEmbed = new EmbedBuilder()
            .setTitle('🔗 Verificação Combinada - Etapa 2/3')
            .setDescription('**Agora resolva o captcha de emoji:**')
            .addFields(
                {
                    name: '🎯 Emoji Alvo',
                    value: `Clique no emoji: **${targetEmoji}**`,
                    inline: false
                },
                {
                    name: '⏳ Próximos Passos',
                    value: '1️⃣ ✅ Aceitar regras\n2️⃣ **Resolver captcha** ← Você está aqui\n3️⃣ Confirmação final',
                    inline: false
                }
            )
            .setColor('#ffa500')
            .setFooter({ text: 'Etapa 2 de 3 - Clique no emoji correto' })
            .setTimestamp();

        const captchaRow = new ActionRowBuilder();
        shuffledEmojis.forEach(emoji => {
            captchaRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`combined_captcha_${emoji}`)
                    .setEmoji(emoji)
                    .setStyle(ButtonStyle.Secondary)
            );
        });

        await interaction.editReply({
            embeds: [captchaEmbed],
            components: [captchaRow]
        });

    } catch (error) {
        console.error('❌ [COMBINED VERIFICATION] Erro na aceitação de regras:', error);
        await interaction.editReply({
            content: '❌ Erro ao processar aceitação de regras. Tente novamente.'
        }).catch(() => {});
    }
}

/**
 * Processar resposta de captcha na verificação combinada
 */
async function handleCombinedCaptchaResponse(interaction, client, customId) {
    const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

    try {
        await interaction.deferReply({ ephemeral: true });

        // Verificar se há verificação pendente
        if (!client.pendingVerifications?.has(interaction.user.id)) {
            return await interaction.editReply({
                content: '❌ Nenhuma verificação pendente encontrada!'
            });
        }

        const verification = client.pendingVerifications.get(interaction.user.id);
        const clickedEmoji = customId.replace('combined_captcha_', '');
        const isCorrect = clickedEmoji === verification.captchaData.targetEmoji;

        if (!isCorrect) {
            // Resposta incorreta
            const retryEmbed = new EmbedBuilder()
                .setTitle('❌ Emoji Incorreto')
                .setDescription('Tente novamente com o emoji correto.')
                .addFields({
                    name: '🎯 Emoji Correto',
                    value: `Procure por: **${verification.captchaData.targetEmoji}**`,
                    inline: true
                })
                .setColor('#ff6b35')
                .setFooter({ text: 'Tente novamente' })
                .setTimestamp();

            return await interaction.editReply({ embeds: [retryEmbed] });
        }

        // Resposta correta - avançar para etapa 3
        verification.captchaSolved = true;
        verification.step = 3;
        client.pendingVerifications.set(interaction.user.id, verification);

        // Etapa 3: Confirmação final
        const finalEmbed = new EmbedBuilder()
            .setTitle('🔗 Verificação Combinada - Etapa 3/3')
            .setDescription('**Última etapa! Confirme sua verificação:**')
            .addFields(
                {
                    name: '✅ Progresso',
                    value: '1️⃣ ✅ Regras aceitas\n2️⃣ ✅ Captcha resolvido\n3️⃣ **Confirmação final** ← Você está aqui',
                    inline: false
                },
                {
                    name: '🎉 Quase lá!',
                    value: 'Clique em "Finalizar Verificação" para completar o processo.',
                    inline: false
                }
            )
            .setColor('#00ff7f')
            .setFooter({ text: 'Etapa 3 de 3 - Clique para finalizar' })
            .setTimestamp();

        const finalRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`combined_final_confirm_${interaction.user.id}`)
                    .setLabel('🎉 Finalizar Verificação')
                    .setStyle(ButtonStyle.Success)
            );

        await interaction.editReply({
            embeds: [finalEmbed],
            components: [finalRow]
        });

    } catch (error) {
        console.error('❌ [COMBINED VERIFICATION] Erro no captcha:', error);
        await interaction.editReply({
            content: '❌ Erro ao processar captcha. Tente novamente.'
        }).catch(() => {});
    }
}

/**
 * Processar confirmação final da verificação combinada
 */
async function handleCombinedFinalConfirmation(interaction, client) {
    const { EmbedBuilder } = require('discord.js');

    try {
        await interaction.deferReply({ ephemeral: true });

        // Verificar se há verificação pendente
        if (!client.pendingVerifications?.has(interaction.user.id)) {
            return await interaction.editReply({
                content: '❌ Nenhuma verificação pendente encontrada!'
            });
        }

        const verification = client.pendingVerifications.get(interaction.user.id);

        // Verificar se todas as etapas foram completadas
        if (!verification.rulesAccepted || !verification.captchaSolved) {
            return await interaction.editReply({
                content: '❌ Nem todas as etapas foram completadas!'
            });
        }

        // Completar verificação
        await client.verificationSystem.completeVerification(
            interaction.member,
            verification.config,
            'combined',
            'combined_complete'
        );

        // Remover verificação pendente
        client.pendingVerifications.delete(interaction.user.id);

        const successEmbed = new EmbedBuilder()
            .setTitle('🎉 Verificação Combinada Concluída!')
            .setDescription('Parabéns! Você completou todas as etapas da verificação.')
            .addFields(
                {
                    name: '✅ Etapas Completadas',
                    value: '1️⃣ ✅ Regras aceitas\n2️⃣ ✅ Captcha resolvido\n3️⃣ ✅ Verificação finalizada',
                    inline: false
                },
                {
                    name: '🎯 Status',
                    value: 'Você agora tem acesso completo ao servidor!',
                    inline: false
                }
            )
            .setColor('#00ff7f')
            .setFooter({ text: 'Nodex | Moderação - Verificação Combinada' })
            .setTimestamp();

        await interaction.editReply({ embeds: [successEmbed] });

    } catch (error) {
        console.error('❌ [COMBINED VERIFICATION] Erro na confirmação final:', error);
        await interaction.editReply({
            content: '❌ Erro ao finalizar verificação. Tente novamente.'
        }).catch(() => {});
    }
}

/**
 * Processar resposta de captcha emoji
 */
async function handleEmojiCaptchaResponse(interaction, client, customId) {
    const { EmbedBuilder } = require('discord.js');

    try {
        console.log(`🎯 [EMOJI CAPTCHA] Iniciando processamento para ${interaction.user.tag}`);
        console.log(`🎯 [EMOJI CAPTCHA] CustomId recebido: ${customId}`);

        await interaction.deferReply({ ephemeral: true });

        // Verificar se há verificação pendente
        console.log(`🎯 [EMOJI CAPTCHA] Verificando pendingVerifications...`);
        console.log(`🎯 [EMOJI CAPTCHA] client.pendingVerifications existe: ${!!client.pendingVerifications}`);
        console.log(`🎯 [EMOJI CAPTCHA] Usuário tem verificação pendente: ${client.pendingVerifications?.has(interaction.user.id)}`);

        if (!client.pendingVerifications || !client.pendingVerifications.has(interaction.user.id)) {
            console.log(`❌ [EMOJI CAPTCHA] Nenhuma verificação pendente para ${interaction.user.tag}`);
            return await interaction.editReply({
                content: '❌ Nenhuma verificação pendente encontrada!'
            });
        }

        const pendingVerification = client.pendingVerifications.get(interaction.user.id);

        // Verificar se é do servidor correto
        if (pendingVerification.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Verificação não encontrada para este servidor!'
            });
        }

        // Extrair emoji clicado de forma mais robusta
        let clickedEmoji;
        if (customId.startsWith('captcha_emoji_')) {
            clickedEmoji = customId.replace('captcha_emoji_', '');
        } else if (customId.includes('captcha_emoji_')) {
            // Fallback para diferentes codificações
            const parts = customId.split('captcha_emoji_');
            clickedEmoji = parts[parts.length - 1];
        } else {
            // Último fallback - tentar extrair emoji do final
            const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
            const matches = customId.match(emojiRegex);
            clickedEmoji = matches ? matches[matches.length - 1] : customId.slice(-2);
        }

        console.log(`🎯 [EMOJI CAPTCHA] Emoji extraído: "${clickedEmoji}"`);
        console.log(`🎯 [EMOJI CAPTCHA] Tamanho do emoji: ${clickedEmoji.length}`);

        const { captchaData, config } = pendingVerification;

        // Verificar se a resposta está correta com múltiplas validações
        console.log(`🎯 [EMOJI CAPTCHA] Comparando emojis:`);
        console.log(`🎯 [EMOJI CAPTCHA] Clicado: "${clickedEmoji}" (${clickedEmoji.length} chars)`);
        console.log(`🎯 [EMOJI CAPTCHA] Esperado: "${captchaData.targetEmoji}" (${captchaData.targetEmoji.length} chars)`);
        console.log(`🎯 [EMOJI CAPTCHA] Comparação direta: ${clickedEmoji === captchaData.targetEmoji}`);
        console.log(`🎯 [EMOJI CAPTCHA] Comparação normalizada: ${clickedEmoji.trim() === captchaData.targetEmoji.trim()}`);

        // Múltiplas formas de validação
        let isCorrect = false;

        // Validação 1: Comparação direta
        if (clickedEmoji === captchaData.targetEmoji) {
            isCorrect = true;
            console.log(`✅ [EMOJI CAPTCHA] Validação 1 (direta) passou`);
        }

        // Validação 2: Comparação com trim
        else if (clickedEmoji.trim() === captchaData.targetEmoji.trim()) {
            isCorrect = true;
            console.log(`✅ [EMOJI CAPTCHA] Validação 2 (trim) passou`);
        }

        // Validação 3: Verificar se o emoji clicado está nas opções
        else if (captchaData.options && captchaData.options.includes(clickedEmoji)) {
            isCorrect = clickedEmoji === captchaData.targetEmoji;
            console.log(`✅ [EMOJI CAPTCHA] Validação 3 (opções) passou: ${isCorrect}`);
        }

        // Validação 4: Fallback - verificar se contém o emoji
        else if (captchaData.targetEmoji.includes(clickedEmoji) || clickedEmoji.includes(captchaData.targetEmoji)) {
            isCorrect = true;
            console.log(`✅ [EMOJI CAPTCHA] Validação 4 (contém) passou`);
        }

        console.log(`🎯 [EMOJI CAPTCHA] Resultado final: ${isCorrect ? 'CORRETO' : 'INCORRETO'}`);

        if (isCorrect) {
            // Resposta correta - completar verificação
            await client.verificationSystem.completeVerification(
                interaction.member,
                config,
                'captcha_emoji',
                'emoji_solved'
            );

            // Remover verificação pendente
            client.pendingVerifications.delete(interaction.user.id);

            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Captcha Correto!')
                .setDescription('Parabéns! Você foi verificado com sucesso.')
                .addFields({
                    name: '🎯 Emoji Correto',
                    value: `Você selecionou: ${clickedEmoji}`,
                    inline: true
                })
                .setColor('#00ff7f')
                .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                .setTimestamp();

            await interaction.editReply({ embeds: [successEmbed] });

        } else {
            // Resposta incorreta - incrementar tentativas
            const attempts = (pendingVerification.attempts || 0) + 1;
            const maxAttempts = config.max_attempts || 3;

            if (attempts >= maxAttempts) {
                // Máximo de tentativas atingido
                client.pendingVerifications.delete(interaction.user.id);

                // Log da falha
                client.database.db.prepare(`
                    INSERT INTO verification_logs
                    (guild_id, user_id, action, method, details, timestamp)
                    VALUES (?, ?, 'failed_max_attempts', 'captcha_emoji', ?, CURRENT_TIMESTAMP)
                `).run(interaction.guild.id, interaction.user.id, JSON.stringify({
                    attempts: attempts,
                    max_attempts: maxAttempts,
                    target_emoji: captchaData.targetEmoji,
                    clicked_emoji: clickedEmoji
                }));

                // Enviar log para canal
                if (client.verificationSystem) {
                    await client.verificationSystem.sendVerificationLogToChannel(
                        interaction.member,
                        config,
                        'failed_max_attempts',
                        'captcha_emoji',
                        {
                            attempts: attempts,
                            max_attempts: maxAttempts,
                            target_emoji: captchaData.targetEmoji,
                            clicked_emoji: clickedEmoji
                        }
                    );
                }

                const failEmbed = new EmbedBuilder()
                    .setTitle('❌ Tentativas Esgotadas')
                    .setDescription(`Você excedeu o número máximo de tentativas (${maxAttempts}).`)
                    .addFields({
                        name: '📞 Próximo Passo',
                        value: 'Entre em contato com um moderador para assistência.',
                        inline: false
                    })
                    .setColor('#ff4757')
                    .setFooter({ text: 'Nodex | Moderação' })
                    .setTimestamp();

                await interaction.editReply({ embeds: [failEmbed] });

            } else {
                // Ainda há tentativas - atualizar contador
                pendingVerification.attempts = attempts;
                client.pendingVerifications.set(interaction.user.id, pendingVerification);

                // Incrementar tentativas no banco
                await client.verificationSystem.incrementAttempts(interaction.member);

                const retryEmbed = new EmbedBuilder()
                    .setTitle('❌ Emoji Incorreto')
                    .setDescription(`Tente novamente. Tentativas restantes: ${maxAttempts - attempts}`)
                    .addFields(
                        {
                            name: '🎯 Emoji Correto',
                            value: `Procure por: **${captchaData.targetEmoji}**`,
                            inline: true
                        },
                        {
                            name: '❌ Você Clicou',
                            value: `${clickedEmoji}`,
                            inline: true
                        }
                    )
                    .setColor('#ffa500')
                    .setFooter({ text: 'Nodex | Moderação' })
                    .setTimestamp();

                await interaction.editReply({ embeds: [retryEmbed] });
            }
        }

    } catch (error) {
        console.error('❌ [VERIFICATION] Erro ao processar emoji captcha:', error);
        await interaction.editReply({
            content: '❌ Erro ao processar resposta do captcha. Tente novamente.'
        }).catch(() => {});
    }
}
