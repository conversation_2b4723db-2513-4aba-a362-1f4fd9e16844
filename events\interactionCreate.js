/**
 * ========================================
 * EVENTO: INTERACTION CREATE
 * Manipula todas as interações (comandos, botões, etc.)
 * ========================================
 */

const { Events, EmbedBuilder, Collection } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        // Comandos Slash
        if (interaction.isChatInputCommand()) {
            await handleSlashCommand(interaction, client);
        }
        
        // Botões
        else if (interaction.isButton()) {
            await handleButton(interaction, client);
        }
        
        // Menus de Seleção
        else if (interaction.isStringSelectMenu()) {
            await handleSelectMenu(interaction, client);
        }
        
        // Modais
        else if (interaction.isModalSubmit()) {
            await handleModal(interaction, client);
        }
        
        // Context Menus (comandos de contexto)
        else if (interaction.isContextMenuCommand()) {
            await handleContextMenu(interaction, client);
        }
    }
};

/**
 * Manipula comandos slash
 */
async function handleSlashCommand(interaction, client) {
    const command = client.commands.get(interaction.commandName);

    if (!command) {
        client.logger.warn(`Comando não encontrado: ${interaction.commandName}`, {
            user: interaction.user.id,
            guild: interaction.guild?.id
        });
        
        return await interaction.reply({
            content: '❌ Comando não encontrado!',
            ephemeral: true
        });
    }

    try {
        // Verificar cooldown
        const cooldownResult = checkCooldown(interaction, client, command);
        if (cooldownResult.onCooldown) {
            return await interaction.reply({
                content: `⏰ Aguarde ${cooldownResult.timeLeft} antes de usar este comando novamente.`,
                ephemeral: true
            });
        }

        // Verificar se o comando pode ser usado em DM
        if (!interaction.guild && command.data.dm_permission === false) {
            return await interaction.reply({
                content: '❌ Este comando não pode ser usado em mensagens diretas!',
                ephemeral: true
            });
        }

        // Verificar se o comando está habilitado no servidor
        if (interaction.guild && client.commandStateManager) {
            const isEnabled = client.commandStateManager.isCommandEnabled(
                interaction.guild.id,
                interaction.commandName
            );

            if (!isEnabled) {
                const EmbedStyles = require('../utils/EmbedStyles');
                const embedStyles = new EmbedStyles();

                const disabledEmbed = embedStyles.createWarningEmbed(
                    'Comando Desabilitado',
                    `${embedStyles.format.bold('Este comando foi desabilitado pelos administradores.')}\n\n${embedStyles.icons.info} **Comando:** \`/${interaction.commandName}\`\n${embedStyles.icons.settings} **Status:** Desabilitado\n\n${embedStyles.format.italic('Entre em contato com os administradores se precisar usar este comando.')}`
                );

                return await interaction.reply({
                    embeds: [disabledEmbed],
                    ephemeral: true
                });
            }
        }

        // Verificar permissões do usuário
        if (interaction.guild && command.data.default_member_permissions) {
            const userPermissions = interaction.member.permissions;
            const requiredPermissions = command.data.default_member_permissions;
            
            if (!userPermissions.has(requiredPermissions)) {
                return await interaction.reply({
                    content: '❌ Você não tem permissão para usar este comando!',
                    ephemeral: true
                });
            }
        }

        // Verificar permissões do bot
        if (interaction.guild && command.botPermissions) {
            const botPermissions = interaction.guild.members.me.permissions;
            const missingPermissions = command.botPermissions.filter(perm => !botPermissions.has(perm));
            
            if (missingPermissions.length > 0) {
                return await interaction.reply({
                    content: `❌ Eu não tenho as seguintes permissões necessárias: ${missingPermissions.join(', ')}`,
                    ephemeral: true
                });
            }
        }

        // Executar comando
        const startTime = Date.now();
        await command.execute(interaction);
        const executionTime = Date.now() - startTime;

        // Log do comando
        client.logger.command(
            interaction.commandName,
            interaction.user.id,
            interaction.guild?.id,
            true
        );

        // Log de performance se demorou muito
        if (executionTime > 3000) {
            client.logger.performance(
                `Comando ${interaction.commandName}`,
                executionTime,
                {
                    user: interaction.user.id,
                    guild: interaction.guild?.id
                }
            );
        }

        // Atualizar cooldown
        setCooldown(interaction, client, command);

    } catch (error) {
        client.logger.error(`Erro no comando ${interaction.commandName}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id,
            options: interaction.options.data
        });

        const errorEmbed = new EmbedBuilder()
            .setColor('#e74c3c')
            .setTitle('❌ Erro no Comando')
            .setDescription('Ocorreu um erro ao executar este comando. O erro foi registrado e será investigado.')
            .addFields({
                name: '🆔 ID do Erro',
                value: `\`${Date.now()}\``,
                inline: true
            })
            .setTimestamp()
            .setFooter({ text: 'Nova Moderação Bot' });

        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        } catch (followUpError) {
            client.logger.error('Erro ao enviar mensagem de erro:', followUpError);
        }

        // Log do comando com erro
        client.logger.command(
            interaction.commandName,
            interaction.user.id,
            interaction.guild?.id,
            false,
            error.message
        );
    }
}

/**
 * Manipula cliques em botões
 */
async function handleButton(interaction, client) {
    const customId = interaction.customId;

    try {
        // Botão de voltar na ajuda
        if (customId === 'help_back') {
            // Recriar o embed principal de ajuda
            const { EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

            const helpEmbed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('📚 Nodex | Moderação - Ajuda')
                .setDescription(`Olá! Eu sou o **Nodex | Moderação**, seu assistente de moderação inteligente!\n\n🇧🇷 **100% em Português Brasileiro**\n🤖 **IA Integrada para moderação inteligente**\n🛡️ **Sistema anti-raid avançado**`)
                .addFields(
                    {
                        name: '🔧 Como Usar',
                        value: `• **Comandos Slash:** Digite \`/\` e escolha um comando\n• **Configuração:** Use \`/config\` para configurar o bot`,
                        inline: false
                    },
                    {
                        name: '📋 Categorias de Comandos',
                        value: '🔨 **Moderação** - Comandos para moderar o servidor\n⚙️ **Configuração** - Configurações do bot\n📊 **Informações** - Estatísticas e informações\n🎫 **Utilidades** - Comandos úteis diversos',
                        inline: false
                    },
                    {
                        name: '🚀 Recursos Principais',
                        value: '• Auto-moderação inteligente\n• Sistema anti-raid\n• Logs detalhados\n• Dashboard web\n• IA integrada',
                        inline: false
                    }
                )
                .setThumbnail(client.user.displayAvatarURL())
                .setTimestamp()
                .setFooter({
                    text: 'Nodex | Moderação • Use o menu abaixo para navegar',
                    iconURL: client.user.displayAvatarURL()
                });

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('help_category_select')
                .setPlaceholder('📂 Selecione uma categoria para ver os comandos')
                .addOptions([
                    {
                        label: 'Moderação',
                        description: 'Comandos para moderar o servidor',
                        value: 'moderation',
                        emoji: '🔨'
                    },
                    {
                        label: 'Configuração',
                        description: 'Configurações do bot',
                        value: 'config',
                        emoji: '⚙️'
                    },
                    {
                        label: 'Informações',
                        description: 'Estatísticas e informações',
                        value: 'info',
                        emoji: '📊'
                    },
                    {
                        label: 'Utilidades',
                        description: 'Comandos úteis diversos',
                        value: 'utility',
                        emoji: '🎫'
                    }
                ]);

            const buttons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('Dashboard Web')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`http://localhost:${process.env.WEB_PORT || 3000}/dashboard`)
                        .setEmoji('🌐'),
                    new ButtonBuilder()
                        .setLabel('Suporte')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`http://localhost:${process.env.WEB_PORT || 3000}/support`)
                        .setEmoji('🆘'),
                    new ButtonBuilder()
                        .setLabel('Documentação')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`http://localhost:${process.env.WEB_PORT || 3000}/docs`)
                        .setEmoji('📖')
                );

            const row1 = new ActionRowBuilder().addComponents(selectMenu);
            const row2 = buttons;

            await interaction.update({
                embeds: [helpEmbed],
                components: [row1, row2]
            });
        }

        // Botões de configuração
        else if (customId.startsWith('config_')) {
            await handleConfigButton(interaction, client);
        }

        // Botões de moderação
        else if (customId.startsWith('mod_')) {
            await handleModerationButton(interaction, client);
        }





        // Botões de verificação (IMPLEMENTADO - SEGUINDO PADRÃO DOS MÓDULOS FUNCIONAIS)
        else if (customId.startsWith('verify_')) {
            await handleVerificationButton(interaction, client);
        }

        // Botão não reconhecido
        else {
            await interaction.reply({
                content: '❌ Botão não reconhecido!',
                ephemeral: true
            });
        }

    } catch (error) {
        client.logger.error(`Erro no botão ${customId}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id
        });

        await interaction.reply({
            content: '❌ Ocorreu um erro ao processar esta ação.',
            ephemeral: true
        }).catch(() => {});
    }
}

/**
 * Manipula botões de configuração
 */
async function handleConfigButton(interaction, client) {
    const action = interaction.customId.replace('config_', '');
    const guild = interaction.guild;

    // Verificar permissões
    if (!interaction.member.permissions.has('Administrator')) {
        return await interaction.reply({
            content: '❌ Apenas administradores podem alterar configurações!',
            ephemeral: true
        });
    }

    let guildConfig = client.database.getGuildConfig(guild.id) || {};

    switch (action) {
        case 'automod_toggle':
            guildConfig.auto_mod_enabled = !guildConfig.auto_mod_enabled;
            client.database.saveGuildConfig(guild.id, guildConfig);
            
            await interaction.reply({
                content: `🤖 Auto-moderação ${guildConfig.auto_mod_enabled ? 'ativada' : 'desativada'}!`,
                ephemeral: true
            });
            break;

        case 'antiraid_toggle':
            guildConfig.anti_raid_enabled = !guildConfig.anti_raid_enabled;
            client.database.saveGuildConfig(guild.id, guildConfig);
            
            await interaction.reply({
                content: `🛡️ Anti-raid ${guildConfig.anti_raid_enabled ? 'ativado' : 'desativado'}!`,
                ephemeral: true
            });
            break;

        case 'advanced':
            // Mostrar menu de configurações avançadas
            await showAdvancedConfigMenu(interaction, client);
            break;

        default:
            await interaction.reply({
                content: '❌ Ação de configuração não reconhecida!',
                ephemeral: true
            });
    }
}

/**
 * Manipula menus de seleção
 */
async function handleSelectMenu(interaction, client) {
    const customId = interaction.customId;
    const values = interaction.values;

    try {
        // Menu de ajuda
        if (customId === 'help_category_select') {
            const category = values[0];
            const { handleCategorySelection } = require('../commands/geral/help.js');
            await handleCategorySelection(interaction, category);
        }
        else if (customId === 'config_advanced_menu') {
            await handleAdvancedConfigSelection(interaction, client, values);
        }
        else if (customId === 'mod_action_menu') {
            await handleModerationActionSelection(interaction, client, values);
        }
        else {
            await interaction.reply({
                content: '❌ Menu não reconhecido!',
                ephemeral: true
            });
        }

    } catch (error) {
        client.logger.error(`Erro no menu ${customId}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id,
            values: values
        });

        await interaction.reply({
            content: '❌ Ocorreu um erro ao processar sua seleção.',
            ephemeral: true
        }).catch(() => {});
    }
}

/**
 * Manipula modais
 */
async function handleModal(interaction, client) {
    const customId = interaction.customId;

    try {
        if (customId === 'reason_modal') {
            await handleReasonModal(interaction, client);
        }
        else if (customId === 'config_modal') {
            await handleConfigModal(interaction, client);
        }
        else {
            await interaction.reply({
                content: '❌ Modal não reconhecido!',
                ephemeral: true
            });
        }

    } catch (error) {
        client.logger.error(`Erro no modal ${customId}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id
        });

        await interaction.reply({
            content: '❌ Ocorreu um erro ao processar o formulário.',
            ephemeral: true
        }).catch(() => {});
    }
}

/**
 * Manipula botões de verificação (NOVO - SEGUINDO PADRÃO DOS MÓDULOS FUNCIONAIS)
 */
async function handleVerificationButton(interaction, client) {
    const customId = interaction.customId;

    try {
        if (customId === 'verify_user') {
            console.log(`🛡️ [VERIFICATION] Usuário ${interaction.user.tag} clicou no botão de verificação`);

            // Verificar se o sistema de verificação está disponível
            if (!client.verificationSystem) {
                return await interaction.reply({
                    content: '❌ Sistema de verificação não disponível!',
                    ephemeral: true
                });
            }

            // Buscar configuração de verificação
            const config = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ? AND enabled = 1
            `).get(interaction.guild.id);

            if (!config) {
                return await interaction.reply({
                    content: '❌ Sistema de verificação não configurado neste servidor!',
                    ephemeral: true
                });
            }

            // Verificar se o usuário já está verificado
            const existingVerification = client.database.db.prepare(`
                SELECT verified FROM member_verification
                WHERE guild_id = ? AND user_id = ?
            `).get(interaction.guild.id, interaction.user.id);

            if (existingVerification?.verified) {
                return await interaction.reply({
                    content: '✅ Você já está verificado neste servidor!',
                    ephemeral: true
                });
            }

            // Processar verificação
            const member = interaction.member;
            const guild = interaction.guild;

            // Obter cargo de verificado
            const verifiedRole = guild.roles.cache.get(config.verified_role_id);
            if (!verifiedRole) {
                return await interaction.reply({
                    content: '❌ Cargo de verificado não encontrado! Contate um administrador.',
                    ephemeral: true
                });
            }

            // Adicionar cargo de verificado
            await member.roles.add(verifiedRole, 'Verificação automática via botão');

            // Remover cargo de não verificado se existir
            if (config.unverified_role_id) {
                const unverifiedRole = guild.roles.cache.get(config.unverified_role_id);
                if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
                    await member.roles.remove(unverifiedRole, 'Usuário verificado');
                }
            }

            // Registrar verificação no banco
            client.database.db.prepare(`
                INSERT OR REPLACE INTO member_verification
                (guild_id, user_id, verified, verification_method, verified_at)
                VALUES (?, ?, 1, 'button', CURRENT_TIMESTAMP)
            `).run(guild.id, member.id);

            // Log da verificação
            client.database.db.prepare(`
                INSERT INTO verification_logs
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'completed', 'button', ?, CURRENT_TIMESTAMP)
            `).run(guild.id, member.id, JSON.stringify({
                success: true,
                roleAdded: verifiedRole.name
            }));

            console.log(`✅ [VERIFICATION] ${member.user.tag} verificado com sucesso no servidor ${guild.name}`);

            // Responder ao usuário
            const { EmbedBuilder } = require('discord.js');
            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Verificação Concluída!')
                .setDescription(`Parabéns! Você foi verificado com sucesso no servidor **${guild.name}**.`)
                .addFields(
                    { name: '🎯 Cargo Obtido', value: `@${verifiedRole.name}`, inline: true },
                    { name: '🕐 Verificado em', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                )
                .setColor('#00ff7f')
                .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                .setTimestamp();

            await interaction.reply({
                embeds: [successEmbed],
                ephemeral: true
            });

            // Enviar mensagem de boas-vindas se configurada
            if (config.welcome_message) {
                try {
                    await member.send({
                        embeds: [new EmbedBuilder()
                            .setTitle(`🎉 Bem-vindo(a) ao ${guild.name}!`)
                            .setDescription(config.welcome_message)
                            .setColor('#00ff7f')
                            .setFooter({ text: 'Nodex | Moderação' })
                        ]
                    });
                } catch (error) {
                    console.log(`⚠️ [VERIFICATION] Não foi possível enviar DM de boas-vindas para ${member.user.tag}`);
                }
            }

        } else {
            await interaction.reply({
                content: '❌ Botão de verificação não reconhecido!',
                ephemeral: true
            });
        }

    } catch (error) {
        client.logger.error(`Erro no botão de verificação ${customId}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id
        });

        await interaction.reply({
            content: '❌ Ocorreu um erro durante a verificação. Tente novamente ou contate um administrador.',
            ephemeral: true
        }).catch(() => {});
    }
}

/**
 * Manipula comandos de contexto
 */
async function handleContextMenu(interaction, client) {
    const command = client.commands.get(interaction.commandName);

    if (!command) {
        return await interaction.reply({
            content: '❌ Comando de contexto não encontrado!',
            ephemeral: true
        });
    }

    try {
        await command.execute(interaction);
        
        client.logger.command(
            interaction.commandName,
            interaction.user.id,
            interaction.guild?.id,
            true
        );

    } catch (error) {
        client.logger.error(`Erro no comando de contexto ${interaction.commandName}:`, error);

        await interaction.reply({
            content: '❌ Ocorreu um erro ao executar esta ação.',
            ephemeral: true
        }).catch(() => {});
    }
}

/**
 * Verifica cooldown de comandos
 */
function checkCooldown(interaction, client, command) {
    if (!command.cooldown) return { onCooldown: false };

    const cooldowns = client.cooldowns;
    const commandName = command.data.name;

    if (!cooldowns.has(commandName)) {
        cooldowns.set(commandName, new Collection());
    }

    const now = Date.now();
    const timestamps = cooldowns.get(commandName);
    const cooldownAmount = command.cooldown * 1000;

    if (timestamps.has(interaction.user.id)) {
        const expirationTime = timestamps.get(interaction.user.id) + cooldownAmount;

        if (now < expirationTime) {
            const timeLeft = Math.ceil((expirationTime - now) / 1000);
            return {
                onCooldown: true,
                timeLeft: `${timeLeft} segundo(s)`
            };
        }
    }

    return { onCooldown: false };
}

/**
 * Define cooldown para um comando
 */
function setCooldown(interaction, client, command) {
    if (!command.cooldown) return;

    const cooldowns = client.cooldowns;
    const commandName = command.data.name;
    const timestamps = cooldowns.get(commandName);
    
    timestamps.set(interaction.user.id, Date.now());
    
    // Remover cooldown após expirar
    setTimeout(() => {
        timestamps.delete(interaction.user.id);
    }, command.cooldown * 1000);
}

/**
 * Funções auxiliares para botões específicos
 */
async function handleModerationButton(interaction, client) {
    // Implementar lógica de botões de moderação
    await interaction.reply({
        content: '🔧 Funcionalidade de moderação em desenvolvimento!',
        ephemeral: true
    });
}



async function handleVerificationButton(interaction, client) {
    const customId = interaction.customId;

    try {
        if (customId === 'verify_user') {
            console.log(`🛡️ [VERIFICATION] Usuário ${interaction.user.tag} clicou no botão de verificação`);

            // Defer reply immediately to prevent timeout
            await interaction.deferReply({ ephemeral: true });

            // Verificar se o sistema de verificação está disponível
            if (!client.verificationSystem) {
                return await interaction.editReply({
                    content: '❌ Sistema de verificação não disponível!'
                });
            }

            // Buscar configuração de verificação
            const stmt = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ? AND enabled = 1
            `);
            const config = stmt.get(interaction.guild.id);

            console.log(`🔍 [VERIFICATION DEBUG] Config encontrada:`, config);

            if (!config) {
                return await interaction.editReply({
                    content: '❌ Sistema de verificação não configurado neste servidor!'
                });
            }

            // Verificar se o usuário já está verificado
            const existingVerification = client.database.db.prepare(`
                SELECT verified FROM member_verification
                WHERE guild_id = ? AND user_id = ?
            `).get(interaction.guild.id, interaction.user.id);

            if (existingVerification?.verified) {
                return await interaction.editReply({
                    content: '✅ Você já está verificado neste servidor!'
                });
            }

            // Processar verificação
            const member = interaction.member;
            const guild = interaction.guild;

            // Obter cargo de verificado
            console.log(`🔍 [VERIFICATION DEBUG] Procurando cargo com ID: ${config.verified_role_id}`);
            console.log(`🔍 [VERIFICATION DEBUG] Cargos disponíveis no servidor:`, guild.roles.cache.map(r => `${r.name} (${r.id})`));

            // VALIDAÇÃO RIGOROSA - DETECTAR E CORRIGIR VALORES "undefined" COMO STRING
            if (!config.verified_role_id || config.verified_role_id === 'undefined' || config.verified_role_id === 'null') {
                console.error(`❌ [VERIFICATION DEBUG] ID do cargo inválido detectado: "${config.verified_role_id}"`);

                // Tentar corrigir automaticamente no banco de dados
                try {
                    client.database.db.prepare(`
                        UPDATE verification_config
                        SET verified_role_id = NULL
                        WHERE guild_id = ? AND (verified_role_id = 'undefined' OR verified_role_id = 'null')
                    `).run(guild.id);
                    console.log(`🔧 [VERIFICATION DEBUG] Valor inválido corrigido no banco de dados`);
                } catch (error) {
                    console.error(`❌ [VERIFICATION DEBUG] Erro ao corrigir banco:`, error);
                }

                return await interaction.editReply({
                    content: `❌ Sistema de verificação não configurado corretamente! O cargo de verificado não foi definido. Contate um administrador para configurar o sistema.`
                });
            }

            const verifiedRole = guild.roles.cache.get(config.verified_role_id);
            console.log(`🔍 [VERIFICATION DEBUG] Cargo encontrado:`, verifiedRole ? `${verifiedRole.name} (${verifiedRole.id})` : 'null');

            if (!verifiedRole) {
                return await interaction.editReply({
                    content: `❌ Cargo de verificado não encontrado! ID configurado: ${config.verified_role_id}. Contate um administrador.`
                });
            }

            // Adicionar cargo de verificado
            await member.roles.add(verifiedRole, 'Verificação automática via botão');

            // Remover cargo de não verificado se existir
            if (config.unverified_role_id) {
                const unverifiedRole = guild.roles.cache.get(config.unverified_role_id);
                if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
                    await member.roles.remove(unverifiedRole, 'Usuário verificado');
                }
            }

            // Registrar verificação no banco
            client.database.db.prepare(`
                INSERT OR REPLACE INTO member_verification
                (guild_id, user_id, verified, verification_method, verified_at)
                VALUES (?, ?, 1, 'button', CURRENT_TIMESTAMP)
            `).run(guild.id, member.id);

            // Log da verificação
            client.database.db.prepare(`
                INSERT INTO verification_logs
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'completed', 'button', ?, CURRENT_TIMESTAMP)
            `).run(guild.id, member.id, JSON.stringify({
                success: true,
                roleAdded: verifiedRole.name
            }));

            console.log(`✅ [VERIFICATION] ${member.user.tag} verificado com sucesso no servidor ${guild.name}`);

            // Responder ao usuário
            const { EmbedBuilder } = require('discord.js');
            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Verificação Concluída!')
                .setDescription(`Parabéns! Você foi verificado com sucesso no servidor **${guild.name}**.`)
                .addFields(
                    { name: '🎯 Cargo Obtido', value: `@${verifiedRole.name}`, inline: true },
                    { name: '🕐 Verificado em', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                )
                .setColor('#00ff7f')
                .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                .setTimestamp();

            await interaction.editReply({
                embeds: [successEmbed]
            });

            // Enviar mensagem de boas-vindas se configurada
            if (config.welcome_message) {
                try {
                    await member.send({
                        embeds: [new EmbedBuilder()
                            .setTitle(`🎉 Bem-vindo(a) ao ${guild.name}!`)
                            .setDescription(config.welcome_message)
                            .setColor('#00ff7f')
                            .setFooter({ text: 'Nodex | Moderação' })
                        ]
                    });
                } catch (error) {
                    console.log(`⚠️ [VERIFICATION] Não foi possível enviar DM de boas-vindas para ${member.user.tag}`);
                }
            }

        } else {
            await interaction.editReply({
                content: '❌ Botão de verificação não reconhecido!'
            });
        }

    } catch (error) {
        client.logger.error(`Erro no botão de verificação ${customId}:`, error, {
            user: interaction.user.id,
            guild: interaction.guild?.id
        });

        const errorResponse = {
            content: '❌ Ocorreu um erro durante a verificação. Tente novamente ou contate um administrador.'
        };

        if (interaction.deferred) {
            await interaction.editReply(errorResponse).catch(() => {});
        } else {
            await interaction.reply({ ...errorResponse, ephemeral: true }).catch(() => {});
        }
    }
}



async function showAdvancedConfigMenu(interaction, client) {
    // Implementar menu de configurações avançadas
    await interaction.reply({
        content: '⚙️ Menu de configurações avançadas em desenvolvimento!',
        ephemeral: true
    });
}

async function handleAdvancedConfigSelection(interaction, client, values) {
    // Implementar seleção de configurações avançadas
    await interaction.reply({
        content: `⚙️ Configuração selecionada: ${values.join(', ')}`,
        ephemeral: true
    });
}

async function handleModerationActionSelection(interaction, client, values) {
    // Implementar seleção de ações de moderação
    await interaction.reply({
        content: `🔨 Ação de moderação selecionada: ${values.join(', ')}`,
        ephemeral: true
    });
}

async function handleReasonModal(interaction, client) {
    // Implementar modal de motivo
    await interaction.reply({
        content: '📝 Modal de motivo processado!',
        ephemeral: true
    });
}

async function handleConfigModal(interaction, client) {
    // Implementar modal de configuração
    await interaction.reply({
        content: '⚙️ Modal de configuração processado!',
        ephemeral: true
    });
}
