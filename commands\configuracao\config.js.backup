/**
 * ========================================
 * COMANDO: CONFIG
 * Sistema de configuração do bot
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const EmbedStyles = require('../../utils/EmbedStyles');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('config')
        .setDescription('⚙️ Configurações do bot para o servidor')
        .addSubcommand(subcommand =>
            subcommand
                .setName('ver')
                .setDescription('Ver configurações atuais do servidor'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('canal-logs')
                .setDescription('Definir canal de logs')
                .addChannelOption(option =>
                    option.setName('canal')
                        .setDescription('Canal para logs gerais')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('canal-moderacao')
                .setDescription('Definir canal de logs de moderação')
                .addChannelOption(option =>
                    option.setName('canal')
                        .setDescription('Canal para logs de moderação')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('auto-mod')
                .setDescription('Configurar auto-moderação')
                .addBooleanOption(option =>
                    option.setName('ativo')
                        .setDescription('Ativar/desativar auto-moderação')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('anti-raid')
                .setDescription('Configurar sistema anti-raid')
                .addBooleanOption(option =>
                    option.setName('ativo')
                        .setDescription('Ativar/desativar anti-raid')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('ia')
                .setDescription('Configurar moderação por IA')
                .addBooleanOption(option =>
                    option.setName('ativo')
                        .setDescription('Ativar/desativar moderação por IA')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('prefixo')
                .setDescription('Alterar prefixo do bot')
                .addStringOption(option =>
                    option.setName('novo_prefixo')
                        .setDescription('Novo prefixo (máximo 3 caracteres)')
                        .setMaxLength(3)
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Resetar todas as configurações para o padrão'))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .setDMPermission(false),

    async execute(interaction) {
        const client = interaction.client;
        const guild = interaction.guild;
        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'ver':
                    await this.showCurrentConfig(interaction);
                    break;
                case 'canal-logs':
                    await this.setLogChannel(interaction);
                    break;
                case 'canal-moderacao':
                    await this.setModerationChannel(interaction);
                    break;
                case 'auto-mod':
                    await this.toggleAutoMod(interaction);
                    break;
                case 'anti-raid':
                    await this.toggleAntiRaid(interaction);
                    break;
                case 'ia':
                    await this.toggleAI(interaction);
                    break;
                case 'prefixo':
                    await this.setPrefix(interaction);
                    break;
                case 'reset':
                    await this.resetConfig(interaction);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Subcomando não reconhecido!',
                        ephemeral: true
                    });
            }
        } catch (error) {
            client.logger.error('Erro no comando config:', error, {
                guild: guild.id,
                user: interaction.user.id,
                subcommand: subcommand
            });

            const errorEmbed = new EmbedBuilder()
                .setColor('#e74c3c')
                .setTitle('❌ Erro')
                .setDescription('Ocorreu um erro ao processar a configuração. Tente novamente.')
                .setTimestamp();

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    /**
     * Mostra configurações atuais
     */
    async showCurrentConfig(interaction) {
        const embedStyles = new EmbedStyles();
        const guild = interaction.guild;
        const client = interaction.client;

        // Obter configurações do banco
        let guildConfig = client.database.getGuildConfig(guild.id);

        // Se não existir, criar configuração padrão
        if (!guildConfig) {
            const defaultConfig = {
                prefix: '!',
                language: 'pt-BR',
                timezone: 'America/Sao_Paulo',
                auto_mod_enabled: true,
                anti_raid_enabled: true,
                settings: {}
            };

            client.database.saveGuildConfig(guild.id, defaultConfig);
            guildConfig = defaultConfig;
        }

        // Obter canais
        const logChannel = guildConfig.log_channel_id 
            ? guild.channels.cache.get(guildConfig.log_channel_id) 
            : null;
        const modLogChannel = guildConfig.mod_log_channel_id 
            ? guild.channels.cache.get(guildConfig.mod_log_channel_id) 
            : null;
        const welcomeChannel = guildConfig.welcome_channel_id 
            ? guild.channels.cache.get(guildConfig.welcome_channel_id) 
            : null;

        // Obter cargos
        const muteRole = guildConfig.mute_role_id 
            ? guild.roles.cache.get(guildConfig.mute_role_id) 
            : null;
        const quarantineRole = guildConfig.quarantine_role_id 
            ? guild.roles.cache.get(guildConfig.quarantine_role_id) 
            : null;

        // Criar embed premium de configurações
        const settings = JSON.parse(guildConfig.settings || '{}');

        const configEmbed = {
            color: parseInt(embedStyles.colors.info.replace('#', ''), 16),
            title: `${embedStyles.icons.settings} ${embedStyles.format.bold('Configuração Premium do Servidor')}`,
            description: `**Configuração completa do ${embedStyles.format.bold(guild.name)}**\n\n${embedStyles.format.italic('Visão geral de todos os módulos e sistemas configurados')}`,
            fields: [
                {
                    name: `${embedStyles.icons.settings} **Configurações Gerais**`,
                    value: `${embedStyles.format.bold('Prefixo:')} ${embedStyles.format.code(guildConfig.prefix)}\n${embedStyles.format.bold('Idioma:')} ${guildConfig.language}\n${embedStyles.format.bold('Fuso Horário:')} ${guildConfig.timezone}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.shield} **Sistemas de Proteção**`,
                    value: `${embedStyles.createStatusIndicator(guildConfig.auto_mod_enabled ? 'active' : 'inactive', 'Auto-Moderação')}\n${embedStyles.createStatusIndicator(guildConfig.anti_raid_enabled ? 'active' : 'inactive', 'Anti-Raid')}\n${embedStyles.createStatusIndicator(settings.ai_moderation_enabled ? 'active' : 'inactive', 'IA de Moderação')}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.analytics} **Status dos Sistemas**`,
                    value: `${embedStyles.createStatusIndicator(guildConfig.log_enabled ? 'active' : 'inactive', 'Logs Gerais')}\n${embedStyles.createStatusIndicator(settings.tickets_enabled ? 'active' : 'inactive', 'Sistema de Tickets')}\n${embedStyles.createStatusIndicator(settings.analytics_enabled ? 'active' : 'inactive', 'Analytics')}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.channel} **Canais Configurados**`,
                    value: `${embedStyles.format.bold('Logs Gerais:')} ${logChannel ? `<#${logChannel.id}>` : `${embedStyles.icons.error} Não configurado`}\n${embedStyles.format.bold('Logs de Moderação:')} ${modLogChannel ? `<#${modLogChannel.id}>` : `${embedStyles.icons.error} Não configurado`}\n${embedStyles.format.bold('Boas-vindas:')} ${welcomeChannel ? `<#${welcomeChannel.id}>` : `${embedStyles.icons.error} Não configurado`}`,
                    inline: false
                },
                {
                    name: `${embedStyles.icons.role} **Cargos Especiais**`,
                    value: `${embedStyles.format.bold('Cargo de Mute:')} ${muteRole ? `<@&${muteRole.id}>` : `${embedStyles.icons.error} Não configurado`}\n${embedStyles.format.bold('Cargo de Quarentena:')} ${quarantineRole ? `<@&${quarantineRole.id}>` : `${embedStyles.icons.error} Não configurado`}\n${embedStyles.format.bold('Moderadores:')} ${guildConfig.mod_roles?.length ? `${guildConfig.mod_roles.length} cargo(s)` : `${embedStyles.icons.error} Não configurado`}`,
                    inline: false
                },
                {
                    name: `${embedStyles.icons.info} **Informações Adicionais**`,
                    value: `${embedStyles.format.bold('Dashboard:')} ${embedStyles.format.code('http://localhost:3000')}\n${embedStyles.format.bold('Última Atualização:')} ${embedStyles.format.timestampRelative(new Date())}\n${embedStyles.format.bold('Versão:')} ${embedStyles.format.code('2.0.0 Premium')}`,
                    inline: false
                }
            ],
            timestamp: new Date().toISOString(),
            footer: {
                text: 'Nodex | Moderação • Configuração Premium',
                icon_url: guild.iconURL()
            },
            thumbnail: {
                url: guild.iconURL({ dynamic: true })
            }
        };

        // Botões premium para configuração rápida
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('config_automod_toggle')
                    .setLabel(guildConfig.auto_mod_enabled ? 'Desativar Auto-Mod' : 'Ativar Auto-Mod')
                    .setStyle(guildConfig.auto_mod_enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setEmoji(embedStyles.icons.shield),
                new ButtonBuilder()
                    .setCustomId('config_antiraid_toggle')
                    .setLabel(guildConfig.anti_raid_enabled ? 'Desativar Anti-Raid' : 'Ativar Anti-Raid')
                    .setStyle(guildConfig.anti_raid_enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setEmoji(embedStyles.icons.shield),
                new ButtonBuilder()
                    .setCustomId('config_ai_toggle')
                    .setLabel(settings.ai_moderation_enabled ? 'Desativar IA' : 'Ativar IA')
                    .setStyle(settings.ai_moderation_enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setEmoji(embedStyles.icons.ai)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setLabel('🌐 Dashboard Premium')
                    .setURL(`http://localhost:3000/dashboard/${guild.id}`)
                    .setStyle(ButtonStyle.Link),
                new ButtonBuilder()
                    .setCustomId('config_refresh')
                    .setLabel('🔄 Atualizar')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('config_advanced')
                    .setLabel('⚙️ Configurações Avançadas')
                    .setStyle(ButtonStyle.Primary)
            );

        await interaction.reply({ embeds: [configEmbed], components: [row1, row2] });
    },

    /**
     * Define canal de logs
     */
    async setLogChannel(interaction) {
        const channel = interaction.options.getChannel('canal');
        const guild = interaction.guild;
        const client = interaction.client;

        // Verificar se é um canal de texto
        if (!channel.isTextBased()) {
            return await interaction.reply({
                content: '❌ O canal deve ser um canal de texto!',
                ephemeral: true
            });
        }

        // Verificar permissões do bot no canal
        const permissions = channel.permissionsFor(guild.members.me);
        if (!permissions.has(['SendMessages', 'EmbedLinks'])) {
            return await interaction.reply({
                content: '❌ Eu não tenho permissões para enviar mensagens e embeds neste canal!',
                ephemeral: true
            });
        }

        // Obter configuração atual
        let guildConfig = client.database.getGuildConfig(guild.id) || {};
        guildConfig.log_channel_id = channel.id;

        // Salvar no banco
        client.database.saveGuildConfig(guild.id, guildConfig);

        const successEmbed = new EmbedBuilder()
            .setColor('#2ecc71')
            .setTitle('✅ Canal de Logs Definido')
            .setDescription(`Canal de logs definido como ${channel}`)
            .setTimestamp();

        await interaction.reply({ embeds: [successEmbed] });

        // Enviar mensagem de teste no canal
        const testEmbed = new EmbedBuilder()
            .setColor('#3498db')
            .setTitle('📝 Canal de Logs Configurado')
            .setDescription('Este canal foi configurado para receber logs do Nova Moderação Bot!')
            .setTimestamp();

        await channel.send({ embeds: [testEmbed] });

        client.logger.info(`Canal de logs definido: ${channel.name} (${channel.id}) no servidor ${guild.name}`);
    },

    /**
     * Define canal de moderação
     */
    async setModerationChannel(interaction) {
        const channel = interaction.options.getChannel('canal');
        const guild = interaction.guild;
        const client = interaction.client;

        // Verificar se é um canal de texto
        if (!channel.isTextBased()) {
            return await interaction.reply({
                content: '❌ O canal deve ser um canal de texto!',
                ephemeral: true
            });
        }

        // Verificar permissões do bot no canal
        const permissions = channel.permissionsFor(guild.members.me);
        if (!permissions.has(['SendMessages', 'EmbedLinks'])) {
            return await interaction.reply({
                content: '❌ Eu não tenho permissões para enviar mensagens e embeds neste canal!',
                ephemeral: true
            });
        }

        // Obter configuração atual
        let guildConfig = client.database.getGuildConfig(guild.id) || {};
        guildConfig.mod_log_channel_id = channel.id;

        // Salvar no banco
        client.database.saveGuildConfig(guild.id, guildConfig);

        const successEmbed = new EmbedBuilder()
            .setColor('#2ecc71')
            .setTitle('✅ Canal de Moderação Definido')
            .setDescription(`Canal de logs de moderação definido como ${channel}`)
            .setTimestamp();

        await interaction.reply({ embeds: [successEmbed] });

        // Enviar mensagem de teste no canal
        const testEmbed = new EmbedBuilder()
            .setColor('#e74c3c')
            .setTitle('🔨 Canal de Moderação Configurado')
            .setDescription('Este canal foi configurado para receber logs de moderação do Nova Moderação Bot!')
            .setTimestamp();

        await channel.send({ embeds: [testEmbed] });

        client.logger.info(`Canal de moderação definido: ${channel.name} (${channel.id}) no servidor ${guild.name}`);
    },

    /**
     * Alterna auto-moderação
     */
    async toggleAutoMod(interaction) {
        const enabled = interaction.options.getBoolean('ativo');
        const guild = interaction.guild;
        const client = interaction.client;

        // Obter configuração atual
        let guildConfig = client.database.getGuildConfig(guild.id) || {};
        guildConfig.auto_mod_enabled = enabled;

        // Salvar no banco
        client.database.saveGuildConfig(guild.id, guildConfig);

        const embed = new EmbedBuilder()
            .setColor(enabled ? '#2ecc71' : '#e74c3c')
            .setTitle(`🤖 Auto-Moderação ${enabled ? 'Ativada' : 'Desativada'}`)
            .setDescription(`A auto-moderação foi ${enabled ? 'ativada' : 'desativada'} para este servidor.`)
            .addFields({
                name: 'ℹ️ Informação',
                value: enabled 
                    ? 'O bot agora irá moderar automaticamente mensagens com conteúdo inadequado.'
                    : 'O bot não irá mais moderar mensagens automaticamente.',
                inline: false
            })
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });

        client.logger.info(`Auto-moderação ${enabled ? 'ativada' : 'desativada'} no servidor ${guild.name}`);
    },

    /**
     * Alterna anti-raid
     */
    async toggleAntiRaid(interaction) {
        const enabled = interaction.options.getBoolean('ativo');
        const guild = interaction.guild;
        const client = interaction.client;

        // Obter configuração atual
        let guildConfig = client.database.getGuildConfig(guild.id) || {};
        guildConfig.anti_raid_enabled = enabled;

        // Salvar no banco
        client.database.saveGuildConfig(guild.id, guildConfig);

        const embed = new EmbedBuilder()
            .setColor(enabled ? '#2ecc71' : '#e74c3c')
            .setTitle(`🛡️ Anti-Raid ${enabled ? 'Ativado' : 'Desativado'}`)
            .setDescription(`O sistema anti-raid foi ${enabled ? 'ativado' : 'desativado'} para este servidor.`)
            .addFields({
                name: 'ℹ️ Informação',
                value: enabled 
                    ? 'O bot agora irá detectar e prevenir raids automaticamente.'
                    : 'O bot não irá mais detectar raids automaticamente.',
                inline: false
            })
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });

        client.logger.info(`Anti-raid ${enabled ? 'ativado' : 'desativado'} no servidor ${guild.name}`);
    },

    /**
     * Alterna moderação por IA
     */
    async toggleAI(interaction) {
        const enabled = interaction.options.getBoolean('ativo');
        const guild = interaction.guild;
        const client = interaction.client;

        // Verificar se a IA está disponível
        if (!client.aiMod || !client.aiMod.enabled) {
            return await interaction.reply({
                content: '❌ Sistema de IA não está disponível! Verifique se a DEEPSEEK_API_KEY está configurada.',
                ephemeral: true
            });
        }

        // Obter configuração atual
        let guildConfig = await client.database.getGuildConfig(guild.id) || {};

        // Parse settings corretamente (pode estar com duplo stringify)
        let settingsStr = guildConfig.settings || '{}';
        if (typeof settingsStr === 'string' && settingsStr.startsWith('"')) {
            settingsStr = JSON.parse(settingsStr); // Remove o stringify extra
        }
        let settings = JSON.parse(settingsStr);

        settings.ai_moderation_enabled = enabled;
        guildConfig.settings = settings; // Não fazer stringify aqui, deixar para o banco

        // Salvar no banco
        await client.database.saveGuildConfig(guild.id, guildConfig);

        const embed = new EmbedBuilder()
            .setColor(enabled ? '#2ecc71' : '#e74c3c')
            .setTitle(`🧠 Moderação por IA ${enabled ? 'Ativada' : 'Desativada'}`)
            .setDescription(`A moderação por IA foi ${enabled ? 'ativada' : 'desativada'} para este servidor.`)
            .addFields({
                name: 'ℹ️ Informação',
                value: enabled
                    ? '🤖 O bot agora usará inteligência artificial para analisar mensagens e detectar:\n• Toxicidade e linguagem ofensiva\n• Spam e conteúdo repetitivo\n• Assédio e bullying\n• Discurso de ódio\n• Conteúdo sexual inadequado\n\n⚡ **Ações automáticas:** Advertências, timeouts e exclusão de mensagens baseadas na análise da IA.'
                    : '❌ O bot não usará mais IA para moderar mensagens automaticamente.',
                inline: false
            })
            .setTimestamp();

        if (enabled) {
            embed.addFields({
                name: '🎯 Como Funciona',
                value: '1. **Análise em Tempo Real:** Cada mensagem é analisada pela IA\n2. **Scores de Risco:** A IA atribui pontuações de 0-100% para diferentes categorias\n3. **Ações Automáticas:** Baseadas nos scores, o bot pode advertir, aplicar timeout ou deletar mensagens\n4. **Logs Detalhados:** Todas as ações são registradas com a análise da IA',
                inline: false
            });
        }

        await interaction.reply({ embeds: [embed] });

        client.logger.info(`Moderação por IA ${enabled ? 'ativada' : 'desativada'} no servidor ${guild.name}`);
    },

    /**
     * Define novo prefixo
     */
    async setPrefix(interaction) {
        const newPrefix = interaction.options.getString('novo_prefixo');
        const guild = interaction.guild;
        const client = interaction.client;

        // Validar prefixo
        if (newPrefix.length > 3) {
            return await interaction.reply({
                content: '❌ O prefixo deve ter no máximo 3 caracteres!',
                ephemeral: true
            });
        }

        // Obter configuração atual
        let guildConfig = client.database.getGuildConfig(guild.id) || {};
        const oldPrefix = guildConfig.prefix || '!';
        guildConfig.prefix = newPrefix;

        // Salvar no banco
        client.database.saveGuildConfig(guild.id, guildConfig);

        const embed = new EmbedBuilder()
            .setColor('#2ecc71')
            .setTitle('✅ Prefixo Alterado')
            .setDescription(`Prefixo alterado de \`${oldPrefix}\` para \`${newPrefix}\``)
            .addFields({
                name: 'ℹ️ Nota',
                value: 'Os comandos slash (/) continuam funcionando normalmente.',
                inline: false
            })
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });

        client.logger.info(`Prefixo alterado de "${oldPrefix}" para "${newPrefix}" no servidor ${guild.name}`);
    },

    /**
     * Reseta configurações
     */
    async resetConfig(interaction) {
        const guild = interaction.guild;
        const client = interaction.client;

        // Configuração padrão
        const defaultConfig = {
            prefix: '!',
            language: 'pt-BR',
            timezone: 'America/Sao_Paulo',
            auto_mod_enabled: true,
            anti_raid_enabled: true,
            log_channel_id: null,
            mod_log_channel_id: null,
            welcome_channel_id: null,
            mute_role_id: null,
            quarantine_role_id: null,
            mod_roles: [],
            admin_roles: [],
            ignored_channels: [],
            settings: {}
        };

        // Salvar configuração padrão
        client.database.saveGuildConfig(guild.id, defaultConfig);

        const embed = new EmbedBuilder()
            .setColor('#f39c12')
            .setTitle('🔄 Configurações Resetadas')
            .setDescription('Todas as configurações foram resetadas para os valores padrão.')
            .addFields({
                name: '⚠️ Atenção',
                value: 'Você precisará reconfigurar os canais de log e outras configurações personalizadas.',
                inline: false
            })
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });

        client.logger.info(`Configurações resetadas no servidor ${guild.name}`);
    }
};
