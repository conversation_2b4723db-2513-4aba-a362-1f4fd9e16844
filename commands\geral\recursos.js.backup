/**
 * ========================================
 * COMANDO: RECURSOS
 * Mostra todos os recursos de moderação
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('recursos')
        .setNameLocalizations({
            'pt-BR': 'recursos'
        })
        .setDescription('🛡️ Mostra todos os recursos de moderação disponíveis')
        .setDescriptionLocalizations({
            'pt-BR': 'Exibe uma lista completa de todos os recursos de moderação e segurança do bot'
        }),

    category: 'geral',
    cooldown: 10,

    async execute(interaction) {
        try {
            const client = interaction.client;
            const guild = interaction.guild;

            // Verificar quais sistemas estão ativos
            const systems = {
                autoMod: !!client.autoMod,
                aiMod: !!client.aiMod,
                antiRaid: !!client.antiRaid,
                tickets: !!client.tickets,
                analytics: !!client.analytics,
                backup: !!client.backup
            };

            // Criar embed principal
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('🛡️ Recursos de Moderação - Nodex | Moderação')
                .setDescription('**O sistema de moderação mais completo do Discord**\n\nTodos os recursos necessários para manter sua comunidade segura e organizada.')
                .setThumbnail(client.user.displayAvatarURL({ size: 256 }))
                .addFields(
                    {
                        name: '🤖 IA de Moderação Avançada',
                        value: [
                            `${systems.aiMod ? '✅' : '❌'} **Status:** ${systems.aiMod ? 'Ativo' : 'Inativo'}`,
                            '🧠 **Detecção Contextual** - Entende português brasileiro',
                            '🎯 **Análise de Toxicidade** - Identifica discurso de ódio',
                            '📝 **Moderação Progressiva** - Sistema de advertências',
                            '⚡ **Ação Instantânea** - Resposta em milissegundos'
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🚫 Sistema Anti-Raid',
                        value: [
                            `${systems.antiRaid ? '✅' : '❌'} **Status:** ${systems.antiRaid ? 'Ativo' : 'Inativo'}`,
                            '🔍 **Detecção Inteligente** - Identifica padrões suspeitos',
                            '🛡️ **Quarentena Automática** - Isola usuários suspeitos',
                            '⏰ **Análise Temporal** - Monitora velocidade de entrada',
                            '🚨 **Alertas Instantâneos** - Notifica moderadores'
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🔨 Auto-Moderação',
                        value: [
                            `${systems.autoMod ? '✅' : '❌'} **Status:** ${systems.autoMod ? 'Ativo' : 'Inativo'}`,
                            '🚫 **Filtro de Spam** - Bloqueia mensagens repetitivas',
                            '🔗 **Controle de Links** - Filtra links suspeitos',
                            '📢 **Limite de Menções** - Previne spam de menções',
                            '⚠️ **Sistema de Advertências** - Punições progressivas'
                        ].join('\n'),
                        inline: true
                    },

                    {
                        name: '📊 Analytics e Relatórios',
                        value: [
                            `${systems.analytics ? '✅' : '❌'} **Status:** ${systems.analytics ? 'Ativo' : 'Inativo'}`,
                            '📈 **Estatísticas em Tempo Real** - Dados atualizados',
                            '📋 **Relatórios Detalhados** - Análise de moderação',
                            '🎯 **Métricas de Segurança** - Nível de proteção',
                            '📅 **Histórico Completo** - Dados dos últimos 30 dias'
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '💾 Sistema de Backup',
                        value: [
                            `${systems.backup ? '✅' : '❌'} **Status:** ${systems.backup ? 'Ativo' : 'Inativo'}`,
                            '🔄 **Backup Automático** - Configurações salvas',
                            '🔒 **Criptografia** - Dados protegidos',
                            '⚡ **Restauração Rápida** - Recovery em segundos',
                            '📦 **Múltiplas Versões** - Histórico de backups'
                        ].join('\n'),
                        inline: true
                    }
                )
                .addFields({
                    name: '🌐 Dashboard Web Profissional',
                    value: [
                        '🎨 **Interface Moderna** - Design inspirado no GS Defender',
                        '⚙️ **Configuração Completa** - Todos os recursos via web',
                        '📱 **Responsivo** - Funciona em qualquer dispositivo',
                        '🔐 **Login Discord** - Autenticação segura',
                        '⚡ **Tempo Real** - Mudanças aplicadas instantaneamente',
                        '📊 **Gráficos Interativos** - Visualização de dados'
                    ].join('\n'),
                    inline: false
                })
                .setFooter({
                    text: `Nodex | Moderação v2.0.0 • ${Object.values(systems).filter(Boolean).length}/6 sistemas ativos • Solicitado por ${interaction.user.username}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            // Adicionar recomendações se nem todos os sistemas estão ativos
            const inactiveSystems = Object.entries(systems).filter(([key, value]) => !value);
            if (inactiveSystems.length > 0) {
                const recommendations = [];
                
                inactiveSystems.forEach(([system, active]) => {
                    switch(system) {
                        case 'autoMod':
                            recommendations.push('• Ativar Auto-Moderação para filtros automáticos');
                            break;
                        case 'aiMod':
                            recommendations.push('• Configurar IA de Moderação para detecção avançada');
                            break;
                        case 'antiRaid':
                            recommendations.push('• Ativar Anti-Raid para proteção contra invasões');
                            break;
                        case 'tickets':
                            recommendations.push('• Configurar Sistema de Tickets para suporte');
                            break;
                        case 'analytics':
                            recommendations.push('• Ativar Analytics para relatórios detalhados');
                            break;
                        case 'backup':
                            recommendations.push('• Ativar Backup para segurança das configurações');
                            break;
                    }
                });

                if (recommendations.length > 0) {
                    embed.addFields({
                        name: '💡 Recomendações para Maximizar a Proteção',
                        value: recommendations.join('\n'),
                        inline: false
                    });
                }
            }

            // Criar botões
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('🌐 Configurar Dashboard')
                        .setStyle(ButtonStyle.Link)
                        .setURL(process.env.DASHBOARD_URL || 'http://localhost:3000'),
                    new ButtonBuilder()
                        .setLabel('📚 Documentação')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`${process.env.DASHBOARD_URL || 'http://localhost:3000'}/docs`),
                    new ButtonBuilder()
                        .setLabel('🎧 Suporte')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`${process.env.DASHBOARD_URL || 'http://localhost:3000'}/support`)
                );

            await interaction.reply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error('Erro no comando recursos:', error);
            
            const errorMessage = '❌ Ocorreu um erro ao buscar os recursos de moderação!';
            
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
};
