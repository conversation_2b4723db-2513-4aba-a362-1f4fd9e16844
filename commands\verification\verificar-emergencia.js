const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('verificar-emergencia')
        .setDescription('🚨 Comando de emergência para verificação quando outros métodos falham')
        .addUserOption(option =>
            option.setName('usuario')
                .setDescription('Usuário para verificar manualmente (apenas moderadores)')
                .setRequired(false)
        ),

    async execute(interaction) {
        try {
            await interaction.deferReply({ ephemeral: true });

            const targetUser = interaction.options.getUser('usuario');
            const member = interaction.member;
            const guild = interaction.guild;

            // Verificar se o sistema de verificação está disponível
            if (!interaction.client.verificationSystem) {
                return await interaction.editReply({
                    content: '❌ Sistema de verificação não disponível!'
                });
            }

            // Buscar configuração de verificação
            const config = interaction.client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ? AND enabled = 1
            `).get(guild.id);

            if (!config) {
                return await interaction.editReply({
                    content: '❌ Sistema de verificação não configurado neste servidor!'
                });
            }

            // Se um usuário foi especificado, verificar se o autor tem permissões
            if (targetUser) {
                if (!member.permissions.has('ManageRoles')) {
                    return await interaction.editReply({
                        content: '❌ Você não tem permissão para verificar outros usuários!'
                    });
                }

                const targetMember = await guild.members.fetch(targetUser.id).catch(() => null);
                if (!targetMember) {
                    return await interaction.editReply({
                        content: '❌ Usuário não encontrado no servidor!'
                    });
                }

                // Verificar o usuário especificado
                await interaction.client.verificationSystem.completeVerification(
                    targetMember,
                    config,
                    'emergency_manual',
                    `verified_by_${member.user.tag}`
                );

                // Log da verificação manual
                interaction.client.database.db.prepare(`
                    INSERT INTO verification_logs
                    (guild_id, user_id, action, method, details, timestamp)
                    VALUES (?, ?, 'emergency_verification', 'manual', ?, CURRENT_TIMESTAMP)
                `).run(guild.id, targetUser.id, JSON.stringify({
                    verified_by: member.user.tag,
                    verified_by_id: member.user.id,
                    reason: 'emergency_command'
                }));

                const successEmbed = new EmbedBuilder()
                    .setTitle('✅ Verificação de Emergência Concluída')
                    .setDescription(`${targetUser} foi verificado manualmente com sucesso.`)
                    .addFields(
                        { name: '👤 Usuário Verificado', value: `${targetUser.tag}`, inline: true },
                        { name: '👮 Verificado Por', value: `${member.user.tag}`, inline: true },
                        { name: '🔧 Método', value: 'Verificação Manual de Emergência', inline: true }
                    )
                    .setColor('#00ff7f')
                    .setFooter({ text: 'Nodex | Moderação - Verificação de Emergência' })
                    .setTimestamp();

                return await interaction.editReply({ embeds: [successEmbed] });
            }

            // Auto-verificação de emergência
            console.log(`🚨 [EMERGENCY VERIFICATION] ${member.user.tag} usando verificação de emergência`);

            // Verificar se o usuário já está verificado
            const existingVerification = interaction.client.database.db.prepare(`
                SELECT verified FROM member_verification
                WHERE guild_id = ? AND user_id = ?
            `).get(guild.id, member.user.id);

            if (existingVerification?.verified) {
                return await interaction.editReply({
                    content: '✅ Você já está verificado neste servidor!'
                });
            }

            // Verificar tentativas anteriores
            const attemptCheck = await interaction.client.verificationSystem.canAttemptVerification(member, config);
            
            if (!attemptCheck.canAttempt && attemptCheck.reason === 'max_attempts_exceeded') {
                // Permitir verificação de emergência mesmo com tentativas esgotadas
                console.log(`🚨 [EMERGENCY] Permitindo verificação apesar de tentativas esgotadas`);
            }

            // Completar verificação de emergência
            await interaction.client.verificationSystem.completeVerification(
                member,
                config,
                'emergency_self',
                'emergency_command'
            );

            // Log da verificação de emergência
            interaction.client.database.db.prepare(`
                INSERT INTO verification_logs
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'emergency_verification', 'self', ?, CURRENT_TIMESTAMP)
            `).run(guild.id, member.user.id, JSON.stringify({
                reason: 'emergency_command',
                previous_attempts: attemptCheck.attempts || 0
            }));

            const emergencyEmbed = new EmbedBuilder()
                .setTitle('🚨 Verificação de Emergência Concluída')
                .setDescription('Você foi verificado usando o comando de emergência.')
                .addFields(
                    { name: '⚠️ Aviso', value: 'Este comando deve ser usado apenas quando outros métodos falham.', inline: false },
                    { name: '📞 Suporte', value: 'Se você continuar tendo problemas, contate um moderador.', inline: false },
                    { name: '🕐 Verificado em', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                )
                .setColor('#ff9500')
                .setFooter({ text: 'Nodex | Moderação - Verificação de Emergência' })
                .setTimestamp();

            await interaction.editReply({ embeds: [emergencyEmbed] });

            // Notificar moderadores sobre uso do comando de emergência
            const guildConfig = await interaction.client.database.getGuildConfig(guild.id);
            if (guildConfig?.log_channel_id) {
                const logChannel = guild.channels.cache.get(guildConfig.log_channel_id);
                if (logChannel) {
                    const modEmbed = new EmbedBuilder()
                        .setTitle('🚨 Comando de Verificação de Emergência Usado')
                        .setDescription(`${member.user} usou o comando de verificação de emergência.`)
                        .addFields(
                            { name: '👤 Usuário', value: `${member.user.tag} (${member.user.id})`, inline: true },
                            { name: '📅 Conta Criada', value: `<t:${Math.floor(member.user.createdTimestamp / 1000)}:R>`, inline: true },
                            { name: '🔍 Investigar', value: 'Verifique se há problemas no sistema de verificação.', inline: false }
                        )
                        .setColor('#ff9500')
                        .setTimestamp();

                    await logChannel.send({ embeds: [modEmbed] });
                }
            }

        } catch (error) {
            console.error('❌ [EMERGENCY VERIFICATION] Erro:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ Erro na Verificação de Emergência')
                .setDescription('Ocorreu um erro ao processar a verificação de emergência.')
                .addFields({
                    name: '📞 Próximo Passo',
                    value: 'Contate um administrador imediatamente.',
                    inline: false
                })
                .setColor('#ff4757')
                .setFooter({ text: 'Nodex | Moderação' })
                .setTimestamp();

            await interaction.editReply({ embeds: [errorEmbed] }).catch(() => {});
        }
    }
};
