/**
 * ========================================
 * COMANDO: BAN
 * Comando para banir usuários do servidor
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const EmbedStyles = require('../../utils/EmbedStyles');
const SVGIcons = require('../../utils/svgIcons');
const ms = require('ms');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ban')
        .setDescription('Banir usuário do servidor permanentemente')
        .addUserOption(option =>
            option.setName('usuario')
                .setDescription('Usuário a ser banido')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('motivo')
                .setDescription('Motivo do banimento')
                .setRequired(false))
        .addIntegerOption(option =>
            option.setName('deletar_mensagens')
                .setDescription('Quantos dias de mensagens deletar (0-7)')
                .setMinValue(0)
                .setMaxValue(7)
                .setRequired(false))
        .addBooleanOption(option =>
            option.setName('silencioso')
                .setDescription('Não enviar DM para o usuário')
                .setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.BanMembers)
        .setDMPermission(false),

    async execute(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const moderator = interaction.member;
        const targetUser = interaction.options.getUser('usuario');
        const reason = interaction.options.getString('motivo') || 'Nenhum motivo fornecido';
        const deleteMessageDays = interaction.options.getInteger('deletar_mensagens') || 1;
        const silent = interaction.options.getBoolean('silencioso') || false;

        try {
            // Verificar se o bot tem permissões
            if (!guild.members.me.permissions.has(PermissionFlagsBits.BanMembers)) {
                const errorData = {
                    title: 'Permissão Insuficiente',
                    description: 'Não tenho permissão para banir membros.\n\nPermissão necessária: Banir Membros\n\nVerifique as permissões do bot no servidor.'
                };
                const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Verificar se o usuário já está banido
            const banList = await guild.bans.fetch();
            if (banList.has(targetUser.id)) {
                const errorData = {
                    title: 'Usuário Já Banido',
                    description: `Este usuário já está banido do servidor.\n\nUsuário: ${targetUser.tag}\n\nUse /unban para remover o banimento.`
                };
                const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Tentar obter o membro (pode não estar no servidor)
            let targetMember = null;
            try {
                targetMember = await guild.members.fetch(targetUser.id);
            } catch (error) {
                // Usuário não está no servidor, mas ainda pode ser banido
            }

            // Verificações de hierarquia se o membro estiver no servidor
            if (targetMember) {
                // Verificar se não está tentando banir a si mesmo
                if (targetMember.id === moderator.id) {
                    const errorData = {
                        title: 'Ação Inválida',
                        description: 'Você não pode banir a si mesmo.\n\nEsta operação não é permitida pelo sistema.'
                    };
                    const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }

                // Verificar se não está tentando banir o bot
                if (targetMember.id === client.user.id) {
                    const errorData = {
                        title: 'Ação Inválida',
                        description: 'Não posso banir a mim mesmo.\n\nEsta operação não é permitida pelo sistema.'
                    };
                    const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }

                // Verificar hierarquia de cargos
                if (targetMember.roles.highest.position >= moderator.roles.highest.position) {
                    const errorData = {
                        title: 'Hierarquia Insuficiente',
                        description: 'Você não pode banir este usuário.\n\nMotivo: Hierarquia de cargos\n\nO cargo do usuário é igual ou superior ao seu.'
                    };
                    const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }

                // Verificar se o bot pode banir o usuário
                if (targetMember.roles.highest.position >= guild.members.me.roles.highest.position) {
                    const errorData = {
                        title: 'Hierarquia Insuficiente do Bot',
                        description: 'Não posso banir este usuário.\n\nMotivo: Hierarquia de cargos\n\nO cargo do usuário é igual ou superior ao meu.'
                    };
                    const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }

                // Verificar se o usuário é um administrador
                if (targetMember.permissions.has(PermissionFlagsBits.Administrator)) {
                    const errorData = {
                        title: 'Usuário Protegido',
                        description: 'Não posso banir um administrador.\n\nStatus: Administrador do servidor\n\nAdministradores têm proteção especial contra banimentos.'
                    };
                    const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            }

            // Responder imediatamente para evitar timeout
            await interaction.deferReply();

            // Enviar DM para o usuário (se não for silencioso e se estiver no servidor)
            let dmSent = false;
            if (!silent && targetMember) {
                try {
                    const dmData = {
                        title: 'Notificação de Banimento',
                        description: `Você foi banido permanentemente do servidor ${guild.name}.\n\nEsta é uma notificação automática do sistema de moderação.`,
                        fields: [
                            {
                                name: 'Motivo do Banimento',
                                value: reason,
                                inline: false
                            },
                            {
                                name: 'Moderador Responsável',
                                value: `${moderator.user.tag}\n${moderator.user.id}`,
                                inline: true
                            },
                            {
                                name: 'Servidor',
                                value: `${guild.name}\n${guild.id}`,
                                inline: true
                            },
                            {
                                name: 'Informações Importantes',
                                value: `Este é um banimento permanente.\n\nSuas mensagens dos últimos ${deleteMessageDays} dia(s) foram removidas.\n\nPara contestar, entre em contato com a administração.\n\nRespeite as regras em futuras participações.`,
                                inline: false
                            }
                        ]
                    };

                    const processedDmData = SVGIcons.createProfessionalEmbed(dmData);
                    const dmEmbed = new EmbedBuilder()
                        .setColor(embedStyles.colors.error)
                        .setTitle(processedDmData.title)
                        .setDescription(processedDmData.description)
                        .addFields(processedDmData.fields)
                        .setTimestamp()
                        .setFooter({ text: 'Nodex | Moderação', iconURL: guild.iconURL() })
                        .setThumbnail(guild.iconURL({ dynamic: true }));

                    await targetUser.send({ embeds: [dmEmbed] });
                    dmSent = true;
                } catch (error) {
                    client.logger.debug(`Não foi possível enviar DM para ${targetUser.tag}:`, error);
                }
            }

            // Executar o banimento
            await guild.members.ban(targetUser, {
                deleteMessageDays: deleteMessageDays,
                reason: `${reason} | Moderador: ${moderator.user.tag}`
            });

            // Registrar no banco de dados
            client.database.logModerationAction(
                guild.id, targetUser.id, moderator.id, 'ban', reason,
                null, interaction.channel.id, null,
                { deleteMessageDays, dmSent, silent }
            );

            // Atualizar estatísticas do usuário
            const userStats = client.database.db.prepare(`
                INSERT OR REPLACE INTO user_stats 
                (guild_id, user_id, bans_received, last_activity)
                VALUES (?, ?, COALESCE((SELECT bans_received FROM user_stats WHERE guild_id = ? AND user_id = ?), 0) + 1, CURRENT_TIMESTAMP)
            `).run(guild.id, targetUser.id, guild.id, targetUser.id);

            // Criar embed de confirmação
            const successData = {
                title: 'Usuário Banido com Sucesso',
                description: `O usuário foi banido permanentemente do servidor.\n\nTodas as configurações foram aplicadas conforme solicitado.`,
                fields: [
                    {
                        name: 'Usuário Banido',
                        value: `${targetUser.tag}\n${targetUser.id}`,
                        inline: true
                    },
                    {
                        name: 'Moderador',
                        value: `${moderator.user.tag}\n${moderator.user.id}`,
                        inline: true
                    },
                    {
                        name: 'Status da Notificação',
                        value: dmSent ? 'DM enviada com sucesso' : 'DM não pôde ser enviada',
                        inline: true
                    },
                    {
                        name: 'Motivo do Banimento',
                        value: reason,
                        inline: false
                    },
                    {
                        name: 'Configurações Aplicadas',
                        value: `Mensagens Deletadas: ${deleteMessageDays} dia(s)\n\nModo Silencioso: ${silent ? 'Ativado' : 'Desativado'}\n\nTipo: Banimento Permanente`,
                        inline: false
                    }
                ]
            };

            const processedSuccessData = SVGIcons.createProfessionalEmbed(successData);
            const successEmbed = new EmbedBuilder()
                .setColor(embedStyles.colors.success)
                .setTitle(processedSuccessData.title)
                .setDescription(processedSuccessData.description)
                .addFields(processedSuccessData.fields)
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação', iconURL: interaction.client.user.displayAvatarURL() });

            await interaction.editReply({ embeds: [successEmbed] });

            // Log para o canal de moderação
            await this.logToModerationChannel(guild, {
                action: 'ban',
                target: targetUser,
                moderator: moderator.user,
                reason: reason,
                deleteMessageDays: deleteMessageDays,
                dmSent: dmSent
            });

            // Log no sistema
            client.logger.moderation('ban', guild.id, targetUser.id, moderator.id, reason, {
                deleteMessageDays: deleteMessageDays,
                dmSent: dmSent
            });

        } catch (error) {
            client.logger.error('Erro no comando ban:', error, {
                guild: guild.id,
                moderator: moderator.id,
                target: targetUser.id
            });

            const errorData = {
                title: 'Erro no Sistema',
                description: 'Ocorreu um erro ao tentar banir o usuário.\n\nVerifique as permissões e tente novamente.'
            };
            const processedErrorData = SVGIcons.createProfessionalEmbed(errorData);
            const errorEmbed = new EmbedBuilder()
                .setColor('#e74c3c')
                .setTitle(processedErrorData.title)
                .setDescription(processedErrorData.description)
                .setTimestamp();

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    // Função para comandos de texto
    async executeText(message, args) {
        const client = message.client;
        const guild = message.guild;
        const moderator = message.member;

        // Verificar se há argumentos suficientes
        if (args.length < 1) {
            return await message.reply('❌ **Uso correto:** `!ban @usuário [motivo] [dias_para_deletar_mensagens]`');
        }

        // Extrair usuário, motivo e dias
        const userMention = args[0];
        let reason = 'Nenhum motivo fornecido';
        let deleteMessageDays = 1;

        // Processar argumentos
        if (args.length > 1) {
            const lastArg = args[args.length - 1];
            // Se o último argumento for um número (dias), extrair
            if (/^\d+$/.test(lastArg) && parseInt(lastArg) <= 7) {
                deleteMessageDays = parseInt(lastArg);
                reason = args.slice(1, -1).join(' ') || 'Nenhum motivo fornecido';
            } else {
                reason = args.slice(1).join(' ');
            }
        }

        // Tentar encontrar o usuário
        let targetUser;
        let targetMember;

        // Se for uma menção
        if (userMention.startsWith('<@') && userMention.endsWith('>')) {
            const userId = userMention.slice(2, -1).replace('!', '');
            targetMember = guild.members.cache.get(userId);
            targetUser = targetMember?.user || await client.users.fetch(userId).catch(() => null);
        }
        // Se for um ID
        else if (/^\d+$/.test(userMention)) {
            targetMember = guild.members.cache.get(userMention);
            targetUser = targetMember?.user || await client.users.fetch(userMention).catch(() => null);
        }
        // Se for um nome de usuário
        else {
            targetMember = guild.members.cache.find(member =>
                member.user.username.toLowerCase().includes(userMention.toLowerCase()) ||
                member.displayName.toLowerCase().includes(userMention.toLowerCase())
            );
            targetUser = targetMember?.user;
        }

        if (!targetUser) {
            return await message.reply('❌ Usuário não encontrado! Use `!ban @usuário [motivo] [dias]`');
        }

        // Verificações de segurança
        if (targetUser.id === message.author.id) {
            return await message.reply('❌ Você não pode se banir!');
        }

        if (targetUser.id === client.user.id) {
            return await message.reply('❌ Não posso me banir!');
        }

        // Verificar permissões
        if (!moderator.permissions.has('BanMembers')) {
            return await message.reply('❌ Você não tem permissão para banir usuários!');
        }

        // Verificar hierarquia (se o usuário ainda estiver no servidor)
        if (targetMember) {
            if (moderator.roles.highest.position <= targetMember.roles.highest.position) {
                return await message.reply('❌ Você não pode banir este usuário! (Hierarquia de cargos)');
            }

            if (guild.members.me.roles.highest.position <= targetMember.roles.highest.position) {
                return await message.reply('❌ Não posso banir este usuário! (Hierarquia de cargos)');
            }

            if (!targetMember.bannable) {
                return await message.reply('❌ Este usuário não pode ser banido!');
            }
        }

        try {
            // Tentar enviar DM para o usuário
            let dmSent = false;
            try {
                const dmData = {
                    title: 'Notificação de Banimento',
                    description: `Você foi banido do servidor ${guild.name}`,
                    fields: [
                        { name: 'Motivo', value: reason, inline: false },
                        { name: 'Moderador', value: moderator.user.tag, inline: true },
                        { name: 'Data', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                    ]
                };
                const processedDmData = SVGIcons.createProfessionalEmbed(dmData);
                const dmEmbed = new EmbedBuilder()
                    .setColor('#e74c3c')
                    .setTitle(processedDmData.title)
                    .setDescription(processedDmData.description)
                    .addFields(processedDmData.fields)
                    .setThumbnail(guild.iconURL())
                    .setTimestamp()
                    .setFooter({ text: 'Nodex | Moderação' });

                await targetUser.send({ embeds: [dmEmbed] });
                dmSent = true;
            } catch (error) {
                // Usuário não pode receber DM
            }

            // Banir o usuário
            await guild.members.ban(targetUser, {
                reason: reason,
                deleteMessageDays: deleteMessageDays
            });

            // Resposta de sucesso
            await message.reply(`🔨 **${targetUser.tag}** foi banido do servidor!\n📋 **Motivo:** ${reason}\n🗑️ **Mensagens deletadas:** ${deleteMessageDays} dia(s)\n📨 **DM enviada:** ${dmSent ? '✅ Sim' : '❌ Não'}`);

            // Log no banco de dados
            client.database.logModerationAction(
                guild.id, targetUser.id, moderator.id, 'ban', reason, null, null, null,
                { automatic: false, deleteMessageDays: deleteMessageDays, dmSent: dmSent }
            );

            // Log para o canal de moderação
            await this.logToModerationChannel(guild, {
                action: 'ban',
                target: targetUser,
                moderator: moderator.user,
                reason: reason,
                deleteMessageDays: deleteMessageDays,
                dmSent: dmSent
            });

            client.logger.info(`Usuário ${targetUser.tag} banido por ${moderator.user.tag}: ${reason}`);

        } catch (error) {
            client.logger.error('Erro no comando ban (texto):', error);
            await message.reply('❌ Ocorreu um erro ao banir o usuário.');
        }
    },

    /**
     * Envia log para o canal de moderação
     */
    async logToModerationChannel(guild, data) {
        try {
            const guildConfig = guild.client.database.getGuildConfig(guild.id);
            if (!guildConfig || !guildConfig.mod_log_channel_id) return;

            const logChannel = guild.channels.cache.get(guildConfig.mod_log_channel_id);
            if (!logChannel) return;

            const logData = {
                title: 'Usuário Banido',
                fields: [
                    { name: 'Usuário', value: `${data.target.tag} (${data.target.id})`, inline: true },
                    { name: 'Moderador', value: `${data.moderator.tag} (${data.moderator.id})`, inline: true },
                    { name: 'Motivo', value: data.reason, inline: false },
                    { name: 'Mensagens Deletadas', value: `${data.deleteMessageDays} dia(s)`, inline: true },
                    { name: 'DM Enviada', value: data.dmSent ? 'Sim' : 'Não', inline: true }
                ]
            };

            const processedLogData = SVGIcons.createProfessionalEmbed(logData);
            const logEmbed = new EmbedBuilder()
                .setColor('#e74c3c')
                .setTitle(processedLogData.title)
                .addFields(processedLogData.fields)
                .setThumbnail(data.target.displayAvatarURL())
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação' });

            await logChannel.send({ embeds: [logEmbed] });

        } catch (error) {
            guild.client.logger.error('Erro ao enviar log de banimento:', error);
        }
    }
};
