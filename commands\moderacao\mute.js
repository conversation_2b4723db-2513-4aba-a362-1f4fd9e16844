/**
 * ========================================
 * PREMIUM MUTE COMMAND SYSTEM
 * Advanced timeout/mute with premium styling
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const EmbedStyles = require('../../utils/EmbedStyles');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('mute')
        .setDescription('🔇 Aplicar timeout/mute em um usuário')
        .addUserOption(option =>
            option.setName('usuário')
                .setDescription('Usuário para aplicar o mute')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('duração')
                .setDescription('Duração do mute (ex: 10m, 1h, 1d)')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('motivo')
                .setDescription('Motivo do mute')
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('silencioso')
                .setDescription('Não enviar DM para o usuário')
                .setRequired(false)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .setDMPermission(false),

    async execute(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const moderator = interaction.member;
        const target = interaction.options.getMember('usuário');
        const duration = interaction.options.getString('duração') || '10m';
        const reason = interaction.options.getString('motivo') || 'Nenhum motivo fornecido';
        const silent = interaction.options.getBoolean('silencioso') || false;

        try {
            // Verificações de segurança com embeds premium
            if (!target) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Usuário Não Encontrado',
                    `${embedStyles.format.bold('O usuário especificado não foi encontrado no servidor!')}\n\n${embedStyles.icons.info} **Dica:** Verifique se o usuário ainda está no servidor.`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (target.id === interaction.user.id) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Ação Inválida',
                    `${embedStyles.format.bold('Você não pode mutar a si mesmo!')}\n\n${embedStyles.format.italic('Isso seria um pouco contraproducente, não acha?')} 😄`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (target.id === client.user.id) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Ação Inválida',
                    `${embedStyles.format.bold('Não posso mutar a mim mesmo!')}\n\n${embedStyles.format.italic('Isso seria um silêncio digital!')} 🤖`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Verificar hierarquia
            if (moderator.roles.highest.position <= target.roles.highest.position) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Hierarquia Insuficiente',
                    `${embedStyles.format.bold('Você não pode mutar este usuário!')}\n\n${embedStyles.icons.crown} **Motivo:** Hierarquia de cargos\n${embedStyles.format.italic('O cargo do usuário é igual ou superior ao seu.')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (guild.members.me.roles.highest.position <= target.roles.highest.position) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Hierarquia Insuficiente do Bot',
                    `${embedStyles.format.bold('Não posso mutar este usuário!')}\n\n${embedStyles.icons.crown} **Motivo:** Hierarquia de cargos\n${embedStyles.format.italic('O cargo do usuário é igual ou superior ao meu.')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Verificar se o usuário já está mutado
            if (target.communicationDisabledUntil && target.communicationDisabledUntil > new Date()) {
                const errorEmbed = embedStyles.createWarningEmbed(
                    'Usuário Já Mutado',
                    `${embedStyles.format.bold('Este usuário já está mutado!')}\n\n${embedStyles.icons.info} **Mute expira em:** ${embedStyles.format.timestampRelative(target.communicationDisabledUntil)}\n\n${embedStyles.format.italic('Use /unmute para remover o mute atual.')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Converter duração para milissegundos
            const durationMs = this.parseDuration(duration);
            if (!durationMs || durationMs > 28 * 24 * 60 * 60 * 1000) { // Max 28 dias
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Duração Inválida',
                    `${embedStyles.format.bold('Duração inválida ou muito longa!')}\n\n${embedStyles.icons.info} **Formatos aceitos:**\n• ${embedStyles.format.code('10m')} - 10 minutos\n• ${embedStyles.format.code('2h')} - 2 horas\n• ${embedStyles.format.code('1d')} - 1 dia\n• ${embedStyles.format.code('7d')} - 7 dias\n\n${embedStyles.format.italic('Máximo: 28 dias')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            await interaction.deferReply();

            let dmSent = false;

            // Tentar enviar DM premium para o usuário
            if (!silent) {
                try {
                    const expiresAt = new Date(Date.now() + durationMs);
                    const dmEmbed = {
                        color: parseInt(embedStyles.colors.warning.replace('#', ''), 16),
                        title: `${embedStyles.icons.timeout} ${embedStyles.format.bold('Você foi mutado!')}`,
                        description: `**Você foi mutado no servidor ${embedStyles.format.bold(guild.name)}**\n\n${embedStyles.format.italic('Esta é uma notificação automática do sistema de moderação.')}`,
                        fields: [
                            {
                                name: `${embedStyles.icons.info} **Motivo do Mute**`,
                                value: embedStyles.format.code(reason),
                                inline: false
                            },
                            {
                                name: `${embedStyles.icons.shield} **Moderador Responsável**`,
                                value: `${moderator.user.tag}\n${embedStyles.format.code(moderator.user.id)}`,
                                inline: true
                            },
                            {
                                name: `${embedStyles.icons.info} **Duração**`,
                                value: `${embedStyles.formatDuration(durationMs)}\n${embedStyles.format.italic('Expira em:')} ${embedStyles.format.timestamp(expiresAt)}`,
                                inline: true
                            },
                            {
                                name: `${embedStyles.icons.info} **Informações Importantes**`,
                                value: `• Você não poderá enviar mensagens durante o mute\n• O mute será removido automaticamente\n• Entre em contato com a moderação se tiver dúvidas\n• Respeite as regras para evitar punições futuras`,
                                inline: false
                            }
                        ],
                        timestamp: new Date().toISOString(),
                        footer: {
                            text: 'Nodex | Moderação • Sistema de Mute Premium',
                            icon_url: guild.iconURL()
                        },
                        thumbnail: {
                            url: guild.iconURL({ dynamic: true })
                        }
                    };

                    await target.send({ embeds: [dmEmbed] });
                    dmSent = true;
                } catch (error) {
                    // Usuário não pode receber DM
                }
            }

            // Aplicar timeout
            await target.timeout(durationMs, reason);

            // Resposta de sucesso premium
            const expiresAt = new Date(Date.now() + durationMs);
            const successFields = [
                {
                    name: `${embedStyles.icons.user} **Usuário Mutado**`,
                    value: `${target.user.tag}\n${embedStyles.format.code(target.user.id)}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.shield} **Moderador**`,
                    value: `${moderator.user.tag}\n${embedStyles.format.code(moderator.user.id)}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.info} **Status da Notificação**`,
                    value: dmSent ? `${embedStyles.icons.success} DM enviada` : `${embedStyles.icons.error} DM não enviada`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.info} **Duração do Mute**`,
                    value: `${embedStyles.format.bold('Duração:')} ${embedStyles.formatDuration(durationMs)}\n${embedStyles.format.bold('Expira em:')} ${embedStyles.format.timestamp(expiresAt)}\n${embedStyles.format.bold('Expira:')} ${embedStyles.format.timestampRelative(expiresAt)}`,
                    inline: false
                },
                {
                    name: `${embedStyles.icons.info} **Motivo do Mute**`,
                    value: embedStyles.format.code(reason),
                    inline: false
                }
            ];

            const successEmbed = embedStyles.createModerationEmbed('timeout', target.user, moderator.user, reason, successFields);
            
            await interaction.editReply({ embeds: [successEmbed] });

            // Log no banco de dados
            client.database.logModerationAction(
                guild.id, target.id, moderator.user.id, 'timeout', reason, durationMs, null, null,
                { automatic: false, dmSent: dmSent, expiresAt: expiresAt.toISOString() }
            );

            // Log no canal de logs se configurado
            await this.logToModerationChannel(guild, {
                action: 'timeout',
                target: target.user,
                moderator: moderator.user,
                reason: reason,
                duration: durationMs,
                expiresAt: expiresAt,
                dmSent: dmSent
            });

            client.logger.info(`Usuário ${target.user.tag} mutado por ${moderator.user.tag} por ${embedStyles.formatDuration(durationMs)}: ${reason}`);

        } catch (error) {
            client.logger.error('Erro no comando mute:', error);
            
            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                `${embedStyles.format.bold('Ocorreu um erro ao aplicar o mute.')}\n\n${embedStyles.icons.info} **Detalhes:**\n• Verifique as permissões do bot\n• Tente novamente em alguns segundos\n• Contate o suporte se o problema persistir\n\n${embedStyles.format.italic('Erro técnico: ' + error.message)}`
            );
            
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    // Função para converter duração em string para milissegundos
    parseDuration(duration) {
        const regex = /^(\d+)([smhd])$/i;
        const match = duration.match(regex);
        
        if (!match) return null;
        
        const value = parseInt(match[1]);
        const unit = match[2].toLowerCase();
        
        const multipliers = {
            's': 1000,
            'm': 60 * 1000,
            'h': 60 * 60 * 1000,
            'd': 24 * 60 * 60 * 1000
        };
        
        return value * multipliers[unit];
    },

    // Função para log no canal de moderação
    async logToModerationChannel(guild, data) {
        try {
            const embedStyles = new EmbedStyles();
            const guildConfig = guild.client.database.getGuildConfig(guild.id);
            
            if (!guildConfig?.log_channel) return;
            
            const logChannel = guild.channels.cache.get(guildConfig.log_channel);
            if (!logChannel) return;

            const logEmbed = {
                color: parseInt(embedStyles.colors.warning.replace('#', ''), 16),
                title: `${embedStyles.icons.timeout} ${embedStyles.format.bold('Usuário Mutado')}`,
                description: `**Ação de moderação executada no servidor**\n${embedStyles.format.italic('Log automático do sistema de moderação')}`,
                fields: [
                    {
                        name: `${embedStyles.icons.user} **Usuário Mutado**`,
                        value: `${data.target.tag}\n${embedStyles.format.code(data.target.id)}`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.shield} **Moderador**`,
                        value: `${data.moderator.tag}\n${embedStyles.format.code(data.moderator.id)}`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.info} **Status da DM**`,
                        value: data.dmSent ? `${embedStyles.icons.success} Enviada` : `${embedStyles.icons.error} Não enviada`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.info} **Duração**`,
                        value: `${embedStyles.formatDuration(data.duration)}\n${embedStyles.format.bold('Expira:')} ${embedStyles.format.timestampRelative(data.expiresAt)}`,
                        inline: false
                    },
                    {
                        name: `${embedStyles.icons.info} **Motivo do Mute**`,
                        value: embedStyles.format.code(data.reason),
                        inline: false
                    }
                ],
                timestamp: new Date().toISOString(),
                footer: {
                    text: 'Nodex | Moderação • Sistema de Logs Premium',
                    icon_url: guild.iconURL()
                },
                thumbnail: {
                    url: data.target.displayAvatarURL({ dynamic: true })
                }
            };

            await logChannel.send({ embeds: [logEmbed] });
        } catch (error) {
            guild.client.logger.error('Erro ao enviar log de mute:', error);
        }
    },

    // Função para comandos de texto
    executeText: async function(message, args) {
        // Verificar argumentos básicos
        if (args.length === 0) {
            return await message.reply('**Uso:** `!mute @usuário [duração] [motivo]`');
        }

        // Simular interaction para compatibilidade
        const fakeInteraction = {
            guild: message.guild,
            member: message.member,
            user: message.author,
            channel: message.channel,
            client: message.client,
            options: {
                getMember: () => {
                    const mention = args[0];
                    if (!mention) return null;

                    if (mention.startsWith('<@') && mention.endsWith('>')) {
                        const userId = mention.slice(2, -1).replace('!', '');
                        return message.guild.members.cache.get(userId);
                    }

                    if (/^\d+$/.test(mention)) {
                        return message.guild.members.cache.get(mention);
                    }

                    return message.guild.members.cache.find(member =>
                        member.user.username.toLowerCase().includes(mention.toLowerCase()) ||
                        member.displayName.toLowerCase().includes(mention.toLowerCase())
                    );
                },
                getString: (name) => {
                    if (name === 'duração') return args[1] || '10m';
                    if (name === 'motivo') return args.slice(2).join(' ') || 'Nenhum motivo fornecido';
                    return null;
                },
                getBoolean: () => false
            },
            reply: async (content) => await message.reply(content),
            deferReply: async () => Promise.resolve(),
            editReply: async (content) => await message.reply(content),
            deferred: false
        };

        return await this.execute(fakeInteraction);
    },

    cooldown: 3,
    category: 'moderacao',
    examples: [
        '/mute usuário:@usuário duração:10m motivo:Spam',
        '/mute usuário:@usuário duração:1h',
        '/mute usuário:@usuário duração:1d motivo:Comportamento inadequado silencioso:true'
    ]
};
