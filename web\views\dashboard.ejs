<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .navbar {
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 255, 127, 0.1);
            padding: 1rem 2rem;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: #00ff7f;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
        }

        .nav-link {
            color: #b0b0b0;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: #00ff7f;
            background: rgba(0, 255, 127, 0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: rgba(0, 255, 127, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            border: 1px solid rgba(0, 255, 127, 0.2);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid rgba(0, 255, 127, 0.3);
        }

        .main-content {
            margin-top: 80px;
            padding: 2rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .dashboard-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(90deg, #ffffff, #00ff7f);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .dashboard-subtitle {
            color: #b0b0b0;
            font-size: 1.1rem;
        }

        .servers-section {
            background: rgba(20, 20, 20, 0.7);
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid rgba(0, 255, 127, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .servers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .server-card {
            background: rgba(30, 30, 30, 0.8);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(0, 255, 127, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .server-card:hover {
            transform: translateY(-4px);
            border-color: rgba(0, 255, 127, 0.3);
            box-shadow: 0 8px 25px rgba(0, 255, 127, 0.2);
        }

        .server-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .server-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: 2px solid rgba(0, 255, 127, 0.3);
        }

        .server-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ffffff;
            margin: 0;
        }

        .server-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(0, 255, 127, 0.2);
            color: #00ff7f;
            border: 1px solid rgba(0, 255, 127, 0.3);
        }

        .status-inactive {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        .no-servers {
            text-align: center;
            padding: 3rem;
            color: #b0b0b0;
        }

        .no-servers i {
            font-size: 3rem;
            color: #00ff7f;
            margin-bottom: 1rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #00ff7f 0%, #00cc66 100%);
            color: #000000;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 127, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #00ff7f;
            border: 2px solid #00ff7f;
        }

        .btn-secondary:hover {
            background: #00ff7f;
            color: #000000;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="nav-brand">
            <img src="https://cdn.discordapp.com/icons/1381755403326455838/a401a3f3c833d228490b9f4bda8e9ca3.png" alt="Nodex | Community" class="server-avatar">
            <span><%= botName %></span>
        </div>
        <div class="nav-links">
            <a href="/" class="nav-link">Início</a>
            <a href="/dashboard" class="nav-link active">Dashboard</a>
            <a href="/docs" class="nav-link">Documentação</a>
            <a href="/support" class="nav-link">Suporte</a>
        </div>
        <div class="nav-actions">
            <% if (user) { %>
            <div class="user-info">
                <% if (user.avatar) { %>
                <img src="https://cdn.discordapp.com/avatars/<%= user.id %>/<%= user.avatar %>.png" 
                     alt="Avatar" class="user-avatar">
                <% } else { %>
                <div class="user-avatar" style="background: #00ff7f; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-user" style="color: #000;"></i>
                </div>
                <% } %>
                <span><%= user.username %></span>
                <a href="/logout" style="color: #b0b0b0; margin-left: 0.5rem;">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
            <% } else { %>
            <a href="/login" class="btn btn-primary">
                <i class="fab fa-discord"></i>
                Entrar
            </a>
            <% } %>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="dashboard-header">
            <h1 class="dashboard-title">Dashboard de Configuração</h1>
            <p class="dashboard-subtitle">Gerencie o bot em seus servidores</p>
        </div>

        <div class="servers-section">
            <h2 class="section-title">
                <i class="fas fa-server"></i>
                Seus Servidores
            </h2>

            <% if (guilds && guilds.length > 0) { %>
            <div class="servers-grid">
                <% guilds.forEach(function(guild) { %>
                <div class="server-card">
                    <div class="server-header">
                        <img src="<%= guild.iconURL %>" alt="<%= guild.name %>" class="server-icon">
                        <div>
                            <h3 class="server-name"><%= guild.name %></h3>
                            <div class="server-status">
                                <span class="status-badge <%= guild.botInGuild ? 'status-active' : 'status-inactive' %>">
                                    <% if (guild.botInGuild) { %>
                                        <i class="fas fa-check"></i> Bot Ativo
                                    <% } else { %>
                                        <i class="fas fa-times"></i> Bot Ausente
                                    <% } %>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <% if (guild.botInGuild) { %>
                    <a href="/dashboard/guild/<%= guild.id %>" class="btn btn-primary">
                        <i class="fas fa-cog"></i>
                        Configurar
                    </a>
                    <a href="/dashboard/guild/<%= guild.id %>/backup" class="btn btn-secondary">
                        <i class="fas fa-save"></i>
                        Backup
                    </a>
                    <% } else { %>
                    <a href="https://discord.com/oauth2/authorize?client_id=<%= client.user.id %>&scope=bot&permissions=8&guild_id=<%= guild.id %>" 
                       class="btn btn-secondary" target="_blank">
                        <i class="fas fa-plus"></i>
                        Convidar Bot
                    </a>
                    <% } %>
                </div>
                <% }); %>
            </div>
            <% } else { %>
            <div class="no-servers">
                <i class="fas fa-info-circle"></i>
                <h3>Nenhum Servidor Encontrado</h3>
                <p>Você não tem permissão de administrador em servidores onde o bot está presente.</p>
                <a href="https://discord.com/oauth2/authorize?client_id=<%= client.user.id %>&scope=bot&permissions=8" 
                   class="btn btn-primary" target="_blank">
                    <i class="fas fa-plus"></i>
                    Convidar Bot para um Servidor
                </a>
            </div>
            <% } %>
        </div>
    </main>

    <script>
        console.log('✅ Dashboard carregado com sucesso');
        console.log('📊 Dados recebidos:', {
            user: '<%= user ? user.username : "null" %>',
            guilds: <%= guilds ? guilds.length : 0 %>,
            botName: '<%= botName %>'
        });
    </script>
</body>
</html>