/**
 * ========================================
 * DASHBOARD AVANÇADO - CSS SUPREMO
 * Design moderno, animações fluidas e UX perfeita
 * ========================================
 */

/* ===== LAYOUT PRINCIPAL ===== */
.dashboard-main {
    display: flex;
    min-height: 100vh;
    padding-top: 80px;
    background: var(--gradient-background);
}

.dashboard-sidebar {
    width: 280px;
    background: var(--gradient-card);
    border-right: 1px solid rgba(0, 255, 127, 0.1);
    padding: 2rem 0;
    position: fixed;
    height: calc(100vh - 80px);
    overflow-y: auto;
    z-index: 100;
    transition: var(--transition-medium);
    /* Garantir que todos os itens sejam visíveis */
    min-height: 600px;
}

.dashboard-content {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    max-width: calc(100vw - 280px);
}

/* ===== SIDEBAR ===== */
.sidebar-header {
    padding: 0 2rem 2rem;
    border-bottom: 1px solid rgba(0, 255, 127, 0.1);
    margin-bottom: 2rem;
}

.sidebar-header h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.sidebar-header p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.sidebar-nav {
    padding: 0 1rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition-medium);
    z-index: -1;
}

.nav-item:hover::before,
.nav-item.active::before {
    width: 100%;
}

.nav-item:hover,
.nav-item.active {
    color: var(--black);
    transform: translateX(5px);
}

.nav-item i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Link externo para auto-roles */
.nav-external {
    position: relative;
    background: linear-gradient(135deg, rgba(0, 255, 127, 0.1), rgba(0, 255, 127, 0.05));
    border: 1px solid rgba(0, 255, 127, 0.2);
}

.nav-external:hover {
    background: linear-gradient(135deg, rgba(0, 255, 127, 0.2), rgba(0, 255, 127, 0.1));
    border-color: rgba(0, 255, 127, 0.4);
}

.nav-external-icon {
    font-size: 0.8rem !important;
    margin-left: auto;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.nav-external:hover .nav-external-icon {
    opacity: 1;
    transform: translateX(2px);
}

/* ========================================
   VERIFICAÇÃO STYLES - REDESIGNED
   ======================================== */

/* Verification Module Container */
.verification-module-container {
    max-width: 1200px;
    margin: 0 auto;
}

.verification-main-card {
    background: var(--gradient-card);
    border: 2px solid rgba(0, 255, 127, 0.3);
    border-radius: var(--border-radius-large);
    padding: 0;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.verification-main-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    border-color: rgba(0, 255, 127, 0.5);
}

/* Verification Header */
.verification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(0, 255, 127, 0.1) 0%, rgba(13, 77, 32, 0.1) 100%);
    border-bottom: 1px solid rgba(0, 255, 127, 0.2);
}

.verification-title-section h3.verification-main-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.verification-title-section h3.verification-main-title i {
    color: var(--primary-green);
    font-size: 2rem;
    text-shadow: 0 0 10px rgba(0, 255, 127, 0.5);
}

.verification-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
}

/* Enhanced Toggle */
.verification-master-toggle {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.toggle-enhanced {
    width: 80px;
    height: 40px;
}

.toggle-slider-enhanced {
    border-radius: 40px;
    background: var(--medium-gray);
    position: relative;
}

.toggle-slider-enhanced:before {
    height: 32px;
    width: 32px;
    left: 4px;
    bottom: 4px;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.toggle-enhanced input:checked + .toggle-slider-enhanced {
    background: var(--primary-green);
    box-shadow: 0 0 20px rgba(0, 255, 127, 0.4);
}

.toggle-enhanced input:checked + .toggle-slider-enhanced:before {
    transform: translateX(40px);
}

.toggle-status {
    text-align: right;
}

/* Verification Stats Section */
.verification-stats-section {
    padding: 2rem;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(0, 255, 127, 0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 255, 127, 0.05) 0%, rgba(13, 77, 32, 0.05) 100%);
    border: 1px solid rgba(0, 255, 127, 0.2);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    border-color: rgba(0, 255, 127, 0.4);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.stat-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-green);
    border-radius: 50%;
    color: var(--black);
    font-size: 1.5rem;
    box-shadow: 0 0 15px rgba(0, 255, 127, 0.3);
}

.stat-content {
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-green);
    text-shadow: 0 0 10px rgba(0, 255, 127, 0.3);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Tab Navigation */
.verification-config-tabs {
    padding: 2rem;
}

.tab-navigation {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid rgba(0, 255, 127, 0.1);
    padding-bottom: 1rem;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: transparent;
    border: 1px solid rgba(0, 255, 127, 0.2);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 127, 0.1), transparent);
    transition: left 0.5s ease;
}

.tab-btn:hover::before {
    left: 100%;
}

.tab-btn:hover,
.tab-btn.active {
    background: linear-gradient(135deg, rgba(0, 255, 127, 0.1) 0%, rgba(13, 77, 32, 0.1) 100%);
    border-color: rgba(0, 255, 127, 0.4);
    color: var(--primary-green);
    transform: translateY(-2px);
}

.tab-btn.active {
    box-shadow: 0 4px 15px rgba(0, 255, 127, 0.2);
}

.tab-btn i {
    font-size: 1.1rem;
}

/* Tab Content */
.tab-content {
    display: none;
    animation: fadeInUp 0.4s ease-out;
}

.tab-content.active {
    display: block;
}

.config-section-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.config-group {
    background: rgba(26, 26, 26, 0.6);
    border: 1px solid rgba(0, 255, 127, 0.1);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.config-group:hover {
    border-color: rgba(0, 255, 127, 0.3);
    transform: translateY(-1px);
}

.config-group.full-width {
    grid-column: 1 / -1;
}

.config-group-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(0, 255, 127, 0.2);
}

.config-group-title i {
    color: var(--primary-green);
    font-size: 1.3rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: var(--success-color);
    box-shadow: 0 0 10px rgba(0, 255, 127, 0.5);
}

.status-dot.inactive {
    background: var(--text-secondary);
}

.status-text {
    font-weight: 600;
    color: var(--text-primary);
}

/* Full Width Cards */
.config-card.full-width {
    grid-column: 1 / -1;
}

/* Trigger Value Group */
#triggerValueGroup {
    transition: all 0.3s ease;
}

#triggerValueGroup.show {
    display: block !important;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .system-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .auto-role-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .auto-role-actions {
        width: 100%;
        justify-content: space-between;
    }
}

/* ===== CONTENT HEADER ===== */
.content-header {
    margin-bottom: 3rem;
    text-align: center;
    position: relative;
}

.content-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Shield Icon Styling */
.shield-icon {
    color: var(--primary-green);
}

.content-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.save-button-container {
    position: sticky;
    top: 100px;
    z-index: 50;
    margin-bottom: 2rem;
}

.btn-save {
    background: var(--gradient-primary);
    color: var(--black);
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius-large);
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition-medium);
    box-shadow: var(--shadow-medium);
    position: relative;
    overflow: hidden;
}

.btn-save::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition-medium);
}

.btn-save:hover::before {
    width: 300px;
    height: 300px;
}

.btn-save:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-large);
}

.btn-save:active {
    transform: translateY(-1px);
}

/* ===== SEÇÕES DE CONFIGURAÇÃO ===== */
.config-section {
    display: none;
    animation: fadeInUp 0.6s ease-out;
}

.config-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
    text-align: center;
}

.section-header h2 {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.section-header h2 i {
    color: var(--primary-green);
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* ===== GRID DE CONFIGURAÇÕES ===== */
.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.config-card {
    background: var(--gradient-card);
    border: 1px solid rgba(0, 255, 127, 0.1);
    border-radius: var(--border-radius-large);
    padding: 2rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.config-card:hover {
    transform: translateY(-2px);
    border-color: rgba(0, 255, 127, 0.3);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.config-card.featured {
    border: 2px solid rgba(0, 255, 127, 0.3);
    background: linear-gradient(135deg, 
        rgba(0, 255, 127, 0.05) 0%, 
        rgba(13, 77, 32, 0.1) 100%);
}

.config-header {
    margin-bottom: 1.5rem;
}

.config-title {
    font-size: 1.3rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.config-description {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
}

/* ===== CONTROLES DE FORMULÁRIO ===== */
.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.input-group input,
.input-group select,
.input-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--dark-gray);
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: var(--transition-fast);
}

.input-group input:focus,
.input-group select:focus,
.input-group textarea:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(0, 255, 127, 0.1);
}

.input-group small {
    display: block;
    color: var(--text-muted);
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

/* ===== TOGGLE SWITCHES ===== */
.toggle-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.toggle {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--medium-gray);
    transition: var(--transition-fast);
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background: white;
    transition: var(--transition-fast);
    border-radius: 50%;
}

.toggle input:checked + .toggle-slider {
    background: var(--primary-green);
}

.toggle input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-label {
    color: var(--text-primary);
    font-weight: 500;
}

/* ===== FEATURES E LISTAS ===== */
.feature-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.feature-item {
    background: rgba(0, 255, 127, 0.1);
    color: var(--primary-green);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-small);
    font-size: 0.85rem;
    border: 1px solid rgba(0, 255, 127, 0.2);
}

.ai-features,
.raid-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.ai-feature,
.raid-feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(0, 255, 127, 0.05);
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 255, 127, 0.1);
}

.ai-feature i,
.raid-feature i {
    color: var(--primary-green);
    font-size: 1.1rem;
}

/* ===== SLIDERS ===== */
.sensitivity-slider {
    margin: 1rem 0;
}

.sensitivity-slider input[type="range"] {
    width: 100%;
    height: 8px;
    border-radius: 5px;
    background: var(--medium-gray);
    outline: none;
    -webkit-appearance: none;
}

.sensitivity-slider input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-green);
    cursor: pointer;
}

.sensitivity-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.85rem;
}

.sensitivity-description {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(0, 255, 127, 0.05);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-green);
}

/* ===== COMANDOS ===== */
.command-list {
    space-y: 0.75rem;
}

.command-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--medium-gray);
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 255, 127, 0.1);
    transition: var(--transition-fast);
}

.command-item:hover {
    border-color: rgba(0, 255, 127, 0.3);
    background: rgba(0, 255, 127, 0.05);
}

.command-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.command-name {
    font-family: 'Courier New', monospace;
    color: var(--primary-green);
    font-weight: 600;
}

.command-desc {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== RESPONSIVO ===== */
@media (max-width: 1024px) {
    .dashboard-sidebar {
        transform: translateX(-100%);
    }
    
    .dashboard-sidebar.open {
        transform: translateX(0);
    }
    
    .dashboard-content {
        margin-left: 0;
        max-width: 100vw;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .content-header h1 {
        font-size: 2rem;
    }
    
    .config-card {
        padding: 1.5rem;
    }
    
    .section-header h2 {
        font-size: 1.5rem;
    }
}

/* ===== ANIMAÇÕES AVANÇADAS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 0 0 0 rgba(0, 255, 127, 0.4);
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
        box-shadow: 0 0 0 10px rgba(0, 255, 127, 0);
    }
    100% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 0 0 0 rgba(0, 255, 127, 0);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(0, 255, 127, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 255, 127, 0.6), 0 0 30px rgba(0, 255, 127, 0.3);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px) rotate(-1deg); }
    75% { transform: translateX(5px) rotate(1deg); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(1deg); }
    50% { transform: translateY(-20px) rotate(0deg); }
    75% { transform: translateY(-10px) rotate(-1deg); }
}



@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== LOADING E TOASTS ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: var(--text-primary);
}

.loading-spinner i {
    font-size: 3rem;
    color: var(--primary-green);
    margin-bottom: 1rem;
}

.toast {
    position: fixed;
    top: 100px;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 500;
    transform: translateX(400px);
    transition: var(--transition-medium);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    background: linear-gradient(135deg, #00ff7f, #0d4d20);
}

.toast-error {
    background: linear-gradient(135deg, #ff4757, #c44569);
}

/* ========================================
   ESTILOS PARA NOVOS MÓDULOS DO DASHBOARD
   ======================================== */

/* Comandos */
.command-toggles {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.command-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 127, 0.2);
    transition: var(--transition-fast);
}

.command-toggle:hover {
    background: rgba(0, 255, 127, 0.1);
    border-color: rgba(0, 255, 127, 0.4);
    transform: translateX(4px);
}

.command-name {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #00ff7f;
    min-width: 80px;
}

.command-desc {
    color: var(--text-secondary);
    font-size: 0.9em;
}

/* Analytics */
.analytics-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 16px;
}

.analytics-feature {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(0, 255, 127, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 127, 0.3);
    transition: var(--transition-fast);
}

.analytics-feature:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.analytics-feature i {
    color: #00ff7f;
    font-size: 1.1em;
}

.metrics-toggles {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.metric-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    transition: var(--transition-fast);
}

.metric-toggle:hover {
    background: rgba(0, 255, 127, 0.1);
    transform: translateX(4px);
}

/* Backup */
.backup-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.backup-info {
    background: rgba(0, 255, 127, 0.1);
    padding: 16px;
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 127, 0.3);
}

.backup-info p {
    margin: 4px 0;
    font-size: 0.9em;
}

.backup-info strong {
    color: #00ff7f;
}

.warning-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 6px;
    margin-top: 12px;
    color: #ffc107;
    font-size: 0.9em;
}

.warning-message i {
    color: #ffc107;
}

/* Seção Actions */
.section-actions {
    margin-top: 1.5rem;
    text-align: center;
}

.btn-save-section {
    background: var(--gradient-primary);
    color: var(--black);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-medium);
    box-shadow: var(--shadow-medium);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-save-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.btn-save-section.saving {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-save-section.saved {
    background: linear-gradient(135deg, #28a745, #20c997);
}

/* Color picker */
.color-picker-group {
    display: flex;
    align-items: center;
    gap: 16px;
}

.color-picker-group input[type="color"] {
    width: 50px;
    height: 40px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    background: none;
}

.color-presets {
    display: flex;
    gap: 8px;
}

.color-preset {
    width: 30px;
    height: 30px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-preset:hover {
    border-color: #00ff7f;
    transform: scale(1.1);
}

/* Save info */
.save-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(0, 255, 127, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 255, 127, 0.3);
    margin-bottom: 2rem;
}

.save-info i {
    color: #00ff7f;
}

/* Responsividade para novos módulos */
@media (max-width: 768px) {
    .command-toggles,
    .metrics-toggles {
        gap: 8px;
    }

    .command-toggle,
    .metric-toggle {
        padding: 8px;
        font-size: 0.9em;
    }

    .analytics-features {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .backup-actions {
        flex-direction: column;
    }

    .color-picker-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
}

/* ===== SISTEMA DE VERIFICAÇÃO INTEGRADO ===== */
.verification-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.verification-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-subtitle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #00ff7f;
    margin-bottom: 1rem;
    text-shadow: 0 0 10px rgba(0, 255, 127, 0.3);
}

.section-subtitle i {
    font-size: 1rem;
    filter: drop-shadow(0 0 5px rgba(0, 255, 127, 0.5));
}

.method-config {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(0, 255, 127, 0.05);
    border: 1px solid rgba(0, 255, 127, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.method-section {
    padding: 0.5rem 0;
    transition: all 0.3s ease;
}

.method-section.active {
    background: rgba(0, 255, 127, 0.05);
    border-radius: 6px;
    padding: 1rem;
}

.sub-config {
    margin-left: 2rem;
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-left: 3px solid #00ff7f;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
}

.info-box {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.3);
    border-radius: 6px;
    color: #87ceeb;
    font-size: 0.9rem;
}

.info-box i {
    color: #007bff;
    filter: drop-shadow(0 0 3px rgba(0, 123, 255, 0.5));
}

/* Enhanced Preview Section */
.verification-preview-enhanced {
    margin-top: 1.5rem;
}

.preview-embed-enhanced {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid rgba(0, 255, 127, 0.3);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    transition: all 0.4s ease;
}

.preview-embed-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 255, 127, 0.5);
}

.embed-header-enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-green) 0%, #32ff7e 100%);
    color: var(--black);
}

.embed-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.1rem;
}

.embed-brand i {
    font-size: 1.4rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.status-badge {
    background: rgba(0, 0, 0, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.embed-content-enhanced {
    padding: 2rem;
    color: var(--text-primary);
}

.verification-info h5 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-green);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.verification-info p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.preview-method-enhanced {
    margin: 1.5rem 0;
}

.method-display {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(0, 255, 127, 0.1) 0%, rgba(13, 77, 32, 0.1) 100%);
    border: 1px solid rgba(0, 255, 127, 0.3);
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
}

.method-display:hover {
    background: linear-gradient(135deg, rgba(0, 255, 127, 0.15) 0%, rgba(13, 77, 32, 0.15) 100%);
    border-color: rgba(0, 255, 127, 0.5);
}

.method-display i {
    color: var(--primary-green);
    font-size: 1.2rem;
}

.verification-details {
    display: flex;
    gap: 2rem;
    margin-top: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.detail-item i {
    color: var(--primary-green);
    width: 16px;
    text-align: center;
}

.embed-footer-enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: rgba(0, 0, 0, 0.4);
    border-top: 1px solid rgba(0, 255, 127, 0.2);
    font-size: 0.85rem;
    color: var(--text-muted);
}

.footer-info,
.footer-timestamp {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-info i,
.footer-timestamp i {
    color: var(--primary-green);
    opacity: 0.7;
}

/* Animações para atualizações em tempo real */
@keyframes pulseGlow {
    0% {
        box-shadow: 0 0 5px rgba(0, 255, 127, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 255, 127, 0.6), 0 0 30px rgba(0, 255, 127, 0.3);
    }
    100% {
        box-shadow: 0 0 5px rgba(0, 255, 127, 0.3);
    }
}

/* Enhanced toggle styling para verificação */
.verification-section .toggle-group {
    margin-bottom: 1rem;
}

.verification-section .toggle-label {
    font-weight: 500;
    color: #ffffff;
}

/* Additional Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design for New Verification Module */
@media (max-width: 1200px) {
    .config-section-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .verification-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .verification-master-toggle {
        flex-direction: column;
        gap: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .tab-navigation {
        flex-direction: column;
        gap: 0.5rem;
    }

    .tab-btn {
        justify-content: center;
        padding: 0.75rem 1rem;
    }

    .verification-details {
        flex-direction: column;
        gap: 1rem;
    }

    .embed-footer-enhanced {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .verification-config-tabs {
        padding: 1rem;
    }

    .config-group {
        padding: 1rem;
    }

    .verification-main-card {
        margin: 0 -1rem;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
}

@media (max-width: 480px) {
    .verification-title-section h3.verification-main-title {
        font-size: 1.4rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .config-group-title {
        font-size: 1rem;
    }

    .embed-content-enhanced {
        padding: 1.5rem;
    }

    .verification-stats-section {
        padding: 1.5rem;
    }
}

/* Custom scrollbar for verification module */
.verification-main-card::-webkit-scrollbar {
    width: 8px;
}

.verification-main-card::-webkit-scrollbar-track {
    background: var(--dark-gray);
}

.verification-main-card::-webkit-scrollbar-thumb {
    background: var(--primary-green);
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(0, 255, 127, 0.3);
}

.verification-main-card::-webkit-scrollbar-thumb:hover {
    background: #32ff7e;
    box-shadow: 0 0 10px rgba(0, 255, 127, 0.5);
}

/* ===== CORREÇÃO PARA GARANTIR VISIBILIDADE DO BACKUP ===== */
.sidebar-nav {
    min-height: 700px;
    padding-bottom: 3rem;
}

/* ===== SISTEMA DE DEPENDÊNCIAS - SUB-FUNÇÕES ===== */
.form-group.disabled,
.config-item.disabled,
.toggle-group.disabled {
    opacity: 0.4 !important;
    pointer-events: none !important;
    transition: opacity 0.3s ease;
    position: relative;
}

.form-group.disabled::before,
.config-item.disabled::before,
.toggle-group.disabled::before {
    content: "🔒 Ative a função principal primeiro";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: #ff6b6b;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.form-group.disabled:hover::before,
.config-item.disabled:hover::before,
.toggle-group.disabled:hover::before {
    opacity: 1;
}

/* Estilo especial para toggles principais - Design minimalista */
.main-toggle {
    position: relative;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(0, 255, 127, 0.05), rgba(0, 255, 127, 0.02));
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 127, 0.2);
}

.main-toggle .toggle-label {
    font-weight: 600;
    color: #00ff7f;
    font-size: 1.1em;
}

/* Animação para mudanças de estado */
.dependency-transition {
    transition: all 0.3s ease !important;
}

/* ===== DESIGN PROFISSIONAL MINIMALISTA PARA SELECTS ===== */
select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: #1e1e1e;
    border: 1px solid #333333;
    border-radius: 6px;
    padding: 14px 45px 14px 16px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 400;
    width: 100%;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 14px center;
    background-size: 14px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

select:hover {
    border-color: #555555;
    background-color: #252525;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300ff7f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

select:focus {
    outline: none;
    border-color: #00ff7f;
    background-color: #252525;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300ff7f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    box-shadow: 0 0 0 2px rgba(0, 255, 127, 0.2);
}

select:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: #1a1a1a;
    border-color: #2a2a2a;
    color: #666666;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23444444' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* Estilo minimalista para options */
select option {
    background: #2a2a2a;
    color: #ffffff;
    padding: 12px 16px;
    border: none;
    font-size: 14px;
    line-height: 1.4;
}

select option:hover {
    background: #333333;
}

select option:checked,
select option:focus {
    background: rgba(0, 255, 127, 0.15);
    color: #ffffff;
}

select option[value=""] {
    color: #888888;
    font-style: italic;
}

/* Container para melhor apresentação */
.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    color: #ffffff;
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0.3px;
}

.input-group small {
    display: block;
    margin-top: 6px;
    color: #888888;
    font-size: 12px;
    line-height: 1.4;
}

/* ===== MELHORIAS ESPECIAIS PARA CHANNEL/ROLE SELECTS ===== */
select[name*="channel"],
select[name*="role"] {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
}

select[name*="channel"] option,
select[name*="role"] option {
    padding: 10px 16px;
    line-height: 1.6;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

select[name*="channel"] option:last-child,
select[name*="role"] option:last-child {
    border-bottom: none;
}

/* Estilo para opções vazias/placeholder */
select option[value=""] {
    background: #1e1e1e;
    color: #666666;
    font-style: italic;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hover effect melhorado para options */
select option:hover {
    background: linear-gradient(90deg, rgba(0, 255, 127, 0.1), rgba(0, 255, 127, 0.05));
    color: #ffffff;
}

/* Estilo para options selecionadas */
select option:checked {
    background: rgba(0, 255, 127, 0.2);
    color: #ffffff;
    font-weight: 500;
}

/* Container wrapper para selects importantes */
.select-wrapper {
    position: relative;
    display: block;
}

.select-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 40px;
    background: linear-gradient(90deg, transparent, #1e1e1e);
    pointer-events: none;
    border-radius: 0 6px 6px 0;
}

/* ===== DESIGN PROFISSIONAL PARA INPUTS ===== */
input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"],
textarea {
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    border: 1px solid rgba(0, 255, 127, 0.3);
    border-radius: 8px;
    padding: 12px 16px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    width: 100%;
    transition: all 0.3s ease;
}

input[type="text"]:hover,
input[type="number"]:hover,
input[type="email"]:hover,
input[type="password"]:hover,
textarea:hover {
    border-color: rgba(0, 255, 127, 0.6);
    background: linear-gradient(135deg, #2d2d2d, #3a3a3a);
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus {
    outline: none;
    border-color: #00ff7f;
    box-shadow: 0 0 0 3px rgba(0, 255, 127, 0.1);
    background: linear-gradient(135deg, #2d2d2d, #3a3a3a);
}

input[type="text"]:disabled,
input[type="number"]:disabled,
input[type="email"]:disabled,
input[type="password"]:disabled,
textarea:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #1a1a1a;
    border-color: rgba(255, 255, 255, 0.1);
}

/* ===== DESIGN PROFISSIONAL PARA RANGE SLIDERS ===== */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(90deg, #1a1a1a, #2d2d2d);
    outline: none;
    cursor: pointer;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00ff7f, #32ff7e);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 255, 127, 0.3);
    transition: all 0.3s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 255, 127, 0.5);
}

input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00ff7f, #32ff7e);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 255, 127, 0.3);
    transition: all 0.3s ease;
}

input[type="range"]::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 255, 127, 0.5);
}

/* ===== LABELS PROFISSIONAIS ===== */
.input-group label,
.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.5px;
}

.input-group small,
.form-group small {
    display: block;
    margin-top: 6px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    font-style: italic;
}

/* ===== DESIGN ESPECIAL PARA SENSITIVITY SLIDER ===== */
.sensitivity-slider {
    padding: 20px;
    background: linear-gradient(135deg, rgba(0, 255, 127, 0.05), rgba(0, 255, 127, 0.02));
    border-radius: 12px;
    border: 1px solid rgba(0, 255, 127, 0.2);
    margin: 16px 0;
}

.sensitivity-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    font-size: 12px;
    font-weight: 600;
}

.sensitivity-labels span {
    padding: 4px 8px;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.3);
    color: rgba(255, 255, 255, 0.8);
}

.sensitivity-description {
    margin-top: 16px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border-left: 3px solid #00ff7f;
}

.sensitivity-description p {
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
}

/* ===== MELHORIAS PARA COMMAND TOGGLES ===== */
.command-toggles {
    display: grid;
    gap: 12px;
    margin-top: 16px;
}

.command-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.1));
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.command-toggle:hover {
    background: linear-gradient(135deg, rgba(0, 255, 127, 0.1), rgba(0, 255, 127, 0.05));
    border-color: rgba(0, 255, 127, 0.3);
}

.command-name {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #00ff7f;
    min-width: 80px;
}

.command-desc {
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    flex: 1;
}
