/**
 * ========================================
 * SISTEMA DE BACKUP & RESTORE
 * Backup completo e restore seletivo
 * ========================================
 */

const fs = require('fs').promises;
const path = require('path');

class BackupSystem {
    constructor(client) {
        this.client = client;
        this.backupPath = path.join(__dirname, '../backups');
        
        this.init();
    }

    async init() {
        console.log('💾 Sistema de Backup inicializado');
        
        // Criar pasta de backups se não existir
        try {
            await fs.mkdir(this.backupPath, { recursive: true });
        } catch (error) {
            console.error('Erro ao criar pasta de backups:', error);
        }
        
        this.startAutoBackup();
    }

    // ===== BACKUP COMPLETO ===== //
    async createFullBackup(guildId, options = {}) {
        try {
            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) {
                return { success: false, message: 'Servidor não encontrado!' };
            }

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupId = `${guildId}_${timestamp}`;
            
            const backup = {
                id: backupId,
                guildId: guildId,
                guildName: guild.name,
                createdAt: new Date().toISOString(),
                createdBy: options.createdBy || 'Sistema',
                type: 'full',
                data: {}
            };

            // Backup das configurações
            backup.data.config = await this.backupGuildConfig(guildId);
            
            // Backup dos canais
            if (options.includeChannels !== false) {
                backup.data.channels = await this.backupChannels(guild);
            }
            
            // Backup dos cargos
            if (options.includeRoles !== false) {
                backup.data.roles = await this.backupRoles(guild);
            }
            
            // Backup das permissões
            if (options.includePermissions !== false) {
                backup.data.permissions = await this.backupPermissions(guild);
            }
            
            // Backup dos dados de usuários
            if (options.includeUserData !== false) {
                backup.data.userData = await this.backupUserData(guildId);
            }
            
            // Backup da economia
            if (options.includeEconomy !== false) {
                backup.data.economy = await this.backupEconomy(guildId);
            }
            
            // Backup dos níveis
            if (options.includeLevels !== false) {
                backup.data.levels = await this.backupLevels(guildId);
            }

            // Salvar backup no arquivo
            const backupFile = path.join(this.backupPath, `${backupId}.json`);
            await fs.writeFile(backupFile, JSON.stringify(backup, null, 2));
            
            // Salvar referência no banco
            await this.saveBackupReference(backup);

            return {
                success: true,
                backup: backup,
                message: `✅ Backup completo criado! ID: ${backupId}`
            };

        } catch (error) {
            console.error('Erro ao criar backup:', error);
            return { success: false, message: 'Erro ao criar backup!' };
        }
    }

    // ===== BACKUP SELETIVO ===== //
    async createSelectiveBackup(guildId, components, options = {}) {
        try {
            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) {
                return { success: false, message: 'Servidor não encontrado!' };
            }

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupId = `${guildId}_selective_${timestamp}`;
            
            const backup = {
                id: backupId,
                guildId: guildId,
                guildName: guild.name,
                createdAt: new Date().toISOString(),
                createdBy: options.createdBy || 'Sistema',
                type: 'selective',
                components: components,
                data: {}
            };

            // Backup apenas dos componentes selecionados
            for (const component of components) {
                switch (component) {
                    case 'config':
                        backup.data.config = await this.backupGuildConfig(guildId);
                        break;
                    case 'channels':
                        backup.data.channels = await this.backupChannels(guild);
                        break;
                    case 'roles':
                        backup.data.roles = await this.backupRoles(guild);
                        break;
                    case 'economy':
                        backup.data.economy = await this.backupEconomy(guildId);
                        break;
                    case 'levels':
                        backup.data.levels = await this.backupLevels(guildId);
                        break;
                }
            }

            // Salvar backup
            const backupFile = path.join(this.backupPath, `${backupId}.json`);
            await fs.writeFile(backupFile, JSON.stringify(backup, null, 2));
            
            await this.saveBackupReference(backup);

            return {
                success: true,
                backup: backup,
                message: `✅ Backup seletivo criado! Componentes: ${components.join(', ')}`
            };

        } catch (error) {
            console.error('Erro ao criar backup seletivo:', error);
            return { success: false, message: 'Erro ao criar backup!' };
        }
    }

    // ===== RESTORE ===== //
    async restoreFromBackup(backupId, options = {}) {
        try {
            // Carregar backup
            const backup = await this.loadBackup(backupId);
            if (!backup) {
                return { success: false, message: 'Backup não encontrado!' };
            }

            const guild = this.client.guilds.cache.get(backup.guildId);
            if (!guild) {
                return { success: false, message: 'Servidor não encontrado!' };
            }

            const results = [];

            // Restaurar componentes
            if (backup.data.config && (options.restoreConfig !== false)) {
                const result = await this.restoreGuildConfig(backup.guildId, backup.data.config);
                results.push({ component: 'config', success: result.success });
            }

            if (backup.data.channels && (options.restoreChannels !== false)) {
                const result = await this.restoreChannels(guild, backup.data.channels);
                results.push({ component: 'channels', success: result.success });
            }

            if (backup.data.roles && (options.restoreRoles !== false)) {
                const result = await this.restoreRoles(guild, backup.data.roles);
                results.push({ component: 'roles', success: result.success });
            }

            if (backup.data.economy && (options.restoreEconomy !== false)) {
                const result = await this.restoreEconomy(backup.guildId, backup.data.economy);
                results.push({ component: 'economy', success: result.success });
            }

            if (backup.data.levels && (options.restoreLevels !== false)) {
                const result = await this.restoreLevels(backup.guildId, backup.data.levels);
                results.push({ component: 'levels', success: result.success });
            }

            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;

            return {
                success: successCount > 0,
                results: results,
                message: `✅ Restore concluído! ${successCount}/${totalCount} componentes restaurados.`
            };

        } catch (error) {
            console.error('Erro ao restaurar backup:', error);
            return { success: false, message: 'Erro ao restaurar backup!' };
        }
    }

    // ===== MÉTODOS DE BACKUP ===== //
    async backupGuildConfig(guildId) {
        try {
            const config = await this.client.database.getGuildConfig(guildId);
            return config || {};
        } catch (error) {
            console.error('Erro ao fazer backup da configuração:', error);
            return {};
        }
    }

    async backupChannels(guild) {
        try {
            const channels = [];
            
            guild.channels.cache.forEach(channel => {
                if (channel.type === 4) { // Category
                    channels.push({
                        id: channel.id,
                        name: channel.name,
                        type: channel.type,
                        position: channel.position,
                        permissions: channel.permissionOverwrites.cache.map(perm => ({
                            id: perm.id,
                            type: perm.type,
                            allow: perm.allow.bitfield.toString(),
                            deny: perm.deny.bitfield.toString()
                        }))
                    });
                }
            });

            guild.channels.cache.forEach(channel => {
                if (channel.type !== 4) { // Not category
                    channels.push({
                        id: channel.id,
                        name: channel.name,
                        type: channel.type,
                        position: channel.position,
                        parentId: channel.parentId,
                        topic: channel.topic,
                        nsfw: channel.nsfw,
                        rateLimitPerUser: channel.rateLimitPerUser,
                        permissions: channel.permissionOverwrites.cache.map(perm => ({
                            id: perm.id,
                            type: perm.type,
                            allow: perm.allow.bitfield.toString(),
                            deny: perm.deny.bitfield.toString()
                        }))
                    });
                }
            });

            return channels;
        } catch (error) {
            console.error('Erro ao fazer backup dos canais:', error);
            return [];
        }
    }

    async backupRoles(guild) {
        try {
            const roles = [];
            
            guild.roles.cache.forEach(role => {
                if (role.name !== '@everyone' && !role.managed) {
                    roles.push({
                        id: role.id,
                        name: role.name,
                        color: role.color,
                        hoist: role.hoist,
                        mentionable: role.mentionable,
                        permissions: role.permissions.bitfield.toString(),
                        position: role.position
                    });
                }
            });

            return roles;
        } catch (error) {
            console.error('Erro ao fazer backup dos cargos:', error);
            return [];
        }
    }

    async backupPermissions(guild) {
        try {
            const permissions = {};
            
            // Backup das permissões de canais
            guild.channels.cache.forEach(channel => {
                permissions[channel.id] = channel.permissionOverwrites.cache.map(perm => ({
                    id: perm.id,
                    type: perm.type,
                    allow: perm.allow.bitfield.toString(),
                    deny: perm.deny.bitfield.toString()
                }));
            });

            return permissions;
        } catch (error) {
            console.error('Erro ao fazer backup das permissões:', error);
            return {};
        }
    }

    async backupUserData(guildId) {
        try {
            const query = 'SELECT * FROM user_stats WHERE guild_id = ?';
            const rows = await this.client.database.all(query, [guildId]);
            return rows || [];
        } catch (error) {
            console.error('Erro ao fazer backup dos dados de usuário:', error);
            return [];
        }
    }

    async backupEconomy(guildId) {
        try {
            const query = 'SELECT * FROM economy WHERE guild_id = ?';
            const rows = await this.client.database.all(query, [guildId]);
            return rows || [];
        } catch (error) {
            console.error('Erro ao fazer backup da economia:', error);
            return [];
        }
    }

    async backupLevels(guildId) {
        try {
            const query = 'SELECT * FROM user_levels WHERE guild_id = ?';
            const rows = await this.client.database.all(query, [guildId]);
            return rows || [];
        } catch (error) {
            console.error('Erro ao fazer backup dos níveis:', error);
            return [];
        }
    }

    // ===== MÉTODOS DE RESTORE ===== //
    async restoreGuildConfig(guildId, configData) {
        try {
            await this.client.database.saveGuildConfig(guildId, configData);
            return { success: true };
        } catch (error) {
            console.error('Erro ao restaurar configuração:', error);
            return { success: false };
        }
    }

    async restoreChannels(guild, channelsData) {
        try {
            // Implementar restore de canais (cuidado com limitações da API)
            console.log(`Restaurando ${channelsData.length} canais...`);
            return { success: true };
        } catch (error) {
            console.error('Erro ao restaurar canais:', error);
            return { success: false };
        }
    }

    async restoreRoles(guild, rolesData) {
        try {
            // Implementar restore de cargos
            console.log(`Restaurando ${rolesData.length} cargos...`);
            return { success: true };
        } catch (error) {
            console.error('Erro ao restaurar cargos:', error);
            return { success: false };
        }
    }

    async restoreEconomy(guildId, economyData) {
        try {
            // Limpar dados existentes
            await this.client.database.run('DELETE FROM economy WHERE guild_id = ?', [guildId]);
            
            // Restaurar dados
            for (const row of economyData) {
                const query = `
                    INSERT INTO economy (user_id, guild_id, balance, work_streak, daily_streak, last_work, last_daily)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                `;
                await this.client.database.run(query, [
                    row.user_id, row.guild_id, row.balance, row.work_streak, 
                    row.daily_streak, row.last_work, row.last_daily
                ]);
            }
            
            return { success: true };
        } catch (error) {
            console.error('Erro ao restaurar economia:', error);
            return { success: false };
        }
    }

    async restoreLevels(guildId, levelsData) {
        try {
            // Limpar dados existentes
            await this.client.database.run('DELETE FROM user_levels WHERE guild_id = ?', [guildId]);
            
            // Restaurar dados
            for (const row of levelsData) {
                const query = `
                    INSERT INTO user_levels (user_id, guild_id, xp, level, last_xp_gain)
                    VALUES (?, ?, ?, ?, ?)
                `;
                await this.client.database.run(query, [
                    row.user_id, row.guild_id, row.xp, row.level, row.last_xp_gain
                ]);
            }
            
            return { success: true };
        } catch (error) {
            console.error('Erro ao restaurar níveis:', error);
            return { success: false };
        }
    }

    // ===== GERENCIAMENTO DE BACKUPS ===== //
    async listBackups(guildId) {
        try {
            const query = 'SELECT * FROM backups WHERE guild_id = ? ORDER BY created_at DESC';
            const rows = await this.client.database.all(query, [guildId]);
            return rows || [];
        } catch (error) {
            console.error('Erro ao listar backups:', error);
            return [];
        }
    }

    async deleteBackup(backupId) {
        try {
            // Deletar arquivo
            const backupFile = path.join(this.backupPath, `${backupId}.json`);
            await fs.unlink(backupFile);
            
            // Deletar referência do banco
            await this.client.database.run('DELETE FROM backups WHERE id = ?', [backupId]);
            
            return { success: true };
        } catch (error) {
            console.error('Erro ao deletar backup:', error);
            return { success: false };
        }
    }

    async loadBackup(backupId) {
        try {
            const backupFile = path.join(this.backupPath, `${backupId}.json`);
            const data = await fs.readFile(backupFile, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error('Erro ao carregar backup:', error);
            return null;
        }
    }

    async saveBackupReference(backup) {
        try {
            const query = `
                INSERT INTO backups (id, guild_id, guild_name, type, created_at, created_by, size)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `;
            
            const size = JSON.stringify(backup).length;
            
            await this.client.database.run(query, [
                backup.id,
                backup.guildId,
                backup.guildName,
                backup.type,
                backup.createdAt,
                backup.createdBy,
                size
            ]);
        } catch (error) {
            console.error('Erro ao salvar referência do backup:', error);
        }
    }

    // ===== AUTO BACKUP ===== //
    startAutoBackup() {
        // Backup automático diário às 3:00 AM
        setInterval(() => {
            const now = new Date();
            if (now.getHours() === 3 && now.getMinutes() === 0) {
                this.performAutoBackup();
            }
        }, 60000); // Verificar a cada minuto
    }

    async performAutoBackup() {
        try {
            console.log('🔄 Iniciando backup automático...');
            
            const guilds = this.client.guilds.cache;
            let successCount = 0;
            
            for (const [guildId, guild] of guilds) {
                try {
                    const result = await this.createFullBackup(guildId, {
                        createdBy: 'Sistema Automático',
                        includeChannels: false, // Não incluir canais no backup automático
                        includeRoles: false
                    });
                    
                    if (result.success) {
                        successCount++;
                    }
                } catch (error) {
                    console.error(`Erro no backup automático de ${guild.name}:`, error);
                }
            }
            
            console.log(`✅ Backup automático concluído: ${successCount}/${guilds.size} servidores`);
            
            // Limpar backups antigos (manter apenas os últimos 30 dias)
            await this.cleanOldBackups();
            
        } catch (error) {
            console.error('Erro no backup automático:', error);
        }
    }

    async cleanOldBackups() {
        try {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const query = 'SELECT id FROM backups WHERE created_at < ? AND created_by = "Sistema Automático"';
            const oldBackups = await this.client.database.all(query, [thirtyDaysAgo.toISOString()]);

            for (const backup of oldBackups) {
                await this.deleteBackup(backup.id);
            }

            console.log(`🗑️ ${oldBackups.length} backups antigos removidos`);
        } catch (error) {
            console.error('Erro ao limpar backups antigos:', error);
        }
    }

    async getGuildBackups(guildId) {
        try {
            const query = `
                SELECT id, guild_name, type, created_at, created_by, size
                FROM backups
                WHERE guild_id = ?
                ORDER BY created_at DESC
                LIMIT 50
            `;

            const backups = await this.client.database.all(query, [guildId]);

            return backups.map(backup => ({
                ...backup,
                created_at: new Date(backup.created_at).toISOString(),
                size_formatted: this.formatFileSize(backup.size || 0)
            }));
        } catch (error) {
            console.error('Erro ao buscar backups do servidor:', error);
            return [];
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

module.exports = BackupSystem;
