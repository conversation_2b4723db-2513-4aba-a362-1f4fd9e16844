/**
 * ========================================
 * COMANDO: DASHBOARD
 * Abre o dashboard web do bot
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('dashboard')
        .setNameLocalizations({
            'pt-BR': 'dashboard'
        })
        .setDescription('🌐 Abre o dashboard web para configurar o bot')
        .setDescriptionLocalizations({
            'pt-BR': 'Abre o dashboard web para configurar o bot no navegador'
        }),

    category: 'config',
    cooldown: 5,

    async execute(interaction) {
        try {
            const guildId = interaction.guild.id;
            const userId = interaction.user.id;
            const member = interaction.member;

            // Verificar se o usuário tem permissão de administrador
            if (!member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Você precisa ter permissão de **Administrador** para acessar o dashboard!',
                    ephemeral: true
                });
            }

            // URLs do dashboard
            const dashboardUrl = process.env.DASHBOARD_URL || 'http://localhost:3000';
            const homeUrl = dashboardUrl; // Redirecionar para página inicial
            const guildDashboardUrl = `${dashboardUrl}/dashboard/${guildId}`;

            // Criar embed informativo com ícones
            const embedData = {
                title: '🌐 Dashboard Web - Nodex | Moderação',
                description: 'Explore nosso site e configure o bot através do dashboard web moderno!',
                fields: [
                    {
                        name: '🏠 Acesse o Site',
                        value: `[**Clique aqui para explorar**](${homeUrl})`,
                        inline: false
                    },
                    {
                        name: '🛡️ Recursos de Moderação Avançada:',
                        value: [
                            `${SVGIcons.get('bullet')} 🤖 **IA de Moderação** - Detecção automática de toxicidade`,
                            `${SVGIcons.get('bullet')} 🚫 **Anti-Raid Inteligente** - Proteção contra invasões`,
                            `${SVGIcons.get('bullet')} 🎫 **Sistema de Tickets** - Suporte profissional`,
                            `${SVGIcons.get('bullet')} 📊 **Logs Detalhados** - Histórico completo`,
                            `${SVGIcons.get('bullet')} ⚙️ **Configuração Avançada** - Personalização total`,
                            `${SVGIcons.get('bullet')} 📈 **Analytics de Moderação** - Estatísticas em tempo real`
                        ].join('\n'),
                        inline: false
                    },
                ]
            };

            const processedEmbed = SVGIcons.createEmbed(embedData);
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle(processedEmbed.title)
                .setDescription(processedEmbed.description)
                .addFields(processedEmbed.fields)
                .addFields({
                        name: '🔐 Como usar:',
                        value: [
                            '1️⃣ Clique no link acima para explorar',
                            '2️⃣ Navegue pelo site e veja os recursos',
                            '3️⃣ Clique em "Dashboard" para configurar',
                            '4️⃣ Faça login com sua conta Discord',
                            '5️⃣ Selecione este servidor e configure!'
                        ].join('\n'),
                        inline: false
                    })
                .setThumbnail(interaction.client.user.displayAvatarURL())
                .setFooter({
                    text: `Solicitado por ${interaction.user.username}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            // Criar botões
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('🏠 Explorar Site')
                        .setStyle(ButtonStyle.Link)
                        .setURL(homeUrl),
                    new ButtonBuilder()
                        .setLabel('📚 Documentação')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`${dashboardUrl}/docs`),
                    new ButtonBuilder()
                        .setLabel('🎧 Suporte')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`${dashboardUrl}/support`)
                );

            await interaction.reply({
                embeds: [embed],
                components: [row],
                ephemeral: false
            });

            // Log da ação
            console.log(`🌐 [DASHBOARD] ${interaction.user.username} (${userId}) acessou o comando dashboard no servidor ${interaction.guild.name} (${guildId})`);

        } catch (error) {
            console.error('Erro no comando dashboard:', error);
            
            const errorMessage = '❌ Ocorreu um erro ao gerar o link do dashboard!';
            
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
};
