/**
 * ========================================
 * SISTEMA DE ANALYTICS AVANÇADO
 * Estatísticas em tempo real e relatórios
 * ========================================
 */

class AnalyticsSystem {
    constructor(client) {
        this.client = client;
        this.stats = new Map();
        this.dailyStats = new Map();
        
        this.init();
    }

    init() {
        console.log('📊 Sistema de Analytics inicializado');
        this.setupEventListeners();
        this.startStatsCollection();
    }

    setupEventListeners() {
        // Rastrear mensagens
        this.client.on('messageCreate', (message) => {
            if (!message.guild || message.author.bot) return;
            this.trackMessage(message);
        });

        // Rastrear entradas/saídas
        this.client.on('guildMemberAdd', (member) => {
            this.trackMemberJoin(member);
        });

        this.client.on('guildMemberRemove', (member) => {
            this.trackMemberLeave(member);
        });

        // Rastrear comandos
        this.client.on('interactionCreate', (interaction) => {
            if (interaction.isCommand()) {
                this.trackCommand(interaction);
            }
        });
    }

    // ===== COLETA DE DADOS ===== //
    // Métodos movidos para o final do arquivo com verificação de ativação

    trackModeration(guildId, action, userId, moderatorId) {
        this.incrementStat(guildId, 'moderation_total');
        this.incrementStat(guildId, 'moderation_today');
        this.incrementStat(guildId, `moderation_${action}`);
        
        // Salvar ação de moderação
        this.saveModerationAction(guildId, action, userId, moderatorId);
    }

    // ===== ESTATÍSTICAS ===== //
    async getGuildStats(guildId) {
        try {
            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) return null;

            const stats = this.stats.get(guildId) || {};
            const today = new Date().toDateString();
            const dailyStats = this.dailyStats.get(`${guildId}_${today}`) || {};

            // Estatísticas básicas
            const basicStats = {
                totalMembers: guild.memberCount,
                totalChannels: guild.channels.cache.size,
                totalRoles: guild.roles.cache.size,
                boostLevel: guild.premiumTier,
                boostCount: guild.premiumSubscriptionCount
            };

            // Estatísticas de atividade
            const activityStats = {
                messagesTotal: stats.messages_total || 0,
                messagesToday: dailyStats.messages_today || 0,
                commandsTotal: stats.commands_total || 0,
                commandsToday: dailyStats.commands_today || 0,
                moderationTotal: stats.moderation_total || 0,
                moderationToday: dailyStats.moderation_today || 0
            };

            // Estatísticas de membros
            const memberStats = {
                joinedTotal: stats.members_joined_total || 0,
                joinedToday: dailyStats.members_joined_today || 0,
                leftTotal: stats.members_left_total || 0,
                leftToday: dailyStats.members_left_today || 0,
                retentionRate: this.calculateRetentionRate(guildId)
            };

            // Top canais
            const topChannels = await this.getTopChannels(guildId);
            
            // Top usuários
            const topUsers = await this.getTopUsers(guildId);

            // Atividade por hora
            const hourlyActivity = this.getHourlyActivity(guildId);

            return {
                basic: basicStats,
                activity: activityStats,
                members: memberStats,
                topChannels,
                topUsers,
                hourlyActivity,
                lastUpdated: new Date().toISOString()
            };

        } catch (error) {
            console.error('Erro ao buscar estatísticas:', error);
            return null;
        }
    }

    async getTopChannels(guildId, limit = 5) {
        try {
            const query = `
                SELECT channel_id, messages 
                FROM channel_stats 
                WHERE guild_id = ? 
                ORDER BY messages DESC 
                LIMIT ?
            `;
            const rows = await this.client.database.all(query, [guildId, limit]);
            
            return rows.map(row => ({
                channelId: row.channel_id,
                messages: row.messages,
                channel: this.client.channels.cache.get(row.channel_id)?.name || 'Canal Deletado'
            }));
        } catch (error) {
            console.error('Erro ao buscar top canais:', error);
            return [];
        }
    }

    async getTopUsers(guildId, limit = 5) {
        try {
            const query = `
                SELECT user_id, messages 
                FROM user_activity 
                WHERE guild_id = ? 
                ORDER BY messages DESC 
                LIMIT ?
            `;
            const rows = await this.client.database.all(query, [guildId, limit]);
            
            return rows.map(row => ({
                userId: row.user_id,
                messages: row.messages,
                user: this.client.users.cache.get(row.user_id)?.username || 'Usuário Desconhecido'
            }));
        } catch (error) {
            console.error('Erro ao buscar top usuários:', error);
            return [];
        }
    }

    getHourlyActivity(guildId) {
        const stats = this.stats.get(guildId) || {};
        const hourlyData = [];
        
        for (let hour = 0; hour < 24; hour++) {
            hourlyData.push({
                hour,
                messages: stats[`messages_hour_${hour}`] || 0
            });
        }
        
        return hourlyData;
    }

    calculateRetentionRate(guildId) {
        const stats = this.stats.get(guildId) || {};
        const joined = stats.members_joined_total || 0;
        const left = stats.members_left_total || 0;
        
        if (joined === 0) return 100;
        
        return Math.round(((joined - left) / joined) * 100);
    }

    // ===== RELATÓRIOS ===== //
    async generateDailyReport(guildId) {
        const stats = await this.getGuildStats(guildId);
        if (!stats) return null;

        const guild = this.client.guilds.cache.get(guildId);
        const today = new Date().toLocaleDateString('pt-BR');

        return {
            color: 0x00ff7f,
            title: `📊 Relatório Diário - ${guild.name}`,
            description: `Estatísticas de ${today}`,
            fields: [
                {
                    name: '💬 Atividade de Mensagens',
                    value: `**Hoje:** ${stats.activity.messagesToday}\n**Total:** ${stats.activity.messagesTotal.toLocaleString()}`,
                    inline: true
                },
                {
                    name: '👥 Movimento de Membros',
                    value: `**Entraram:** ${stats.members.joinedToday}\n**Saíram:** ${stats.members.leftToday}\n**Total:** ${stats.basic.totalMembers}`,
                    inline: true
                },
                {
                    name: '🛡️ Moderação',
                    value: `**Hoje:** ${stats.activity.moderationToday}\n**Total:** ${stats.activity.moderationTotal}`,
                    inline: true
                },
                {
                    name: '⚡ Comandos Usados',
                    value: `**Hoje:** ${stats.activity.commandsToday}\n**Total:** ${stats.activity.commandsTotal}`,
                    inline: true
                },
                {
                    name: '📈 Taxa de Retenção',
                    value: `${stats.members.retentionRate}%`,
                    inline: true
                },
                {
                    name: '🚀 Boost do Servidor',
                    value: `**Nível:** ${stats.basic.boostLevel}\n**Boosts:** ${stats.basic.boostCount}`,
                    inline: true
                }
            ],
            footer: {
                text: 'Relatório gerado automaticamente'
            },
            timestamp: new Date().toISOString()
        };
    }

    async generateWeeklyReport(guildId) {
        // Implementar relatório semanal
        const stats = await this.getGuildStats(guildId);
        if (!stats) return null;

        const guild = this.client.guilds.cache.get(guildId);

        return {
            color: 0x00ff7f,
            title: `📊 Relatório Semanal - ${guild.name}`,
            description: 'Resumo da última semana',
            fields: [
                {
                    name: '🏆 Top Canais',
                    value: stats.topChannels.map((ch, i) => 
                        `${i + 1}. #${ch.channel} - ${ch.messages} mensagens`
                    ).join('\n') || 'Nenhum dado',
                    inline: false
                },
                {
                    name: '👑 Top Usuários',
                    value: stats.topUsers.map((user, i) => 
                        `${i + 1}. ${user.user} - ${user.messages} mensagens`
                    ).join('\n') || 'Nenhum dado',
                    inline: false
                }
            ],
            footer: {
                text: 'Relatório semanal'
            },
            timestamp: new Date().toISOString()
        };
    }

    // ===== GRÁFICOS ===== //
    generateChartData(guildId, type = 'hourly') {
        const stats = this.stats.get(guildId) || {};
        
        switch (type) {
            case 'hourly':
                return this.getHourlyActivity(guildId);
            
            case 'daily':
                // Implementar dados diários dos últimos 7 dias
                return this.getDailyActivity(guildId);
            
            case 'commands':
                // Implementar estatísticas de comandos
                return this.getCommandStats(guildId);
            
            default:
                return [];
        }
    }

    getDailyActivity(guildId) {
        // Simular dados dos últimos 7 dias
        const days = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            
            days.push({
                date: date.toLocaleDateString('pt-BR'),
                messages: Math.floor(Math.random() * 1000) + 100,
                members: Math.floor(Math.random() * 50) + 10
            });
        }
        return days;
    }

    getCommandStats(guildId) {
        const stats = this.stats.get(guildId) || {};
        const commands = [];
        
        // Extrair estatísticas de comandos
        for (const [key, value] of Object.entries(stats)) {
            if (key.startsWith('command_')) {
                const commandName = key.replace('command_', '');
                commands.push({
                    name: commandName,
                    uses: value
                });
            }
        }
        
        return commands.sort((a, b) => b.uses - a.uses).slice(0, 10);
    }

    // ===== UTILITÁRIOS ===== //
    incrementStat(guildId, statName, amount = 1) {
        if (!this.stats.has(guildId)) {
            this.stats.set(guildId, {});
        }
        
        const guildStats = this.stats.get(guildId);
        guildStats[statName] = (guildStats[statName] || 0) + amount;
    }

    incrementUserStat(guildId, userId, statName, amount = 1) {
        // Salvar estatística do usuário no banco
        this.saveUserActivity(guildId, userId, statName, amount);
    }

    incrementChannelStat(guildId, channelId, statName, amount = 1) {
        // Salvar estatística do canal no banco
        this.saveChannelActivity(guildId, channelId, statName, amount);
    }

    // ===== BANCO DE DADOS ===== //
    async saveUserActivity(guildId, userId, statName, amount) {
        try {
            const query = `
                INSERT INTO user_activity (guild_id, user_id, ${statName}) 
                VALUES (?, ?, ?) 
                ON CONFLICT(guild_id, user_id) 
                DO UPDATE SET ${statName} = ${statName} + ?
            `;
            await this.client.database.run(query, [guildId, userId, amount, amount]);
        } catch (error) {
            console.error('Erro ao salvar atividade do usuário:', error);
        }
    }

    async saveChannelActivity(guildId, channelId, statName, amount) {
        try {
            const query = `
                INSERT INTO channel_stats (guild_id, channel_id, ${statName}) 
                VALUES (?, ?, ?) 
                ON CONFLICT(guild_id, channel_id) 
                DO UPDATE SET ${statName} = ${statName} + ?
            `;
            await this.client.database.run(query, [guildId, channelId, amount, amount]);
        } catch (error) {
            console.error('Erro ao salvar atividade do canal:', error);
        }
    }

    async saveMemberData(member, action) {
        try {
            const query = `
                INSERT INTO member_activity (guild_id, user_id, action, timestamp)
                VALUES (?, ?, ?, ?)
            `;
            await this.client.database.run(query, [
                member.guild.id,
                member.id,
                action,
                new Date().toISOString()
            ]);
        } catch (error) {
            console.error('Erro ao salvar dados do membro:', error);
        }
    }

    async saveModerationAction(guildId, action, userId, moderatorId) {
        try {
            const query = `
                INSERT INTO moderation_analytics (guild_id, action, user_id, moderator_id, timestamp)
                VALUES (?, ?, ?, ?, ?)
            `;
            await this.client.database.run(query, [
                guildId,
                action,
                userId,
                moderatorId,
                new Date().toISOString()
            ]);
        } catch (error) {
            console.error('Erro ao salvar ação de moderação:', error);
        }
    }

    startStatsCollection() {
        // Salvar estatísticas a cada 5 minutos
        setInterval(() => {
            this.saveStatsToDatabase();
        }, 5 * 60 * 1000);

        // Reset estatísticas diárias à meia-noite
        setInterval(() => {
            this.resetDailyStats();
        }, 60 * 60 * 1000); // Verificar a cada hora
    }

    async saveStatsToDatabase() {
        for (const [guildId, stats] of this.stats) {
            try {
                const query = `
                    INSERT INTO guild_analytics (guild_id, stats, updated_at)
                    VALUES (?, ?, ?)
                    ON CONFLICT(guild_id)
                    DO UPDATE SET stats = ?, updated_at = ?
                `;
                const statsJson = JSON.stringify(stats);
                const now = new Date().toISOString();
                
                await this.client.database.run(query, [guildId, statsJson, now, statsJson, now]);
            } catch (error) {
                console.error('Erro ao salvar estatísticas:', error);
            }
        }
    }

    resetDailyStats() {
        const now = new Date();
        if (now.getHours() === 0 && now.getMinutes() === 0) {
            // Reset às 00:00
            this.dailyStats.clear();
            console.log('📊 Estatísticas diárias resetadas');
        }
    }

    // ===== CONTROLE DE ATIVAÇÃO ===== //
    async enableForGuild(guildId) {
        try {
            console.log(`📊 [ANALYTICS] Ativando analytics para guild ${guildId}`);

            // Inicializar estatísticas para o servidor se não existir
            if (!this.stats.has(guildId)) {
                this.stats.set(guildId, {
                    messages_total: 0,
                    commands_total: 0,
                    moderation_total: 0,
                    members_joined_total: 0,
                    members_left_total: 0
                });
            }

            // Primeiro garantir que o registro existe
            await this.ensureAnalyticsRecord(guildId);

            // Marcar como ativo no banco
            const query = `
                UPDATE guild_analytics
                SET enabled = 1, updated_at = ?
                WHERE guild_id = ?
            `;
            const now = new Date().toISOString();
            await this.client.database.run(query, [now, guildId]);

            console.log(`✅ [ANALYTICS] Analytics ativado para guild ${guildId}`);
            return { success: true };
        } catch (error) {
            console.error(`❌ [ANALYTICS] Erro ao ativar analytics:`, error);
            return { success: false, error: error.message };
        }
    }

    async disableForGuild(guildId) {
        try {
            console.log(`📊 [ANALYTICS] Desativando analytics para guild ${guildId}`);

            // Marcar como inativo no banco
            const query = `
                UPDATE guild_analytics
                SET enabled = 0, updated_at = ?
                WHERE guild_id = ?
            `;
            const now = new Date().toISOString();
            await this.client.database.run(query, [now, guildId]);

            console.log(`✅ [ANALYTICS] Analytics desativado para guild ${guildId}`);
            return { success: true };
        } catch (error) {
            console.error(`❌ [ANALYTICS] Erro ao desativar analytics:`, error);
            return { success: false, error: error.message };
        }
    }

    async isEnabledForGuild(guildId) {
        try {
            // Primeiro, garantir que o registro existe
            await this.ensureAnalyticsRecord(guildId);

            const query = `SELECT enabled FROM guild_analytics WHERE guild_id = ?`;
            const row = await this.client.database.get(query, [guildId]);
            return row ? Boolean(row.enabled) : false;
        } catch (error) {
            console.error(`❌ [ANALYTICS] Erro ao verificar status:`, error);
            // Tentar criar registro se não existir
            try {
                await this.ensureAnalyticsRecord(guildId);
                return false; // Padrão desabilitado
            } catch (createError) {
                console.error('❌ [ANALYTICS] Erro ao criar registro:', createError);
                return false;
            }
        }
    }

    async ensureAnalyticsRecord(guildId) {
        try {
            const query = `
                INSERT OR IGNORE INTO guild_analytics (guild_id, enabled, stats, updated_at)
                VALUES (?, 0, '{}', datetime('now'))
            `;
            await this.client.database.run(query, [guildId]);
        } catch (error) {
            console.error('❌ [ANALYTICS] Erro ao garantir registro:', error);
            throw error;
        }
    }

    // Sobrescrever métodos de tracking para verificar se está ativo
    trackMessage(message) {
        this.isEnabledForGuild(message.guild.id).then(enabled => {
            if (!enabled) return;

            const guildId = message.guild.id;
            const userId = message.author.id;
            const channelId = message.channel.id;

            this.incrementStat(guildId, 'messages_total');
            this.incrementStat(guildId, 'messages_today');
            this.incrementUserStat(guildId, userId, 'messages');
            this.incrementChannelStat(guildId, channelId, 'messages');

            // Rastrear atividade por hora
            const hour = new Date().getHours();
            this.incrementStat(guildId, `messages_hour_${hour}`);
        });
    }

    trackMemberJoin(member) {
        this.isEnabledForGuild(member.guild.id).then(enabled => {
            if (!enabled) return;

            const guildId = member.guild.id;

            this.incrementStat(guildId, 'members_joined_total');
            this.incrementStat(guildId, 'members_joined_today');

            // Salvar dados do membro
            this.saveMemberData(member, 'join');
        });
    }

    trackMemberLeave(member) {
        this.isEnabledForGuild(member.guild.id).then(enabled => {
            if (!enabled) return;

            const guildId = member.guild.id;

            this.incrementStat(guildId, 'members_left_total');
            this.incrementStat(guildId, 'members_left_today');

            // Salvar dados do membro
            this.saveMemberData(member, 'leave');
        });
    }

    trackCommand(interaction) {
        this.isEnabledForGuild(interaction.guild.id).then(enabled => {
            if (!enabled) return;

            const guildId = interaction.guild.id;
            const commandName = interaction.commandName;

            this.incrementStat(guildId, 'commands_total');
            this.incrementStat(guildId, 'commands_today');
            this.incrementStat(guildId, `command_${commandName}`);
        });
    }
}

module.exports = AnalyticsSystem;
