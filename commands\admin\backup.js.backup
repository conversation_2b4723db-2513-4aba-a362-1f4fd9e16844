/**
 * ========================================
 * COMANDO DE BACKUP & RESTORE
 * Gerenciar backups do servidor
 * ========================================
 */

const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('backup')
        .setDescription('💾 Gerenciar backups do servidor')
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Criar backup completo')
                .addBooleanOption(option =>
                    option
                        .setName('incluir-canais')
                        .setDescription('Incluir estrutura de canais')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('incluir-cargos')
                        .setDescription('Incluir cargos')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('incluir-economia')
                        .setDescription('Incluir dados de economia')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('incluir-niveis')
                        .setDescription('Incluir dados de níveis')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('restore')
                .setDescription('Restaurar de um backup')
                .addStringOption(option =>
                    option
                        .setName('id')
                        .setDescription('ID do backup')
                        .setRequired(true)
                )
                .addBooleanOption(option =>
                    option
                        .setName('confirmar')
                        .setDescription('Confirmar restauração (CUIDADO!)')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('Listar backups disponíveis')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Deletar um backup')
                .addStringOption(option =>
                    option
                        .setName('id')
                        .setDescription('ID do backup')
                        .setRequired(true)
                )
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'create':
                    await this.createBackup(interaction);
                    break;
                case 'restore':
                    await this.restoreBackup(interaction);
                    break;
                case 'list':
                    await this.listBackups(interaction);
                    break;
                case 'delete':
                    await this.deleteBackup(interaction);
                    break;
            }
        } catch (error) {
            console.error('Erro no comando backup:', error);
            
            const errorMessage = '❌ Ocorreu um erro ao executar o comando!';
            
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    },

    async createBackup(interaction) {
        await interaction.deferReply();

        const options = {
            includeChannels: interaction.options.getBoolean('incluir-canais') ?? true,
            includeRoles: interaction.options.getBoolean('incluir-cargos') ?? true,
            includeEconomy: interaction.options.getBoolean('incluir-economia') ?? true,
            includeLevels: interaction.options.getBoolean('incluir-niveis') ?? true,
            createdBy: interaction.user.id
        };

        try {
            const result = await interaction.client.backup.createFullBackup(
                interaction.guild.id,
                options
            );

            if (result.success) {
                const embed = {
                    color: 0x00ff7f,
                    title: '✅ Backup Criado',
                    description: result.message,
                    fields: [
                        {
                            name: '🆔 ID do Backup',
                            value: `\`${result.backup.id}\``,
                            inline: false
                        },
                        {
                            name: '📊 Componentes Incluídos',
                            value: [
                                options.includeChannels ? '✅ Canais' : '❌ Canais',
                                options.includeRoles ? '✅ Cargos' : '❌ Cargos',
                                options.includeEconomy ? '✅ Economia' : '❌ Economia',
                                options.includeLevels ? '✅ Níveis' : '❌ Níveis',
                                '✅ Configurações'
                            ].join('\n'),
                            inline: true
                        },
                        {
                            name: '📅 Criado em',
                            value: `<t:${Math.floor(new Date(result.backup.createdAt).getTime() / 1000)}:F>`,
                            inline: true
                        }
                    ],
                    footer: {
                        text: 'Use /backup restore para restaurar este backup'
                    }
                };

                await interaction.editReply({ embeds: [embed] });
            } else {
                await interaction.editReply({
                    content: result.message
                });
            }

        } catch (error) {
            console.error('Erro ao criar backup:', error);
            await interaction.editReply({
                content: '❌ Erro ao criar backup!'
            });
        }
    },

    async restoreBackup(interaction) {
        const backupId = interaction.options.getString('id');
        const confirmed = interaction.options.getBoolean('confirmar');

        if (!confirmed) {
            return await interaction.reply({
                content: '❌ Você deve confirmar a restauração! **ATENÇÃO:** Esta ação pode sobrescrever dados atuais.',
                ephemeral: true
            });
        }

        await interaction.deferReply();

        try {
            const result = await interaction.client.backup.restoreFromBackup(backupId);

            if (result.success) {
                const embed = {
                    color: 0x00ff7f,
                    title: '✅ Backup Restaurado',
                    description: result.message,
                    fields: [
                        {
                            name: '📊 Resultados',
                            value: result.results.map(r => 
                                `${r.success ? '✅' : '❌'} ${r.component}`
                            ).join('\n'),
                            inline: false
                        }
                    ],
                    footer: {
                        text: 'Restauração concluída'
                    }
                };

                await interaction.editReply({ embeds: [embed] });
            } else {
                await interaction.editReply({
                    content: result.message
                });
            }

        } catch (error) {
            console.error('Erro ao restaurar backup:', error);
            await interaction.editReply({
                content: '❌ Erro ao restaurar backup!'
            });
        }
    },

    async listBackups(interaction) {
        await interaction.deferReply();

        try {
            const backups = await interaction.client.backup.listBackups(interaction.guild.id);

            if (backups.length === 0) {
                return await interaction.editReply({
                    content: '❌ Nenhum backup encontrado para este servidor!'
                });
            }

            const embed = {
                color: 0x00ff7f,
                title: '💾 Backups Disponíveis',
                description: backups.map(backup => {
                    const createdAt = Math.floor(new Date(backup.created_at).getTime() / 1000);
                    const size = (backup.size / 1024).toFixed(2);
                    return `**${backup.type === 'full' ? '🔄' : '📋'} ${backup.type.toUpperCase()}**\n🆔 \`${backup.id}\`\n📅 <t:${createdAt}:R>\n👤 ${backup.created_by}\n💾 ${size} KB\n`;
                }).join('\n'),
                footer: {
                    text: `Total: ${backups.length} backup(s)`
                }
            };

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Erro ao listar backups:', error);
            await interaction.editReply({
                content: '❌ Erro ao listar backups!'
            });
        }
    },

    async deleteBackup(interaction) {
        const backupId = interaction.options.getString('id');

        await interaction.deferReply();

        try {
            const result = await interaction.client.backup.deleteBackup(backupId);

            if (result.success) {
                await interaction.editReply({
                    content: `✅ Backup \`${backupId}\` deletado com sucesso!`
                });
            } else {
                await interaction.editReply({
                    content: '❌ Erro ao deletar backup! Verifique se o ID está correto.'
                });
            }

        } catch (error) {
            console.error('Erro ao deletar backup:', error);
            await interaction.editReply({
                content: '❌ Erro ao deletar backup!'
            });
        }
    }
};
