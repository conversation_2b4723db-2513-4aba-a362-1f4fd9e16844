/**
 * ========================================
 * COMANDO: DEPLOY-LOCAL (REGISTRAR COMANDOS LOCALMENTE)
 * Registra comandos slash no servidor atual (aparece instantaneamente)
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { REST, Routes } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('deploy-local')
        .setNameLocalizations({
            'pt-BR': 'registrar-comandos'
        })
        .setDescription('Registra comandos slash neste servidor (aparece instantaneamente)')
        .setDescriptionLocalizations({
            'pt-BR': 'Registra todos os comandos slash neste servidor para aparecerem instantaneamente'
        })
        .setDefaultMemberPermissions(8), // ADMINISTRATOR
    
    category: 'admin',
    cooldown: 30,
    
    async execute(interaction) {
        try {
            await interaction.deferReply({ ephemeral: true });

            const guildId = interaction.guild.id;
            const clientId = interaction.client.user.id;

            // Coletar todos os comandos
            const commands = [];
            for (const command of interaction.client.commands.values()) {
                if (command.data) {
                    commands.push(command.data.toJSON());
                }
            }

            // Registrar comandos localmente
            const rest = new REST({ version: '10' }).setToken(process.env.DISCORD_TOKEN);

            console.log(`🎯 [DEPLOY] Registrando ${commands.length} comandos no servidor ${interaction.guild.name}`);

            const data = await rest.put(
                Routes.applicationGuildCommands(clientId, guildId),
                { body: commands }
            );

            // Criar embed de resultado
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('✅ Comandos Registrados Localmente')
                .setDescription(`${data.length} comandos slash registrados neste servidor!`)
                .addFields({
                    name: '⚡ Vantagem',
                    value: 'Comandos locais aparecem **instantaneamente** (não precisam esperar até 1 hora)',
                    inline: false
                })
                .addFields({
                    name: '📝 Comandos registrados',
                    value: `${data.length} comandos disponíveis\nDigite \`/\` para ver todos!`,
                    inline: false
                })
                .addFields({
                    name: '🔄 Como usar',
                    value: 'Agora você pode usar todos os comandos:\n• `/ban` - Banir usuários\n• `/timeout` - Silenciar usuários\n• `/setup-panels` - Criar painéis\n• `/dashboard` - Acessar dashboard\n• E muito mais!',
                    inline: false
                })
                .setFooter({
                    text: 'Nodex | Moderação • Registro Local de Comandos',
                    iconURL: interaction.client.user.displayAvatarURL()
                })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

            console.log(`✅ [DEPLOY] ${data.length} comandos registrados no servidor ${interaction.guild.name}`);

        } catch (error) {
            console.error('❌ [DEPLOY] Erro ao registrar comandos localmente:', error);
            
            if (interaction.deferred) {
                await interaction.editReply({
                    content: '❌ Erro ao registrar comandos localmente! Verifique as permissões do bot.'
                });
            } else {
                await interaction.reply({
                    content: '❌ Erro ao registrar comandos localmente!',
                    ephemeral: true
                });
            }
        }
    }
};
