/**
 * ========================================
 * COMANDO: SETUP-PANELS (CONFIGURAR PAINÉIS)
 * Cria painéis de demonstração para todos os sistemas
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-panels')
        .setNameLocalizations({
            'pt-BR': 'configurar-paineis'
        })
        .setDescription('Configura painéis de demonstração para todos os sistemas')
        .setDescriptionLocalizations({
            'pt-BR': 'Cria painéis de demonstração para música, economia, etc.'
        })
        .setDefaultMemberPermissions(8) // ADMINISTRATOR
        .addChannelOption(option =>
            option.setName('canal')
                .setDescription('Canal onde criar os painéis')
                .setDescriptionLocalizations({ 'pt-BR': 'Canal onde criar os painéis de demonstração' })
                .setRequired(true)
        ),
    
    category: 'admin',
    cooldown: 10,
    
    async execute(interaction) {
        try {
            const channel = interaction.options.getChannel('canal');
            const guildId = interaction.guild.id;

            await interaction.deferReply({ ephemeral: true });

            // Verificar permissões no canal
            const permissions = channel.permissionsFor(interaction.guild.members.me);
            if (!permissions.has(['SendMessages', 'EmbedLinks'])) {
                return await interaction.editReply({
                    content: '❌ Não tenho permissão para enviar mensagens nesse canal!'
                });
            }

            let results = [];



            // 🎵 PAINEL DE MÚSICA
            try {
                const musicResult = await interaction.client.music.setupMusicPanel(guildId, channel.id);
                if (musicResult.success) {
                    results.push('✅ Painel de Música criado');
                } else {
                    results.push('❌ Erro no painel de Música');
                }
            } catch (error) {
                results.push('❌ Erro no painel de Música');
                console.error('Erro ao criar painel de música:', error);
            }

            // 💰 PAINEL DE ECONOMIA
            try {
                const economyResult = await interaction.client.economy.setupEconomyPanel(guildId, channel.id);
                if (economyResult.success) {
                    results.push('✅ Painel de Economia criado');
                } else {
                    results.push('❌ Erro no painel de Economia');
                }
            } catch (error) {
                results.push('❌ Erro no painel de Economia');
                console.error('Erro ao criar painel de economia:', error);
            }

            // Criar embed de resultado
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('🎛️ Painéis Configurados')
                .setDescription(`Painéis criados no canal ${channel}:`)
                .addFields({
                    name: '📊 Resultados',
                    value: results.join('\n'),
                    inline: false
                })
                .setFooter({
                    text: 'Nodex | Moderação • Configuração de Painéis',
                    iconURL: interaction.client.user.displayAvatarURL()
                })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Erro no comando setup-panels:', error);
            
            if (interaction.deferred) {
                await interaction.editReply({
                    content: '❌ Erro ao configurar painéis!'
                });
            } else {
                await interaction.reply({
                    content: '❌ Erro ao configurar painéis!',
                    ephemeral: true
                });
            }
        }
    }
};
