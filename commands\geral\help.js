/**
 * ========================================
 * COMANDO: HELP
 * Sistema de ajuda completo
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');
const EmbedStyles = require('../../utils/EmbedStyles');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Sistema de ajuda e documentação do bot')
        .addStringOption(option =>
            option.setName('comando')
                .setDescription('Comando específico para obter ajuda')
                .setRequired(false)
                .setAutocomplete(true)),

    async execute(interaction) {
        const client = interaction.client;
        const specificCommand = interaction.options.getString('comando');

        try {
            if (specificCommand) {
                await showSpecificCommandHelp(interaction, specificCommand);
            } else {
                await showGeneralHelp(interaction);
            }
        } catch (error) {
            client.logger.error('Erro no comando help:', error);
            
            await interaction.reply({
                content: 'Ocorreu um erro ao mostrar a ajuda.',
                ephemeral: true
            });
        }
    },

    async autocomplete(interaction) {
        const focusedValue = interaction.options.getFocused();
        const commands = interaction.client.commands;
        
        const choices = [];
        for (const command of commands.values()) {
            if (command.data.name.startsWith(focusedValue.toLowerCase())) {
                choices.push({
                    name: command.data.name,
                    value: command.data.name
                });
            }
        }

        await interaction.respond(choices.slice(0, 25));
    }
};

/**
 * Mostra ajuda geral do bot
 */
async function showGeneralHelp(interaction) {
    const embedStyles = new EmbedStyles();
    const client = interaction.client;
    const guild = interaction.guild;

    // Obter configurações do servidor
    const guildConfig = client.database.getGuildConfig(guild?.id);
    const prefix = guildConfig?.prefix || '!';

    // Create professional corporate-style main embed
    const mainEmbed = {
        color: 0x00ff7f, // Nodex green
        title: SVGIcons.bold('Nodex | Moderação - Sistema de Ajuda'),
        description: `**Bem-vindo ao bot de moderação mais avançado do Discord**\n\n**Recursos Principais:**\n\n• **IA Integrada** para moderação inteligente\n• **Dashboard Web** profissional\n• **Sistema Anti-Raid** avançado\n• **100% em Português Brasileiro**\n\nSelecione uma categoria abaixo para explorar os comandos.`,
        fields: [
            {
                name: '**Como Usar o Bot**',
                value: `**Comandos Slash:** Digite ${SVGIcons.code('/')} e escolha um comando\n\n**Comandos de Texto:** Use o prefixo ${SVGIcons.code(prefix)} (fallback)\n\n**Dashboard Web:** Acesse ${SVGIcons.code('http://localhost:3000')}\n\n**Configuração:** Use ${SVGIcons.code('/config')} para configurar`,
                inline: false
            },
            {
                name: '**Estatísticas do Bot**',
                value: `**Comandos Totais:** ${client.commands.size}\n\n**Servidores Ativos:** ${client.guilds.cache.size}\n\n**Usuários Alcançados:** ${client.users.cache.size}\n\n**Uptime:** ${Math.floor(client.uptime / 1000 / 60)} minutos`,
                inline: true
            },
            {
                name: '**Status dos Sistemas**',
                value: `● **IA de Moderação**\n\n● **Sistema Anti-Raid**\n\n● **Logs Avançados**\n\n● **Dashboard Web**\n\n● **Sistema de Backup**`,
                inline: true
            },
            {
                name: '**Recursos Exclusivos**',
                value: `**IA Contextual** - Entende português brasileiro perfeitamente\n\n**Anti-Raid Inteligente** - Proteção automática contra invasões\n\n**Dashboard Profissional** - Interface web moderna e intuitiva\n\n**Sistema de Tickets** - Suporte organizado e eficiente\n\n**Backup Automático** - Segurança total dos dados\n\n**Logs Detalhados** - Auditoria completa de todas as ações`,
                inline: false
            }
        ],
        timestamp: new Date().toISOString(),
        footer: {
            text: 'Nodex | Moderação • Sistema de Ajuda Premium',
            icon_url: client.user.displayAvatarURL()
        },
        thumbnail: {
            url: client.user.displayAvatarURL({ dynamic: true })
        }
    };

    // Professional corporate category selection menu
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('help_category_select')
        .setPlaceholder('Selecione uma categoria para explorar comandos detalhados')
        .addOptions([
            {
                label: 'Moderação Premium',
                description: 'Comandos avançados de moderação com IA',
                value: 'moderation'
            },
            {
                label: 'Configuração Inteligente',
                description: 'Sistema de configuração avançado',
                value: 'config'
            },
            {
                label: 'Analytics & Informações',
                description: 'Estatísticas detalhadas e informações',
                value: 'info'
            },
            {
                label: 'Utilidades Avançadas',
                description: 'Ferramentas úteis e recursos especiais',
                value: 'utility'
            },
            {
                label: 'Administração Premium',
                description: 'Comandos administrativos avançados',
                value: 'admin'
            }
        ]);

    // Professional corporate action buttons
    const buttons = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setLabel('Dashboard Premium')
                .setStyle(ButtonStyle.Link)
                .setURL(`http://localhost:${process.env.WEB_PORT || 3000}/dashboard`),
            new ButtonBuilder()
                .setLabel('Suporte Técnico')
                .setStyle(ButtonStyle.Link)
                .setURL(`http://localhost:${process.env.WEB_PORT || 3000}/support`),
            new ButtonBuilder()
                .setLabel('Documentação')
                .setStyle(ButtonStyle.Link)
                .setURL(`http://localhost:${process.env.WEB_PORT || 3000}/docs`)
        );

    const row1 = new ActionRowBuilder().addComponents(selectMenu);
    const row2 = buttons;

    await interaction.reply({
        embeds: [mainEmbed],
        components: [row1, row2]
    });
}

/**
 * Mostra ajuda para um comando específico
 */
async function showSpecificCommandHelp(interaction, commandName) {
    const embedStyles = new EmbedStyles();
    const client = interaction.client;
    const command = client.commands.get(commandName);

    if (!command) {
        const errorEmbed = embedStyles.createErrorEmbed(
            'Comando Não Encontrado',
            `${embedStyles.format.bold('O comando especificado não existe!')}\n\n**Comando solicitado:** ${embedStyles.format.code(commandName)}\n\n${embedStyles.format.italic('Use /help para ver todos os comandos disponíveis.')}`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Create detailed professional command embed
    const commandEmbed = {
        color: parseInt(embedStyles.colors.info.replace('#', ''), 16),
        title: SVGIcons.bold(`Comando: /${command.data.name}`),
        description: `**${command.data.description || 'Sem descrição disponível'}**\n\nInformações detalhadas sobre este comando:`,
        fields: [],
        timestamp: new Date().toISOString(),
        footer: {
            text: 'Nodex | Moderação • Ajuda Detalhada Premium',
            icon_url: interaction.guild.iconURL()
        },
        thumbnail: {
            url: client.user.displayAvatarURL({ dynamic: true })
        }
    };

    // Add basic information
    commandEmbed.fields.push({
        name: '**Informações Básicas**',
        value: `**Nome:** ${embedStyles.format.code(command.data.name)}\n\n**Categoria:** ${command.category || 'Geral'}\n\n**Cooldown:** ${command.cooldown || 3} segundos`,
        inline: true
    });

    // Adicionar informações sobre opções
    if (command.data.options && command.data.options.length > 0) {
        const optionsText = command.data.options.map(option => {
            const required = option.required ? embedStyles.format.bold('[Obrigatório]') : embedStyles.format.italic('[Opcional]');
            const typeText = option.type ? ` (${option.type})` : '';
            return `**${option.name}**${typeText} ${required}\n\n${option.description}`;
        }).join('\n\n');

        commandEmbed.fields.push({
            name: '**Parâmetros do Comando**',
            value: optionsText,
            inline: false
        });
    }

    // Add permission information
    if (command.data.default_member_permissions) {
        commandEmbed.fields.push({
            name: '**Permissões Necessárias**',
            value: `${embedStyles.format.code('Permissões de Moderação')}\n\nApenas moderadores podem usar este comando.`,
            inline: true
        });
    }

    // Adicionar exemplos se existirem
    if (command.examples && command.examples.length > 0) {
        const examplesText = command.examples
            .slice(0, 3) // Mostrar apenas 3 exemplos
            .map(example => embedStyles.format.code(example))
            .join('\n');

        commandEmbed.fields.push({
            name: '**Exemplos de Uso**',
            value: examplesText,
            inline: false
        });
    }

    // Add aliases if they exist
    if (command.aliases && command.aliases.length > 0) {
        commandEmbed.fields.push({
            name: '**Comandos Alternativos**',
            value: command.aliases.map(alias => embedStyles.format.code(alias)).join(', '),
            inline: false
        });
    }

    // Add category information
    const categoryInfo = {
        moderacao: 'Comando de moderação premium com recursos avançados',
        geral: 'Comando de uso geral disponível para todos',
        configuracao: 'Comando de configuração para administradores',
        admin: 'Comando administrativo de alto nível'
    };

    if (categoryInfo[command.category]) {
        commandEmbed.fields.push({
            name: '**Informações da Categoria**',
            value: categoryInfo[command.category],
            inline: false
        });
    }

    await interaction.reply({
        embeds: [commandEmbed],
        ephemeral: true
    });
}

/**
 * Função para lidar com seleção de categoria (seria chamada pelo evento de interação)
 */
async function handleCategorySelection(interaction, category) {
    const client = interaction.client;
    const commands = client.commands;

    // Categorizar comandos dinamicamente baseado nos comandos reais
    const allCommands = Array.from(commands.values());
    let categoryCommands = [];

    switch (category) {
        case 'moderation':
            categoryCommands = allCommands.filter(cmd =>
                ['ban', 'kick', 'timeout', 'warn', 'mute', 'clear', 'unban'].includes(cmd.data.name) ||
                cmd.category === 'moderacao'
            );
            break;
        case 'config':
            categoryCommands = allCommands.filter(cmd =>
                ['config', 'setup'].includes(cmd.data.name) ||
                cmd.category === 'configuracao'
            );
            break;
        case 'info':
            categoryCommands = allCommands.filter(cmd =>
                ['help', 'stats', 'userinfo', 'serverinfo', 'botinfo'].includes(cmd.data.name) ||
                cmd.category === 'informacao'
            );
            break;
        case 'utility':
            categoryCommands = allCommands.filter(cmd =>
                ['ping', 'avatar', 'invite'].includes(cmd.data.name) ||
                cmd.category === 'utilidades'
            );
            break;
        case 'admin':
            categoryCommands = allCommands.filter(cmd =>
                ['eval', 'reload', 'shutdown'].includes(cmd.data.name) ||
                cmd.category === 'admin'
            );
            break;
        default:
            categoryCommands = [];
    }

    const categoryNames = {
        moderation: 'Comandos de Moderação',
        config: 'Comandos de Configuração',
        info: 'Comandos de Informação',
        utility: 'Comandos de Utilidade',
        admin: 'Comandos Administrativos'
    };

    if (categoryCommands.length === 0) {
        const embed = new EmbedBuilder()
            .setColor('#00ff7f')
            .setTitle(categoryNames[category] || 'Categoria')
            .setDescription('Esta categoria ainda não possui comandos disponíveis.\n\n**Em desenvolvimento!**\nNovos comandos serão adicionados em breve.')
            .addFields({
                name: 'Comandos Disponíveis Atualmente',
                value: Array.from(commands.keys()).map(name => `• \`/${name}\``).join('\n') || 'Nenhum',
                inline: false
            })
            .setTimestamp()
            .setFooter({ text: 'Nodex | Moderação' });

        const backButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('help_back')
                    .setLabel('◀ Voltar')
                    .setStyle(ButtonStyle.Secondary)
            );

        return await interaction.update({
            embeds: [embed],
            components: [backButton]
        });
    }

    const embed = new EmbedBuilder()
        .setColor('#00ff7f')
        .setTitle(categoryNames[category])
        .setDescription('Comandos disponíveis nesta categoria:')
        .setTimestamp()
        .setFooter({ text: 'Nodex | Moderação' });

    // Adicionar comandos da categoria
    const commandsText = categoryCommands.map(cmd => {
        const description = cmd.data.description || 'Sem descrição';
        return `**/${cmd.data.name}**- ${description}`;
    }).join('\n');

    embed.addFields({
        name: `${categoryCommands.length} Comando(s) Disponível(eis)`,
        value: commandsText,
        inline: false
    });

    // Adicionar exemplos se disponíveis
    if (categoryCommands.length > 0 && categoryCommands[0].examples) {
        embed.addFields({
            name: 'Exemplos',
            value: categoryCommands[0].examples.slice(0, 3).map(ex => `\`${ex}\``).join('\n'),
            inline: false
        });
    }

    // Professional back button
    const backButton = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('help_back')
                .setLabel('◀ Voltar')
                .setStyle(ButtonStyle.Secondary)
        );

    await interaction.update({
        embeds: [embed],
        components: [backButton]
    });
}

// Exportar função auxiliar para uso em outros módulos
module.exports.handleCategorySelection = handleCategorySelection;
module.exports.cooldown = 3;
module.exports.category = 'geral';
