/**
 * ========================================
 * COMANDO: USERINFO
 * Mostra informações de um usuário
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('userinfo')
        .setDescription('Mostra informações detalhadas de um usuário')
        .addUserOption(option =>
            option.setName('usuário')
                .setDescription('Usuário para ver informações (deixe vazio para ver suas próprias)')
                .setRequired(false)),

    async execute(interaction) {
        const client = interaction.client;
        const targetUser = interaction.options.getUser('usuário') || interaction.user;
        const targetMember = interaction.guild?.members.cache.get(targetUser.id);

        try {
            // Buscar informações do usuário
            const user = await client.users.fetch(targetUser.id, { force: true });

            // Determinar cor baseada no status
            let statusColor = '#747f8d'; // Offline/Invisível
            if (targetMember?.presence?.status === 'online') statusColor = '#43b581';
            else if (targetMember?.presence?.status === 'idle') statusColor = '#faa61a';
            else if (targetMember?.presence?.status === 'dnd') statusColor = '#f04747';

            // Criar embed principal
            const userEmbed = new EmbedBuilder()
                .setColor(statusColor)
                .setTitle(`Informações de ${user.displayName}`)
                .setThumbnail(user.displayAvatarURL({ size: 256 }))
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação' });

            // Informações básicas
            userEmbed.addFields(
                {
                    name: '️ Tag',
                    value: `\`${user.tag}\``,
                    inline: true
                },
                {
                    name: '🆔 ID',
                    value: `\`${user.id}\``,
                    inline: true
                },
                {
                    name: 'Conta Criada',
                    value: `<t:${Math.floor(user.createdTimestamp / 1000)}:F>\n(<t:${Math.floor(user.createdTimestamp / 1000)}:R>)`,
                    inline: false
                }
            );

            // Informações do servidor (se estiver em um servidor)
            if (targetMember && interaction.guild) {
                // Status e atividade
                const status = targetMember.presence?.status || 'offline';
                const statusEmojis = {
                    online: 'Online',
                    idle: 'Ausente',
                    dnd: 'Não Perturbe',
                    offline: 'Offline'
                };

                userEmbed.addFields(
                    {
                        name: 'Status',
                        value: statusEmojis[status],
                        inline: true
                    },
                    {
                        name: 'Entrou no Servidor',
                        value: `<t:${Math.floor(targetMember.joinedTimestamp / 1000)}:F>\n(<t:${Math.floor(targetMember.joinedTimestamp / 1000)}:R>)`,
                        inline: false
                    }
                );

                // Atividade atual
                if (targetMember.presence?.activities?.length > 0) {
                    const activity = targetMember.presence.activities[0];
                    let activityText = '';

                    switch (activity.type) {
                        case 0: // PLAYING
                            activityText = `Jogando**${activity.name}**`;
                            break;
                        case 1: // STREAMING
                            activityText = `Transmitindo**${activity.name}**`;
                            break;
                        case 2: // LISTENING
                            activityText = `Ouvindo**${activity.name}**`;
                            break;
                        case 3: // WATCHING
                            activityText = `Assistindo**${activity.name}**`;
                            break;
                        case 5: // COMPETING
                            activityText = `Competindo em**${activity.name}**`;
                            break;
                        default:
                            activityText = `**${activity.name}**`;
                    }

                    userEmbed.addFields({
                        name: 'Atividade',
                        value: activityText,
                        inline: true
                    });
                }

                // Cargos (máximo 10)
                const roles = targetMember.roles.cache
                    .filter(role => role.id !== interaction.guild.id)
                    .sort((a, b) => b.position - a.position)
                    .map(role => role.toString())
                    .slice(0, 10);

                if (roles.length > 0) {
                    userEmbed.addFields({
                        name: `Cargos [${targetMember.roles.cache.size - 1}]`,
                        value: roles.join(',') + (targetMember.roles.cache.size > 11 ? '...' : ''),
                        inline: false
                    });
                }

                // Permissões importantes
                const importantPerms = [
                    'Administrator',
                    'ManageGuild',
                    'ManageChannels',
                    'ManageRoles',
                    'BanMembers',
                    'KickMembers',
                    'ManageMessages',
                    'ModerateMembers'
                ];

                const userPerms = targetMember.permissions.toArray()
                    .filter(perm => importantPerms.includes(perm))
                    .map(perm => {
                        const permNames = {
                            Administrator: 'Administrador',
                            ManageGuild: 'Gerenciar Servidor',
                            ManageChannels: 'Gerenciar Canais',
                            ManageRoles: 'Gerenciar Cargos',
                            BanMembers: 'Banir Membros',
                            KickMembers: 'Expulsar Membros',
                            ManageMessages: 'Gerenciar Mensagens',
                            ModerateMembers: 'Moderar Membros'
                        };
                        return permNames[perm] || perm;
                    });

                if (userPerms.length > 0) {
                    userEmbed.addFields({
                        name: 'Permissões Importantes',
                        value: userPerms.join('\n'),
                        inline: false
                    });
                }

                // Informações de moderação (se houver)
                const modActions = client.database.getModerationHistory(targetUser.id, interaction.guild.id);
                if (modActions && Array.isArray(modActions) && modActions.length > 0) {
                    const warns = modActions.filter(action => action.action === 'warn').length;
                    const kicks = modActions.filter(action => action.action === 'kick').length;
                    const bans = modActions.filter(action => action.action === 'ban').length;
                    const timeouts = modActions.filter(action => action.action === 'timeout').length;

                    let modText = [];
                    if (warns > 0) modText.push(`${warns} advertência(s)`);
                    if (kicks > 0) modText.push(`${kicks} expulsão(ões)`);
                    if (bans > 0) modText.push(`${bans} banimento(s)`);
                    if (timeouts > 0) modText.push(`${timeouts} timeout(s)`);

                    if (modText.length > 0) {
                        userEmbed.addFields({
                            name: 'Histórico de Moderação',
                            value: modText.join('\n'),
                            inline: false
                        });
                    }
                }
            }

            // Badges do usuário
            const flags = user.flags?.toArray() || [];
            if (flags.length > 0) {
                const flagEmojis = {
                    Staff: '‍ Staff do Discord',
                    Partner: 'Parceiro do Discord',
                    Hypesquad: 'HypeSquad Events',
                    BugHunterLevel1: 'Bug Hunter',
                    BugHunterLevel2: 'Bug Hunter Nível 2',
                    HypesquadOnlineHouse1: 'HypeSquad Bravery',
                    HypesquadOnlineHouse2: 'HypeSquad Brilliance',
                    HypesquadOnlineHouse3: 'HypeSquad Balance',
                    PremiumEarlySupporter: 'Early Supporter',
                    VerifiedDeveloper: 'Desenvolvedor Verificado',
                    CertifiedModerator: 'Moderador Certificado',
                    BotHTTPInteractions: 'Bot HTTP'
                };

                const userFlags = flags.map(flag => flagEmojis[flag] || flag).join('\n');
                userEmbed.addFields({
                    name: 'Badges',
                    value: userFlags,
                    inline: false
                });
            }

            await interaction.reply({ embeds: [userEmbed] });

            client.logger.info(`Comando userinfo executado por ${interaction.user.tag} para ${user.tag}`);

        } catch (error) {
            client.logger.error('Erro no comando userinfo:', error);

            await interaction.reply({
                content: 'Ocorreu um erro ao buscar informações do usuário.',
                ephemeral: true
            });
        }
    },

    cooldown: 3,
    category: 'utilidades',
    examples: [
        '/userinfo',
        '/userinfo @usuário',
        '/userinfo usuário:123456789'
    ]
};
