/**
 * ========================================
 * COMANDO: PING
 * Mostra latência do bot
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ping')
        .setDescription('🏓 Mostra a latência do bot'),

    async execute(interaction) {
        const client = interaction.client;

        try {
            // Primeira resposta para calcular latência
            const sent = await interaction.reply({
                content: '🏓 Calculando ping...',
                fetchReply: true
            });

            // Calcular latências
            const roundtripLatency = sent.createdTimestamp - interaction.createdTimestamp;
            const websocketLatency = client.ws.ping;

            // Determinar qualidade da conexão
            let connectionQuality = '';
            let color = '';

            if (roundtripLatency < 100) {
                connectionQuality = '🟢 Excelente';
                color = '#00ff7f';
            } else if (roundtripLatency < 200) {
                connectionQuality = '🟡 Boa';
                color = '#ffff00';
            } else if (roundtripLatency < 500) {
                connectionQuality = '🟠 Regular';
                color = '#ffa500';
            } else {
                connectionQuality = '🔴 Ruim';
                color = '#ff0000';
            }

            // Embed com informações detalhadas
            const pingEmbed = new EmbedBuilder()
                .setColor(color)
                .setTitle('🏓 Pong!')
                .setDescription('Informações de latência do bot')
                .addFields(
                    {
                        name: '📡 Latência da API',
                        value: `\`${roundtripLatency}ms\``,
                        inline: true
                    },
                    {
                        name: '💓 Latência WebSocket',
                        value: `\`${websocketLatency}ms\``,
                        inline: true
                    },
                    {
                        name: '📊 Qualidade',
                        value: connectionQuality,
                        inline: true
                    },
                    {
                        name: '⏱️ Uptime',
                        value: `<t:${Math.floor((Date.now() - client.uptime) / 1000)}:R>`,
                        inline: true
                    },
                    {
                        name: '🖥️ Servidor',
                        value: `\`${process.platform}\``,
                        inline: true
                    },
                    {
                        name: '💾 Uso de Memória',
                        value: `\`${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB\``,
                        inline: true
                    }
                )
                .setThumbnail(client.user.displayAvatarURL())
                .setTimestamp()
                .setFooter({ 
                    text: 'Nodex | Moderação', 
                    iconURL: client.user.displayAvatarURL() 
                });

            // Adicionar informações do servidor se estiver em um
            if (interaction.guild) {
                pingEmbed.addFields({
                    name: '🌐 Servidor',
                    value: `${interaction.guild.name} (${interaction.guild.memberCount} membros)`,
                    inline: false
                });
            }

            // Atualizar a resposta
            await interaction.editReply({
                content: null,
                embeds: [pingEmbed]
            });

            // Log da execução
            client.logger.info(`Comando ping executado por ${interaction.user.tag} - Latência: ${roundtripLatency}ms`);

        } catch (error) {
            client.logger.error('Erro no comando ping:', error);

            await interaction.editReply({
                content: '❌ Ocorreu um erro ao calcular o ping.',
                embeds: []
            });
        }
    },

    cooldown: 5,
    category: 'utilidades',
    examples: ['/ping']
};
