/**
 * ========================================
 * EVENTO: MESSAGE CREATE
 * Executado quando uma mensagem é criada
 * ========================================
 */

const { Events } = require('discord.js');

module.exports = {
    name: Events.MessageCreate,
    async execute(message, client) {
        // Ignorar mensagens de bots e do sistema
        if (message.author.bot || message.system) return;

        // Ignorar mensagens em DM para auto-moderação
        if (!message.guild) return;

        // Debug removido - funcionando corretamente

        try {
            // Processar com auto-moderação
            if (client.autoMod) {
                await client.autoMod.processMessage(message);
            }

            // Processar com IA se habilitada
            if (client.aiMod && client.aiMod.enabled) {
                await processAIModeration(message, client);
            }

            // Processar respostas de captcha de verificação
            await processCaptchaResponse(message, client);

            // Processar comandos de texto (fallback para comandos slash)
            await processTextCommands(message, client);

            // Atualizar estatísticas do usuário
            await updateUserStats(message, client);

            // Log da mensagem se necessário
            await logMessage(message, client);

        } catch (error) {
            client.logger.error('Erro no evento messageCreate:', error, {
                guild: message.guild?.id,
                channel: message.channel.id,
                user: message.author.id,
                messageId: message.id
            });
        }
    }
};

/**
 * Processa respostas de captcha de verificação
 */
async function processCaptchaResponse(message, client) {
    try {
        // Verificar se há verificações pendentes
        if (!client.pendingVerifications || !client.pendingVerifications.has(message.author.id)) {
            return;
        }

        const pendingVerification = client.pendingVerifications.get(message.author.id);

        // Verificar se a mensagem é no servidor correto
        if (pendingVerification.guildId !== message.guild.id) {
            return;
        }

        // Verificar se não expirou (30 minutos padrão)
        const timeoutMs = (pendingVerification.config.timeout_minutes || 30) * 60 * 1000;
        if (Date.now() - pendingVerification.timestamp > timeoutMs) {
            client.pendingVerifications.delete(message.author.id);
            return;
        }

        const { captchaData, method, config } = pendingVerification;
        const userResponse = message.content.trim();
        let isCorrect = false;

        // Verificar resposta baseada no método
        if (method === 'captcha_math') {
            const userAnswer = parseInt(userResponse);
            isCorrect = userAnswer === captchaData.answer;

        } else if (method === 'captcha_text') {
            isCorrect = userResponse.toUpperCase() === captchaData.answer.toUpperCase();
        }

        if (isCorrect) {
            // Resposta correta - completar verificação
            await client.verificationSystem.completeVerification(
                message.member,
                config,
                method,
                'captcha_solved'
            );

            // Remover verificação pendente
            client.pendingVerifications.delete(message.author.id);

            // Deletar mensagem do usuário
            await message.delete().catch(() => {});

            // Enviar confirmação
            const { EmbedBuilder } = require('discord.js');
            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Captcha Correto!')
                .setDescription('Parabéns! Você foi verificado com sucesso.')
                .setColor('#00ff7f')
                .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                .setTimestamp();

            const confirmMsg = await message.channel.send({ embeds: [successEmbed] });

            // Deletar confirmação após 5 segundos
            setTimeout(() => {
                confirmMsg.delete().catch(() => {});
            }, 5000);

        } else {
            // Resposta incorreta - incrementar tentativas
            const attempts = (pendingVerification.attempts || 0) + 1;
            const maxAttempts = config.max_attempts || 3;

            if (attempts >= maxAttempts) {
                // Máximo de tentativas atingido
                client.pendingVerifications.delete(message.author.id);

                // Log da falha
                client.database.db.prepare(`
                    INSERT INTO verification_logs
                    (guild_id, user_id, action, method, details, timestamp)
                    VALUES (?, ?, 'failed_max_attempts', ?, ?, CURRENT_TIMESTAMP)
                `).run(message.guild.id, message.author.id, method, JSON.stringify({
                    attempts: attempts,
                    max_attempts: maxAttempts,
                    last_response: userResponse
                }));

                await message.delete().catch(() => {});

                const failEmbed = new EmbedBuilder()
                    .setTitle('❌ Tentativas Esgotadas')
                    .setDescription(`Você excedeu o número máximo de tentativas (${maxAttempts}).`)
                    .addFields({
                        name: '📞 Próximo Passo',
                        value: 'Entre em contato com um moderador para assistência.',
                        inline: false
                    })
                    .setColor('#ff4757')
                    .setFooter({ text: 'Nodex | Moderação' })
                    .setTimestamp();

                const failMsg = await message.channel.send({ embeds: [failEmbed] });
                setTimeout(() => failMsg.delete().catch(() => {}), 10000);

            } else {
                // Ainda há tentativas - atualizar contador
                pendingVerification.attempts = attempts;
                client.pendingVerifications.set(message.author.id, pendingVerification);

                await message.delete().catch(() => {});

                const retryEmbed = new EmbedBuilder()
                    .setTitle('❌ Resposta Incorreta')
                    .setDescription(`Tente novamente. Tentativas restantes: ${maxAttempts - attempts}`)
                    .setColor('#ffa500')
                    .setFooter({ text: 'Nodex | Moderação' })
                    .setTimestamp();

                const retryMsg = await message.channel.send({ embeds: [retryEmbed] });
                setTimeout(() => retryMsg.delete().catch(() => {}), 5000);
            }
        }

    } catch (error) {
        console.error('❌ [VERIFICATION] Erro ao processar resposta de captcha:', error);
    }
}

/**
 * Processa comandos de texto (fallback)
 */
async function processTextCommands(message, client) {
    try {
        // Obter configurações do servidor do cache
        const guildConfig = client.guildConfigs?.get(message.guild.id);
        const prefix = guildConfig?.prefix || '!';

        // Verificar se a mensagem começa com o prefixo
        if (!message.content.startsWith(prefix)) return;

        // Extrair comando e argumentos
        const args = message.content.slice(prefix.length).trim().split(/ +/);
        const commandName = args.shift().toLowerCase();

        // Verificar se o comando existe
        const command = client.commands.get(commandName) || 
                       client.commands.find(cmd => cmd.aliases && cmd.aliases.includes(commandName));

        if (!command) return;

        // Verificar se o comando suporta execução por texto
        if (!command.executeText) {
            return await message.reply({
                content: `❌ Este comando só está disponível como comando slash. Use \`/${commandName}\` em vez disso.`
            });
        }

        // Verificar cooldown
        const cooldownResult = checkTextCommandCooldown(message, client, command);
        if (cooldownResult.onCooldown) {
            return await message.reply({
                content: `⏰ Aguarde ${cooldownResult.timeLeft} antes de usar este comando novamente.`
            });
        }

        // Verificar permissões
        if (command.data.default_member_permissions) {
            const userPermissions = message.member.permissions;
            const requiredPermissions = command.data.default_member_permissions;
            
            if (!userPermissions.has(requiredPermissions)) {
                return await message.reply({
                    content: '❌ Você não tem permissão para usar este comando!'
                });
            }
        }

        // Executar comando
        const startTime = Date.now();
        await command.executeText(message, args);
        const executionTime = Date.now() - startTime;

        // Log do comando
        client.logger.command(
            commandName,
            message.author.id,
            message.guild.id,
            true
        );

        // Log de performance se demorou muito
        if (executionTime > 3000) {
            client.logger.performance(
                `Comando texto ${commandName}`,
                executionTime,
                {
                    user: message.author.id,
                    guild: message.guild.id
                }
            );
        }

        // Atualizar cooldown
        setTextCommandCooldown(message, client, command);

    } catch (error) {
        client.logger.error('Erro no processamento de comando de texto:', error, {
            guild: message.guild.id,
            user: message.author.id,
            content: message.content
        });

        await message.reply({
            content: '❌ Ocorreu um erro ao executar este comando.'
        }).catch(() => {});
    }
}

/**
 * Atualiza estatísticas do usuário
 */
async function updateUserStats(message, client) {
    try {
        const guildId = message.guild.id;
        const userId = message.author.id;

        // Atualizar contador de mensagens
        const stmt = client.database.db.prepare(`
            INSERT OR REPLACE INTO user_stats 
            (guild_id, user_id, messages_sent, last_activity)
            VALUES (?, ?, 
                COALESCE((SELECT messages_sent FROM user_stats WHERE guild_id = ? AND user_id = ?), 0) + 1,
                CURRENT_TIMESTAMP)
        `);
        
        stmt.run(guildId, userId, guildId, userId);

        client.logger.debug(`Estatísticas atualizadas para ${message.author.tag}`);

    } catch (error) {
        client.logger.error('Erro ao atualizar estatísticas do usuário:', error);
    }
}

/**
 * Registra mensagem se necessário
 */
async function logMessage(message, client) {
    try {
        // Verificar se logs de mensagens estão ativados
        const guildConfig = client.guildConfigs?.get(message.guild.id);
        if (!guildConfig?.log_enabled) return;

        // Verificar se o canal está na lista de ignorados
        const ignoredChannels = guildConfig?.ignored_channels || [];
        if (ignoredChannels.includes(message.channel.id)) return;

        // Log básico da mensagem (sem conteúdo por privacidade)
        client.database.logModerationAction(
            message.guild.id, message.author.id, null, 'message_sent',
            'Mensagem enviada', null, message.channel.id, message.id,
            {
                hasAttachments: message.attachments.size > 0,
                hasEmbeds: message.embeds.length > 0,
                length: message.content.length,
                mentions: {
                    users: message.mentions.users.size,
                    roles: message.mentions.roles.size,
                    everyone: message.mentions.everyone
                }
            }
        );

    } catch (error) {
        client.logger.error('Erro ao registrar mensagem:', error);
    }
}

/**
 * Verifica cooldown para comandos de texto
 */
function checkTextCommandCooldown(message, client, command) {
    if (!command.cooldown) return { onCooldown: false };

    const cooldowns = client.cooldowns;
    const commandName = command.data.name;
    const cooldownKey = `text_${commandName}`;

    if (!cooldowns.has(cooldownKey)) {
        cooldowns.set(cooldownKey, new Map());
    }

    const now = Date.now();
    const timestamps = cooldowns.get(cooldownKey);
    const cooldownAmount = command.cooldown * 1000;

    if (timestamps.has(message.author.id)) {
        const expirationTime = timestamps.get(message.author.id) + cooldownAmount;

        if (now < expirationTime) {
            const timeLeft = Math.ceil((expirationTime - now) / 1000);
            return {
                onCooldown: true,
                timeLeft: `${timeLeft} segundo(s)`
            };
        }
    }

    return { onCooldown: false };
}

/**
 * Define cooldown para comandos de texto
 */
function setTextCommandCooldown(message, client, command) {
    if (!command.cooldown) return;

    const cooldowns = client.cooldowns;
    const commandName = command.data.name;
    const cooldownKey = `text_${commandName}`;
    const timestamps = cooldowns.get(cooldownKey);
    
    timestamps.set(message.author.id, Date.now());
    
    // Remover cooldown após expirar
    setTimeout(() => {
        timestamps.delete(message.author.id);
    }, command.cooldown * 1000);
}

/**
 * Detecta e processa menções ao bot
 */
async function processBotMentions(message, client) {
    try {
        // Verificar se o bot foi mencionado
        if (!message.mentions.has(client.user)) return;

        // Verificar se é apenas uma menção (sem outros conteúdos)
        const content = message.content.replace(/<@!?\d+>/g, '').trim();
        
        if (content === '') {
            // Resposta amigável quando apenas mencionado
            const guildConfig = client.guildConfigs?.get(message.guild.id);
            const prefix = guildConfig?.prefix || '!';

            const responses = [
                `Olá ${message.author}! 👋`,
                `Precisa de ajuda? Use \`${prefix}help\` ou \`/help\``,
                `Estou aqui para moderar seu servidor! 🛡️`,
                `Use \`/config\` para me configurar! ⚙️`
            ];

            const randomResponse = responses[Math.floor(Math.random() * responses.length)];
            
            await message.reply({
                content: randomResponse,
                allowedMentions: { repliedUser: false }
            });
        }

    } catch (error) {
        client.logger.error('Erro ao processar menção ao bot:', error);
    }
}

/**
 * Detecta comandos inválidos e sugere alternativas
 */
async function suggestCommands(message, client) {
    try {
        const guildConfig = client.guildConfigs?.get(message.guild.id);
        const prefix = guildConfig?.prefix || '!';

        if (!message.content.startsWith(prefix)) return;

        const args = message.content.slice(prefix.length).trim().split(/ +/);
        const attemptedCommand = args[0].toLowerCase();

        // Lista de comandos comuns e suas sugestões
        const suggestions = {
            'ban': '/ban',
            'kick': '/kick',
            'mute': '/mute',
            'warn': '/warn',
            'config': '/config',
            'help': '/help',
            'clear': '/clear',
            'purge': '/clear',
            'delete': '/clear',
            'timeout': '/mute',
            'silence': '/mute'
        };

        if (suggestions[attemptedCommand]) {
            await message.reply({
                content: `💡 Você quis dizer \`${suggestions[attemptedCommand]}\`? Este comando está disponível como comando slash!`,
                allowedMentions: { repliedUser: false }
            });
        }

    } catch (error) {
        client.logger.error('Erro ao sugerir comandos:', error);
    }
}

/**
 * Processa reações automáticas
 */
async function processAutoReactions(message, client) {
    try {
        const guildConfig = client.guildConfigs?.get(message.guild.id);
        if (!guildConfig?.auto_reactions_enabled) return;

        // Verificar se auto-reações estão configuradas
        const autoReactions = guildConfig?.auto_reactions || {};
        
        // Reações baseadas em palavras-chave
        const content = message.content.toLowerCase();
        
        for (const [keyword, emoji] of Object.entries(autoReactions)) {
            if (content.includes(keyword.toLowerCase())) {
                try {
                    await message.react(emoji);
                } catch (error) {
                    client.logger.debug(`Erro ao adicionar reação ${emoji}:`, error.message);
                }
            }
        }

        // Reações especiais para o bot
        if (content.includes('nova moderação') || content.includes('novamod')) {
            try {
                await message.react('🤖');
            } catch (error) {
                client.logger.debug('Erro ao adicionar reação especial:', error.message);
            }
        }

    } catch (error) {
        client.logger.error('Erro ao processar auto-reações:', error);
    }
}

/**
 * Processa moderação por IA
 */
async function processAIModeration(message, client) {
    try {
        // Verificar se a IA está habilitada para este servidor (cache primeiro)
        let guildConfig;
        let settings;

        if (client.guildConfigs && client.guildConfigs.has(message.guild.id)) {
            // Usar configurações do cache
            settings = client.guildConfigs.get(message.guild.id);
            guildConfig = await client.database.getGuildConfig(message.guild.id); // Para logs
        } else {
            // Carregar do banco e cachear
            guildConfig = await client.database.getGuildConfig(message.guild.id);

            // Parse settings corretamente
            try {
                if (typeof guildConfig?.settings === 'string') {
                    settings = JSON.parse(guildConfig.settings);
                } else if (typeof guildConfig?.settings === 'object') {
                    settings = guildConfig.settings;
                } else {
                    settings = {};
                }
            } catch (error) {
                console.error('Erro ao fazer parse das configurações:', error);
                settings = {};
            }

            // Cachear configurações
            if (!client.guildConfigs) {
                client.guildConfigs = new Map();
            }
            client.guildConfigs.set(message.guild.id, settings);
        }

        if (!settings.ai_moderation_enabled) {
            console.log(`[DEBUG] IA desabilitada no servidor ${message.guild.name}`);
            return;
        }

        console.log(`[DEBUG] IA ativa! Analisando mensagem: "${message.content}"`);
        console.log(`[DEBUG] Usuário: ${message.author.tag}, Canal: ${message.channel.name}`);

        // Ignorar mensagens muito curtas ou de moderadores
        if (message.content.length < 3) return;
        if (message.member.permissions.has('ManageMessages')) return;

        // Analisar mensagem (IA ou análise básica)
        let analysis;

        if (client.aiMod && client.aiMod.enabled) {
            try {
                console.log(`[DEBUG] Chamando IA para análise...`);
                analysis = await client.aiMod.analyzeMessage(message, message.guild.id);
                console.log(`[DEBUG] Resultado da IA:`, analysis);
            } catch (error) {
                console.log(`[DEBUG] IA falhou, usando análise básica:`, error.message);
                client.logger.warn('IA falhou, usando análise básica:', error.message);
                analysis = null;
            }
        }

        // Se IA não funcionou, usar análise básica
        if (!analysis) {
            console.log(`[DEBUG] Usando análise básica para: "${message.content}"`);
            analysis = await basicContentAnalysis(message.content);
            console.log(`[DEBUG] Resultado análise básica:`, analysis);
            if (!analysis || analysis.overall_risk < 0.6) {
                console.log(`[DEBUG] Risco muito baixo (${analysis?.overall_risk}), ignorando`);
                return; // Só agir em casos claros
            }
        }

        client.logger.debug(`Análise de IA para mensagem de ${message.author.tag}:`, {
            toxicity: analysis.toxicity,
            spam: analysis.spam,
            harassment: analysis.harassment,
            hate_speech: analysis.hate_speech,
            overall_risk: analysis.overall_risk,
            action_recommended: analysis.action_recommended
        });

        // Obter histórico do usuário para sistema de segunda chance
        const userHistory = client.database.getModerationHistory(message.author.id, message.guild.id);

        // Determinar ação baseada na análise e histórico
        const action = client.aiMod.determineAction(analysis, userHistory);
        if (!action) return;

        // Executar ação recomendada
        await executeAIAction(message, client, action, analysis);

    } catch (error) {
        client.logger.error('Erro no processamento de IA:', error);
    }
}

/**
 * Executa ação recomendada pela IA
 */
async function executeAIAction(message, client, action, analysis) {
    try {
        const { EmbedBuilder } = require('discord.js');

        switch (action.action) {
            case 'delete':
                // Deletar mensagem
                await message.delete();

                // Notificar usuário
                try {
                    const dmEmbed = new EmbedBuilder()
                        .setColor('#ff6b35')
                        .setTitle('🤖 Mensagem Removida pela IA')
                        .setDescription(`Sua mensagem foi removida automaticamente no servidor **${message.guild.name}**`)
                        .addFields(
                            { name: '📋 Motivo', value: action.reason, inline: false },
                            { name: '🧠 Análise', value: analysis.reason || 'Conteúdo inadequado detectado', inline: false },
                            { name: '⚠️ Aviso', value: 'Evite enviar conteúdo similar no futuro.', inline: false }
                        )
                        .setTimestamp()
                        .setFooter({ text: 'Nodex | Moderação - IA' });

                    await message.author.send({ embeds: [dmEmbed] });
                } catch (error) {
                    // Usuário não pode receber DM
                }

                // Log da ação
                client.database.logModerationAction(
                    message.guild.id, message.author.id, client.user.id,
                    'ai_delete', action.reason, null, message.channel.id, message.id,
                    { automatic: true, ai_analysis: analysis }
                );

                client.logger.info(`IA deletou mensagem de ${message.author.tag}: ${action.reason}`);
                break;

            case 'warn':
                // Aplicar advertência automática
                client.database.logModerationAction(
                    message.guild.id, message.author.id, client.user.id,
                    'warn', action.reason, null, message.channel.id, message.id,
                    { automatic: true, ai_analysis: analysis }
                );

                // Notificar usuário
                try {
                    const warnEmbed = new EmbedBuilder()
                        .setColor('#ffa500')
                        .setTitle('⚠️ Advertência Automática')
                        .setDescription(`Você recebeu uma advertência automática no servidor **${message.guild.name}**`)
                        .addFields(
                            { name: '📋 Motivo', value: action.reason, inline: false },
                            { name: '🧠 Análise', value: analysis.reason || 'Comportamento inadequado detectado', inline: false }
                        )
                        .setTimestamp()
                        .setFooter({ text: 'Nodex | Moderação - IA' });

                    // Adicionar aviso sobre segunda chance
                    if (action.isFirstChance) {
                        warnEmbed.addFields({
                            name: '🎯 Segunda Chance',
                            value: '⚠️ **Esta é sua primeira advertência!**\nSe você repetir comportamento similar nas próximas 24 horas, receberá timeout automático.',
                            inline: false
                        });
                    } else {
                        warnEmbed.addFields({
                            name: '💡 Dica',
                            value: 'Mantenha um comportamento respeitoso para evitar punições.',
                            inline: false
                        });
                    }

                    await message.author.send({ embeds: [warnEmbed] });
                } catch (error) {
                    // Usuário não pode receber DM
                }

                client.logger.info(`IA advertiu ${message.author.tag}: ${action.reason}`);
                break;

            case 'timeout':
                // Aplicar timeout
                const duration = action.duration || 10 * 60 * 1000; // 10 minutos padrão

                try {
                    await message.member.timeout(duration, `IA: ${action.reason}`);
                    await message.delete();

                    // Notificar usuário
                    try {
                        const timeoutEmbed = new EmbedBuilder()
                            .setColor('#ff0000')
                            .setTitle('⏰ Timeout Automático')
                            .setDescription(`Você foi colocado em timeout no servidor **${message.guild.name}**`)
                            .addFields(
                                { name: '📋 Motivo', value: action.reason, inline: false },
                                { name: '⏱️ Duração', value: `${Math.floor(duration / 60000)} minuto(s)`, inline: true },
                                { name: '🧠 Análise', value: analysis.reason || 'Comportamento tóxico detectado', inline: false }
                            )
                            .setTimestamp()
                            .setFooter({ text: 'Nodex | Moderação - IA' });

                        // Adicionar informação sobre segunda chance
                        if (action.isSecondChance) {
                            timeoutEmbed.addFields({
                                name: '⚠️ Reincidência',
                                value: 'Você já havia sido advertido nas últimas 24 horas. Por isso, recebeu timeout automático.',
                                inline: false
                            });
                        }

                        await message.author.send({ embeds: [timeoutEmbed] });
                    } catch (dmError) {
                        // Usuário não pode receber DM
                    }

                    // Log da ação
                    client.database.logModerationAction(
                        message.guild.id, message.author.id, client.user.id,
                        'timeout', action.reason, duration, message.channel.id, message.id,
                        { automatic: true, ai_analysis: analysis }
                    );

                    client.logger.info(`IA aplicou timeout em ${message.author.tag}: ${action.reason}`);

                } catch (error) {
                    client.logger.error(`Erro ao aplicar timeout automático:`, error);
                }
                break;

            case 'ban':
                // Ban automático (apenas para casos extremos)
                try {
                    // Mensagem pública no canal ANTES do ban
                    const banPublicEmbed = new EmbedBuilder()
                        .setColor('#8b0000')
                        .setDescription(`🔨 **Banimento aplicado**\n👤 **Usuário:** ${message.author}\n📋 **Motivo:** ${action.reason}\n⚠️ **Severidade:** Extrema`)
                        .setTimestamp()
                        .setFooter({ text: 'Nodex | Moderação' });

                    await message.channel.send({ embeds: [banPublicEmbed] });

                    await message.member.ban({
                        reason: `IA: ${action.reason}`,
                        deleteMessageDays: 1
                    });

                    // Log da ação
                    client.database.logModerationAction(
                        message.guild.id, message.author.id, client.user.id,
                        'ban', action.reason, null, message.channel.id, message.id,
                        { automatic: true, ai_analysis: analysis }
                    );

                    client.logger.warn(`IA baniu ${message.author.tag}: ${action.reason}`);

                } catch (error) {
                    client.logger.error(`Erro ao aplicar ban automático:`, error);
                }
                break;
        }

        // Log no canal de moderação se configurado
        const logChannel = message.guild.channels.cache.get(guildConfig?.log_channel_id);

        if (logChannel) {
            const logEmbed = new EmbedBuilder()
                .setColor(action.severity === 'high' ? '#ff0000' : action.severity === 'medium' ? '#ffa500' : '#ffff00')
                .setTitle('🤖 Ação Automática da IA')
                .setThumbnail(message.author.displayAvatarURL())
                .addFields(
                    { name: '👤 Usuário', value: `${message.author.tag} (${message.author.id})`, inline: true },
                    { name: '📺 Canal', value: `${message.channel} (${message.channel.name})`, inline: true },
                    { name: '🔨 Ação', value: action.action.toUpperCase(), inline: true },
                    { name: '📋 Motivo', value: action.reason, inline: false },
                    { name: '📝 Mensagem Original', value: `\`\`\`${message.content.length > 500 ? message.content.substring(0, 497) + '...' : message.content}\`\`\``, inline: false },
                    { name: '📊 Análise Detalhada da IA', value: `🔥 **Toxicidade:** ${(analysis.toxicity * 100).toFixed(1)}%\n💬 **Spam:** ${(analysis.spam * 100).toFixed(1)}%\n😠 **Assédio:** ${(analysis.harassment * 100).toFixed(1)}%\n💀 **Discurso de Ódio:** ${(analysis.hate_speech * 100).toFixed(1)}%\n⚠️ **Risco Geral:** ${(analysis.overall_risk * 100).toFixed(1)}%`, inline: true },
                    { name: '⚙️ Sistema', value: `**Modelo:** ${analysis.fallback ? 'Análise Básica' : 'DeepSeek V3'}\n**Severidade:** ${action.severity}\n**Automático:** Sim`, inline: true },
                    { name: '📅 Data e Hora', value: `<t:${Math.floor(Date.now() / 1000)}:F>\n<t:${Math.floor(Date.now() / 1000)}:R>`, inline: true }
                )
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação - Sistema de IA' });

            // Adicionar duração se for timeout
            if (action.action === 'timeout' && action.duration) {
                logEmbed.addFields({
                    name: '⏱️ Duração do Timeout',
                    value: `${Math.floor(action.duration / 60000)} minuto(s)`,
                    inline: true
                });
            }

            // Adicionar ID da mensagem
            logEmbed.addFields({
                name: '🆔 ID da Mensagem',
                value: `\`${message.id}\``,
                inline: true
            });

            await logChannel.send({ embeds: [logEmbed] });
        }

    } catch (error) {
        client.logger.error('Erro ao executar ação da IA:', error);
    }
}

/**
 * Análise básica de conteúdo (fallback quando IA não funciona)
 */
async function basicContentAnalysis(content) {
    const text = content.toLowerCase();

    // Ataques pessoais diretos (SEMPRE punir)
    const personalAttacks = [
        'você é um', 'você é uma', 'tu é um', 'tu é uma',
        'vai se foder', 'vai tomar no cu', 'vai se fuder',
        'seu merda', 'sua merda', 'seu lixo', 'sua lixo',
        'seu idiota', 'sua idiota', 'seu burro', 'sua burra'
    ];

    // Lista de termos racistas (SEMPRE punir)
    const racistTerms = [
        'preto', 'negro', 'macaco', 'primata', 'escravo', 'senzala',
        'filho da puta preto', 'filho da puta negro', 'macaco preto'
    ];

    // Palavras ofensivas em contexto de ataque
    const offensiveInContext = [
        'merda', 'lixo', 'idiota', 'burro', 'imbecil', 'retardado',
        'mongolóide', 'viado', 'bicha', 'fdp', 'filho da puta'
    ];

    // Palavrões casuais (score baixo, não punir)
    const casualSwears = [
        'porra', 'caralho', 'cu', 'merda', 'bosta', 'cacete', 'buceta',
        'piroca', 'pau', 'rola', 'pinto', 'xoxota', 'ppk', 'corno',
        'otário', 'babaca', 'imbecil', 'idiota', 'burro'
    ];

    // Lista de palavras de spam
    const spamWords = [
        'discord.gg', 'https://', 'http://', 'www.', '.com', '.net', '.org',
        'free nitro', 'nitro gratis', 'hack', 'generator', 'gerador'
    ];

    let toxicity = 0;
    let spam = 0;
    let harassment = 0;
    let hate_speech = 0;
    let overall_risk = 0;

    // Verificar ataques pessoais diretos (SEMPRE punir)
    for (const attack of personalAttacks) {
        if (text.includes(attack)) {
            toxicity += 0.7; // Score alto para ataques pessoais
            harassment += 0.5;
            overall_risk += 0.6;
        }
    }

    // Verificar termos racistas (SEMPRE punir severamente)
    for (const term of racistTerms) {
        if (text.includes(term)) {
            console.log(`[RACISMO DETECTADO] Termo "${term}" encontrado em: "${text}"`);
            hate_speech += 0.9; // Score muito alto
            toxicity += 0.8;
            overall_risk += 0.9; // Risco máximo
        }
    }

    // Verificar palavras ofensivas em contexto de ataque
    let hasPersonalPronoun = text.includes('você') || text.includes('tu') || text.includes('seu') || text.includes('sua');
    if (hasPersonalPronoun) {
        for (const word of offensiveInContext) {
            if (text.includes(word)) {
                toxicity += 0.6; // Score alto quando direcionado
                harassment += 0.4;
                overall_risk += 0.5;
            }
        }
    }

    // Palavrões casuais - score muito baixo (não punir)
    for (const word of casualSwears) {
        if (text.includes(word)) {
            toxicity += 0.1; // Score muito baixo
            overall_risk += 0.05; // Quase nenhum risco
        }
    }

    // Verificar spam
    for (const word of spamWords) {
        if (text.includes(word)) {
            spam += 0.4;
            overall_risk += 0.2;
        }
    }

    // Verificar CAPS (gritaria)
    const capsRatio = (content.match(/[A-Z]/g) || []).length / content.length;
    if (capsRatio > 0.7 && content.length > 10) {
        harassment += 0.3;
        overall_risk += 0.15;
    }

    // Verificar repetição excessiva
    const words = text.split(' ');
    const uniqueWords = [...new Set(words)];
    if (words.length > 5 && uniqueWords.length / words.length < 0.3) {
        spam += 0.5;
        overall_risk += 0.2;
    }

    // Limitar valores entre 0 e 1
    toxicity = Math.min(toxicity, 1);
    spam = Math.min(spam, 1);
    harassment = Math.min(harassment, 1);
    hate_speech = Math.min(hate_speech, 1);
    overall_risk = Math.min(overall_risk, 1);

    // Determinar ação recomendada (rigoroso com racismo e ataques pessoais)
    let action_recommended = 'none';
    let reason = 'Conteúdo adequado';

    if (hate_speech >= 0.8) {
        action_recommended = 'ban';
        reason = 'Discurso de ódio/racismo detectado';
    } else if (toxicity >= 0.7) {
        action_recommended = 'timeout';
        reason = 'Ataque pessoal direto detectado';
    } else if (overall_risk >= 0.9) {
        action_recommended = 'timeout';
        reason = 'Conteúdo altamente problemático detectado';
    } else if (hate_speech >= 0.4) {
        action_recommended = 'timeout';
        reason = 'Conteúdo discriminatório detectado';
    } else if (toxicity >= 0.5) {
        action_recommended = 'warn';
        reason = 'Linguagem ofensiva direcionada detectada';
    } else if (overall_risk >= 0.6 && harassment >= 0.4) {
        action_recommended = 'warn';
        reason = 'Comportamento inadequado detectado';
    } else if (spam >= 0.7) {
        action_recommended = 'delete';
        reason = 'Spam detectado';
    }

    return {
        toxicity,
        spam,
        harassment,
        hate_speech,
        sexual_content: 0,
        violence: 0,
        self_harm: 0,
        overall_risk,
        action_recommended,
        reason,
        fallback: true // Indica que foi análise básica
    };
}
