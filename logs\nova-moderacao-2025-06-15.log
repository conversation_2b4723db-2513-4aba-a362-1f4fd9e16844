[15/06/2025 00:18:45] INFO: Limpeza de logs concluída: 0 arquivos removidos
[15/06/2025 13:08:35] INFO: ▣ Sistema de logs inicializado
[15/06/2025 13:08:35] INFO: ▣ Gerenciador de configurações inicializado
[15/06/2025 13:08:35] INFO: ▣ Banco de dados inicializado
[15/06/2025 13:08:36] INFO: ▣ 27 comandos carregados
[15/06/2025 13:08:36] INFO: ▣ Gerenciador de estados de comandos inicializado
[15/06/2025 13:08:36] INFO: ▣ Eventos carregados
[15/06/2025 13:08:36] INFO: 🛡️ Sistema Anti-Raid inicializado
[15/06/2025 13:08:36] INFO: 🤖 Sistema de Auto-Moderação inicializado
[15/06/2025 13:08:36] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[15/06/2025 13:08:36] INFO: ▣ Sistemas de moderação inicializados
[15/06/2025 13:08:36] INFO: ▣ Sistema de Tickets inicializado
[15/06/2025 13:08:36] INFO: ▣ Sistema de Analytics de Moderação inicializado
[15/06/2025 13:08:36] INFO: ▣ Sistema de Backup inicializado
[15/06/2025 13:08:36] INFO: ▣ Servidor web inicializado
[15/06/2025 13:08:38] INFO: 🔄 Registrando comandos slash...
[15/06/2025 13:08:39] INFO: ✅ 27 comandos slash registrados globalmente
[15/06/2025 13:08:39] INFO: 🔍 Verificando configurações dos servidores...
[15/06/2025 13:08:39] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[15/06/2025 13:08:39] INFO: 📋 Cache de configurações carregado para 3 servidores
[15/06/2025 13:08:39] INFO: ✅ Tarefas periódicas inicializadas
[15/06/2025 13:08:39] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":21.815634}
[15/06/2025 14:03:45] INFO: COMANDO: say executado | Meta: {"command":"say","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[15/06/2025 14:05:24] WARN: IA baniu xkssad: Discurso de ódio ou ameaças graves detectadas pela IA
[15/06/2025 14:05:24] ERROR: Erro ao executar ação da IA: | Meta: {"error":"guildConfig is not defined","stack":"ReferenceError: guildConfig is not defined\n    at executeAIAction (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:636:61)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:461:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[15/06/2025 14:07:00] INFO: COMANDO: unban executado | Meta: {"command":"unban","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[15/06/2025 14:07:20] ERROR: Erro no evento guildMemberAdd: | Meta: {"error":"Cannot read properties of undefined (reading 'getGuildConfig')","stack":"TypeError: Cannot read properties of undefined (reading 'getGuildConfig')\n    at Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\guildMemberAdd.js:10:49)\n    at Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:189:58)\n    at Client.emit (node:events:530:35)\n    at module.exports [as GUILD_MEMBER_ADD] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\handlers\\GUILD_MEMBER_ADD.js:17:14)\n    at WebSocketManager.handlePacket (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:351:31)\n    at WebSocketManager.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:235:12)\n    at WebSocketManager.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)\n    at WebSocketShard.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\ws\\dist\\index.js:1190:51)\n    at WebSocketShard.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)\n    at WebSocketShard.onMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\ws\\dist\\index.js:1007:14)"}
[15/06/2025 14:08:39] INFO: Limpeza de logs concluída: 0 arquivos removidos
[15/06/2025 14:09:26] WARN: IA baniu xkssad: Discurso de ódio ou ameaças graves detectadas pela IA
[15/06/2025 14:09:26] ERROR: Erro ao executar ação da IA: | Meta: {"error":"guildConfig is not defined","stack":"ReferenceError: guildConfig is not defined\n    at executeAIAction (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:636:61)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:461:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[15/06/2025 14:09:52] INFO: COMANDO: unban executado | Meta: {"command":"unban","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[15/06/2025 14:44:35] INFO: ▣ Iniciando encerramento gracioso...
[15/06/2025 14:44:39] INFO: ▣ Sistema de logs inicializado
[15/06/2025 14:44:39] INFO: ▣ Gerenciador de configurações inicializado
[15/06/2025 14:44:40] INFO: ▣ Banco de dados inicializado
[15/06/2025 14:44:40] INFO: ▣ 26 comandos carregados
[15/06/2025 14:44:40] INFO: ▣ Gerenciador de estados de comandos inicializado
[15/06/2025 14:44:40] INFO: ▣ Eventos carregados
[15/06/2025 14:44:40] INFO: 🛡️ Sistema Anti-Raid inicializado
[15/06/2025 14:44:40] INFO: 🤖 Sistema de Auto-Moderação inicializado
[15/06/2025 14:44:40] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[15/06/2025 14:44:40] INFO: ▣ Sistemas de moderação inicializados
[15/06/2025 14:44:40] INFO: ▣ Sistema de Analytics de Moderação inicializado
[15/06/2025 14:44:40] INFO: ▣ Sistema de Backup inicializado
[15/06/2025 14:44:40] INFO: ▣ Servidor web inicializado
[15/06/2025 14:44:41] INFO: 🔄 Registrando comandos slash...
[15/06/2025 14:44:42] INFO: ✅ 26 comandos slash registrados globalmente
[15/06/2025 14:44:42] INFO: 🔍 Verificando configurações dos servidores...
[15/06/2025 14:44:42] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[15/06/2025 14:44:42] INFO: 📋 Cache de configurações carregado para 3 servidores
[15/06/2025 14:44:42] INFO: ✅ Tarefas periódicas inicializadas
[15/06/2025 14:44:42] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":26,"uptime":4.1380673}
[15/06/2025 15:44:42] INFO: Limpeza de logs concluída: 0 arquivos removidos
[15/06/2025 16:01:44] INFO: COMANDO: deploy-local executado | Meta: {"command":"deploy-local","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[15/06/2025 16:22:41] INFO: COMANDO: stats executado | Meta: {"command":"stats","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[15/06/2025 16:23:10] INFO: COMANDO: stats executado | Meta: {"command":"stats","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[15/06/2025 16:44:42] INFO: Limpeza de logs concluída: 0 arquivos removidos
[15/06/2025 17:27:11] INFO: COMANDO: deploy-local executado | Meta: {"command":"deploy-local","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[15/06/2025 17:36:01] ERROR: Erro no evento guildMemberAdd: | Meta: {"error":"Cannot read properties of undefined (reading 'getGuildConfig')","stack":"TypeError: Cannot read properties of undefined (reading 'getGuildConfig')\n    at Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\guildMemberAdd.js:10:49)\n    at Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:187:58)\n    at Client.emit (node:events:530:35)\n    at module.exports [as GUILD_MEMBER_ADD] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\handlers\\GUILD_MEMBER_ADD.js:17:14)\n    at WebSocketManager.handlePacket (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:351:31)\n    at WebSocketManager.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:235:12)\n    at WebSocketManager.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)\n    at WebSocketShard.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\ws\\dist\\index.js:1190:51)\n    at WebSocketShard.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)\n    at WebSocketShard.onMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\ws\\dist\\index.js:1007:14)"}
[15/06/2025 17:44:42] INFO: Limpeza de logs concluída: 0 arquivos removidos
[15/06/2025 18:17:27] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[15/06/2025 18:33:00] INFO: COMANDO: deploy-local executado | Meta: {"command":"deploy-local","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[15/06/2025 18:44:42] INFO: Limpeza de logs concluída: 0 arquivos removidos
[15/06/2025 19:44:42] INFO: Limpeza de logs concluída: 0 arquivos removidos
[15/06/2025 20:44:42] INFO: Limpeza de logs concluída: 0 arquivos removidos
[15/06/2025 21:44:42] INFO: Limpeza de logs concluída: 0 arquivos removidos
[15/06/2025 22:44:42] INFO: Limpeza de logs concluída: 0 arquivos removidos
[15/06/2025 23:44:41] INFO: Limpeza de logs concluída: 0 arquivos removidos
