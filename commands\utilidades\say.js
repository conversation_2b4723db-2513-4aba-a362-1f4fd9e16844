/**
 * ========================================
 * COMANDO: SAY (FALAR)
 * Faz o bot enviar uma mensagem
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('say')
        .setNameLocalizations({
            'pt-BR': 'falar'
        })
        .setDescription('Faz o bot enviar uma mensagem')
        .setDescriptionLocalizations({
            'pt-BR': 'Faz o bot enviar uma mensagem no canal atual'
        })
        .setDefaultMemberPermissions(32) // MANAGE_MESSAGES
        .addStringOption(option =>
            option.setName('mensagem')
                .setDescription('Mensagem para o bot enviar')
                .setDescriptionLocalizations({ 'pt-BR': 'Mensagem para o bot enviar' })
                .setRequired(true)
                .setMaxLength(2000)
        )
        .addChannelOption(option =>
            option.setName('canal')
                .setDescription('Canal onde enviar a mensagem')
                .setDescriptionLocalizations({ 'pt-BR': 'Canal onde enviar a mensagem' })
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('embed')
                .setDescription('Enviar como embed')
                .setDescriptionLocalizations({ 'pt-BR': 'Enviar a mensagem como embed' })
                .setRequired(false)
        ),
    
    category: 'utilidades',
    cooldown: 5,
    
    async execute(interaction) {
        try {
            const message = interaction.options.getString('mensagem');
            const channel = interaction.options.getChannel('canal') || interaction.channel;
            const useEmbed = interaction.options.getBoolean('embed') || false;

            // Verificar permissões no canal de destino
            if (channel.id !== interaction.channel.id) {
                const permissions = channel.permissionsFor(interaction.member);
                if (!permissions.has('SendMessages')) {
                    return await interaction.reply({
                        content: 'Você não tem permissão para enviar mensagens nesse canal!',
                        ephemeral: true
                    });
                }
            }

            // Verificar se o bot pode enviar mensagens no canal
            const botPermissions = channel.permissionsFor(interaction.guild.members.me);
            if (!botPermissions.has('SendMessages')) {
                return await interaction.reply({
                    content: 'Eu não tenho permissão para enviar mensagens nesse canal!',
                    ephemeral: true
                });
            }

            if (useEmbed && !botPermissions.has('EmbedLinks')) {
                return await interaction.reply({
                    content: 'Eu não tenho permissão para enviar embeds nesse canal!',
                    ephemeral: true
                });
            }

            // Enviar mensagem
            if (useEmbed) {
                const embed = new EmbedBuilder()
                    .setColor('#00ff7f')
                    .setDescription(message)
                    .setFooter({
                        text: `Enviado por ${interaction.user.tag}`,
                        iconURL: interaction.user.displayAvatarURL()
                    })
                    .setTimestamp();

                await channel.send({ embeds: [embed] });
            } else {
                await channel.send(message);
            }

            // Confirmar envio
            const confirmEmbed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('Mensagem Enviada')
                .setDescription(`Mensagem enviada com sucesso em ${channel}!`)
                .addFields(
                    {
                        name: 'Conteúdo',
                        value: message.length > 100 ? message.substring(0, 100) + '...' : message,
                        inline: false
                    },
                    {
                        name: 'Canal',
                        value: channel.toString(),
                        inline: true
                    },
                    {
                        name: 'Formato',
                        value: useEmbed ? 'Embed' : 'Texto simples',
                        inline: true
                    }
                )
                .setFooter({
                    text: 'Nodex | Moderação',
                    iconURL: interaction.client.user.displayAvatarURL()
                })
                .setTimestamp();

            await interaction.reply({ embeds: [confirmEmbed], ephemeral: true });

        } catch (error) {
            console.error('Erro no comando say:', error);
            await interaction.reply({
                content: 'Erro ao enviar mensagem!',
                ephemeral: true
            });
        }
    }
};
