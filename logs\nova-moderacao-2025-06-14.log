[14/06/2025 00:26:54] INFO: 📝 Sistema de logs inicializado
[14/06/2025 00:26:54] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 00:26:54] INFO: 💾 Banco de dados inicializado
[14/06/2025 00:26:54] INFO: 📋 29 comandos carregados
[14/06/2025 00:26:54] ERROR: Erro ao carregar evento C:\Users\<USER>\Desktop\Nova pasta (4)\events\guildMemberAdd.js: | Meta: {"error":"Unexpected token 'catch'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\guildMemberAdd.js:165\n    } catch (error) {\n      ^^^^^\n\nSyntaxError: Unexpected token 'catch'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadEvents (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:179:27)"}
[14/06/2025 00:26:54] INFO: 🎯 Eventos carregados
[14/06/2025 00:26:54] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 00:26:54] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 00:26:54] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 00:26:54] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 00:26:54] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 00:26:54] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 00:26:54] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 00:26:54] INFO: 🌐 Servidor web inicializado
[14/06/2025 00:26:56] INFO: 🔄 Registrando comandos slash...
[14/06/2025 00:26:57] INFO: ✅ 29 comandos slash registrados globalmente
[14/06/2025 00:26:57] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 00:26:57] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 00:26:57] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 00:26:57] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 00:26:57] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":29,"uptime":5.3003443}
[14/06/2025 00:27:06] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 00:27:07] ERROR: Erro no comando dashboard: | Meta: {"user":"558672715243061269","guild":"1381755403326455838","options":[],"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:127:17)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:104:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[14/06/2025 00:27:07] ERROR: Erro ao enviar mensagem de erro: | Meta: {"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:153:17)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[14/06/2025 00:27:07] WARN: COMANDO: dashboard falhou | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":false,"error":"Interaction has already been acknowledged."}
[14/06/2025 00:28:30] INFO: 📝 Sistema de logs inicializado
[14/06/2025 00:28:30] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 00:28:30] INFO: 💾 Banco de dados inicializado
[14/06/2025 00:28:30] INFO: 📋 29 comandos carregados
[14/06/2025 00:28:30] ERROR: Erro ao carregar evento C:\Users\<USER>\Desktop\Nova pasta (4)\events\guildMemberAdd.js: | Meta: {"error":"Unexpected token 'catch'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\guildMemberAdd.js:165\n    } catch (error) {\n      ^^^^^\n\nSyntaxError: Unexpected token 'catch'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadEvents (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:179:27)"}
[14/06/2025 00:28:30] INFO: 🎯 Eventos carregados
[14/06/2025 00:28:30] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 00:28:30] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 00:28:30] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 00:28:30] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 00:28:30] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 00:28:30] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 00:28:30] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 00:28:30] INFO: 🌐 Servidor web inicializado
[14/06/2025 00:28:31] INFO: 🔄 Registrando comandos slash...
[14/06/2025 00:28:32] INFO: ✅ 29 comandos slash registrados globalmente
[14/06/2025 00:28:32] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 00:28:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 00:28:32] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 00:28:32] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 00:28:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":29,"uptime":4.0293084}
[14/06/2025 00:30:50] INFO: 📝 Sistema de logs inicializado
[14/06/2025 00:30:50] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 00:30:50] INFO: 💾 Banco de dados inicializado
[14/06/2025 00:30:50] INFO: 📋 29 comandos carregados
[14/06/2025 00:30:50] INFO: 🎯 Eventos carregados
[14/06/2025 00:30:50] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 00:30:50] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 00:30:50] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 00:30:50] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 00:30:50] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 00:30:50] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 00:30:50] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 00:30:50] INFO: 🌐 Servidor web inicializado
[14/06/2025 00:30:51] INFO: 🔄 Registrando comandos slash...
[14/06/2025 00:30:52] INFO: ✅ 29 comandos slash registrados globalmente
[14/06/2025 00:30:52] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 00:30:52] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 00:30:52] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 00:30:52] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 00:30:52] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":29,"uptime":4.268629}
[14/06/2025 00:34:32] INFO: COMANDO: deploy-local executado | Meta: {"command":"deploy-local","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 00:41:00] INFO: 📝 Sistema de logs inicializado
[14/06/2025 00:41:00] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 00:41:00] INFO: 💾 Banco de dados inicializado
[14/06/2025 00:41:00] INFO: 📋 23 comandos carregados
[14/06/2025 00:41:00] INFO: 🎯 Eventos carregados
[14/06/2025 00:41:00] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 00:41:00] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 00:41:00] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 00:41:00] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 00:41:00] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 00:41:00] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 00:41:00] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 00:41:00] INFO: 🌐 Servidor web inicializado
[14/06/2025 00:41:02] INFO: 🔄 Registrando comandos slash...
[14/06/2025 00:41:02] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 00:41:02] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 00:41:02] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 00:41:02] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 00:41:02] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 00:41:02] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":23,"uptime":4.5625742}
[14/06/2025 00:45:05] INFO: 📝 Sistema de logs inicializado
[14/06/2025 00:45:05] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 00:45:05] INFO: 💾 Banco de dados inicializado
[14/06/2025 00:45:05] INFO: 📋 23 comandos carregados
[14/06/2025 00:45:05] INFO: 🎯 Eventos carregados
[14/06/2025 00:45:05] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 00:45:05] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 00:45:05] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 00:45:05] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 00:45:05] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 00:45:05] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 00:45:05] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 00:45:05] INFO: 🌐 Servidor web inicializado
[14/06/2025 00:45:07] INFO: 🔄 Registrando comandos slash...
[14/06/2025 00:45:07] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 00:45:07] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 00:45:07] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 00:45:07] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 00:45:07] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 00:45:07] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":23,"uptime":3.9730082}
[14/06/2025 00:46:38] INFO: 📝 Sistema de logs inicializado
[14/06/2025 00:46:38] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 00:46:38] INFO: 💾 Banco de dados inicializado
[14/06/2025 00:46:38] INFO: 📋 24 comandos carregados
[14/06/2025 00:46:38] INFO: 🎯 Eventos carregados
[14/06/2025 00:46:38] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 00:46:38] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 00:46:38] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 00:46:38] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 00:46:38] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 00:46:38] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 00:46:38] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 00:46:38] INFO: 🌐 Servidor web inicializado
[14/06/2025 00:46:40] INFO: 🔄 Registrando comandos slash...
[14/06/2025 00:46:41] INFO: ✅ 24 comandos slash registrados globalmente
[14/06/2025 00:46:41] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 00:46:41] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 00:46:41] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 00:46:41] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 00:46:41] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":24,"uptime":4.2193224}
[14/06/2025 00:48:35] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 00:49:03] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[14/06/2025 00:52:48] INFO: 📝 Sistema de logs inicializado
[14/06/2025 00:52:48] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 00:52:48] INFO: 💾 Banco de dados inicializado
[14/06/2025 00:52:48] INFO: 📋 23 comandos carregados
[14/06/2025 00:52:48] INFO: 🎯 Eventos carregados
[14/06/2025 00:52:48] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 00:52:48] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 00:52:48] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 00:52:48] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 00:52:48] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 00:52:48] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 00:52:48] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 00:52:48] INFO: 🌐 Servidor web inicializado
[14/06/2025 00:52:49] INFO: 🔄 Registrando comandos slash...
[14/06/2025 00:52:50] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 00:52:50] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 00:52:50] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 00:52:50] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 00:52:50] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 00:52:50] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":23,"uptime":3.9965083}
[14/06/2025 00:54:23] INFO: 📝 Sistema de logs inicializado
[14/06/2025 00:54:23] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 00:54:23] INFO: 💾 Banco de dados inicializado
[14/06/2025 00:54:23] INFO: 📋 23 comandos carregados
[14/06/2025 00:54:23] INFO: 🎯 Eventos carregados
[14/06/2025 00:54:23] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 00:54:23] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 00:54:23] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 00:54:23] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 00:54:23] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 00:54:23] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 00:54:23] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 00:54:23] INFO: 🌐 Servidor web inicializado
[14/06/2025 00:54:25] INFO: 🔄 Registrando comandos slash...
[14/06/2025 00:54:26] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 00:54:26] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 00:54:26] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 00:54:26] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 00:54:26] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 00:54:26] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":23,"uptime":4.4864481}
[14/06/2025 00:55:01] INFO: COMANDO: kick executado | Meta: {"command":"kick","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 00:55:39] INFO: Usuário GS Defender#7592 expulso por emp2866: Nenhum motivo fornecido
[14/06/2025 00:55:39] INFO: COMANDO: kick executado | Meta: {"command":"kick","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 01:00:46] INFO: 📝 Sistema de logs inicializado
[14/06/2025 01:00:46] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 01:00:46] INFO: 💾 Banco de dados inicializado
[14/06/2025 01:00:46] INFO: 📋 23 comandos carregados
[14/06/2025 01:00:46] INFO: 🎯 Eventos carregados
[14/06/2025 01:00:46] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 01:00:46] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 01:00:46] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 01:00:46] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 01:00:46] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 01:00:46] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 01:00:46] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 01:00:46] INFO: 🌐 Servidor web inicializado
[14/06/2025 01:00:48] INFO: 🔄 Registrando comandos slash...
[14/06/2025 01:00:48] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 01:00:48] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 01:00:48] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 01:00:48] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 01:00:48] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 01:00:48] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":23,"uptime":4.3692152}
[14/06/2025 01:02:10] INFO: 📝 Sistema de logs inicializado
[14/06/2025 01:02:10] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 01:02:10] INFO: 💾 Banco de dados inicializado
[14/06/2025 01:02:10] INFO: 📋 23 comandos carregados
[14/06/2025 01:02:10] INFO: 🎯 Eventos carregados
[14/06/2025 01:02:10] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 01:02:10] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 01:02:10] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 01:02:10] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 01:02:10] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 01:02:10] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 01:02:10] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 01:02:10] INFO: 🌐 Servidor web inicializado
[14/06/2025 01:02:12] INFO: 🔄 Registrando comandos slash...
[14/06/2025 01:02:12] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 01:02:12] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 01:02:12] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 01:02:12] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 01:02:12] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 01:02:12] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":23,"uptime":3.7638438}
[14/06/2025 01:07:39] INFO: 📝 Sistema de logs inicializado
[14/06/2025 01:07:39] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 01:07:39] INFO: 💾 Banco de dados inicializado
[14/06/2025 01:07:39] INFO: 📋 23 comandos carregados
[14/06/2025 01:07:39] INFO: 🎯 Eventos carregados
[14/06/2025 01:07:39] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 01:07:39] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 01:07:39] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 01:07:39] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 01:07:39] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 01:07:40] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 01:07:40] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 01:07:40] INFO: 🌐 Servidor web inicializado
[14/06/2025 01:07:41] INFO: 🔄 Registrando comandos slash...
[14/06/2025 01:07:42] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 01:07:42] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 01:07:42] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 01:07:42] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 01:07:42] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 01:07:42] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":23,"uptime":4.0655803}
[14/06/2025 01:10:47] INFO: 📝 Sistema de logs inicializado
[14/06/2025 01:10:47] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 01:10:47] INFO: 💾 Banco de dados inicializado
[14/06/2025 01:10:47] INFO: 📋 23 comandos carregados
[14/06/2025 01:10:47] INFO: 🎯 Eventos carregados
[14/06/2025 01:10:47] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 01:10:47] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 01:10:47] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 01:10:47] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 01:10:47] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 01:10:47] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 01:10:47] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 01:10:47] INFO: 🌐 Servidor web inicializado
[14/06/2025 01:10:51] INFO: 🔄 Registrando comandos slash...
[14/06/2025 01:10:52] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 01:10:52] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 01:10:52] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 01:10:52] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 01:10:52] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 01:10:52] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":23,"uptime":6.9940668}
[14/06/2025 01:12:44] INFO: 📝 Sistema de logs inicializado
[14/06/2025 01:12:44] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 01:12:44] INFO: 💾 Banco de dados inicializado
[14/06/2025 01:12:44] INFO: 📋 23 comandos carregados
[14/06/2025 01:12:44] INFO: 🎯 Eventos carregados
[14/06/2025 01:12:44] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 01:12:44] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 01:12:44] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 01:12:44] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 01:12:44] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 01:12:44] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 01:12:44] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 01:12:44] INFO: 🌐 Servidor web inicializado
[14/06/2025 01:12:46] INFO: 🔄 Registrando comandos slash...
[14/06/2025 01:12:46] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 01:12:46] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 01:12:46] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 01:12:46] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 01:12:46] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 01:12:46] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":23,"uptime":3.9395345}
[14/06/2025 01:27:07] INFO: 📝 Sistema de logs inicializado
[14/06/2025 01:27:07] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 01:27:08] INFO: 💾 Banco de dados inicializado
[14/06/2025 01:27:08] INFO: 📋 23 comandos carregados
[14/06/2025 01:27:08] INFO: 🎯 Eventos carregados
[14/06/2025 01:27:08] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 01:27:08] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 01:27:08] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 01:27:08] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 01:27:08] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 01:27:08] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 01:27:08] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 01:27:08] INFO: 🌐 Servidor web inicializado
[14/06/2025 01:27:09] INFO: 🔄 Registrando comandos slash...
[14/06/2025 01:27:09] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 01:27:09] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 01:27:09] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 01:27:09] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 01:27:09] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 01:27:09] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":23,"uptime":3.9752828}
[14/06/2025 01:27:17] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 01:29:06] ERROR: Erro no comando warn: | Meta: {"error":"client.database.getModerationHistory(...)?.filter is not a function","stack":"TypeError: client.database.getModerationHistory(...)?.filter is not a function\n    at Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warn.js:65:19)\n    at handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:104:23)\n    at Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:19)\n    at Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:184:58)\n    at Client.emit (node:events:530:35)\n    at InteractionCreateAction.handle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\actions\\InteractionCreate.js:97:12)\n    at module.exports [as INTERACTION_CREATE] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\handlers\\INTERACTION_CREATE.js:4:36)\n    at WebSocketManager.handlePacket (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:351:31)\n    at WebSocketManager.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:235:12)\n    at WebSocketManager.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)"}
[14/06/2025 01:29:07] INFO: COMANDO: warn executado | Meta: {"command":"warn","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 01:34:00] INFO: 📝 Sistema de logs inicializado
[14/06/2025 01:34:00] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 01:34:00] INFO: 💾 Banco de dados inicializado
[14/06/2025 01:34:00] INFO: 📋 23 comandos carregados
[14/06/2025 01:34:00] INFO: 🎯 Eventos carregados
[14/06/2025 01:34:00] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 01:34:00] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 01:34:00] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 01:34:00] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 01:34:00] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 01:34:00] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 01:34:00] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 01:34:00] INFO: 🌐 Servidor web inicializado
[14/06/2025 01:34:03] INFO: 🔄 Registrando comandos slash...
[14/06/2025 01:34:04] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 01:34:04] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 01:34:04] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 01:34:04] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 01:34:04] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 01:34:04] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":23,"uptime":5.6881853}
[14/06/2025 01:35:15] ERROR: Erro no comando warn: | Meta: {"error":"Unknown interaction","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warn.js:196:13)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:104:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[14/06/2025 01:35:16] ERROR: Erro no comando warn: | Meta: {"user":"558672715243061269","guild":"1381755403326455838","options":[{"name":"usuário","type":6,"value":"1195685295568470056","user":{"id":"1195685295568470056","bot":false,"system":false,"flags":0,"username":"nodex_dev","globalName":"Nodex","discriminator":"0","avatar":"1ce8df21f73fe0736679688ae977eaec","banner":null,"accentColor":0,"avatarDecoration":null,"avatarDecorationData":null,"createdTimestamp":1705143989222,"defaultAvatarURL":"https://cdn.discordapp.com/embed/avatars/2.png","hexAccentColor":"#000000","tag":"nodex_dev","avatarURL":"https://cdn.discordapp.com/avatars/1195685295568470056/1ce8df21f73fe0736679688ae977eaec.webp","displayAvatarURL":"https://cdn.discordapp.com/avatars/1195685295568470056/1ce8df21f73fe0736679688ae977eaec.webp","bannerURL":null},"member":{"guildId":"1381755403326455838","joinedTimestamp":1749647621933,"premiumSinceTimestamp":null,"nickname":null,"pending":false,"communicationDisabledUntilTimestamp":null,"userId":"1195685295568470056","avatar":null,"banner":null,"flags":0,"displayName":"Nodex","roles":["1382425258975563997","1381755403326455838"],"avatarURL":null,"bannerURL":null,"displayAvatarURL":"https://cdn.discordapp.com/avatars/1195685295568470056/1ce8df21f73fe0736679688ae977eaec.webp","displayBannerURL":null}},{"name":"motivo","type":3,"value":"gay"}],"error":"Unknown interaction","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warn.js:289:13)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:104:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[14/06/2025 01:35:16] ERROR: Erro ao enviar mensagem de erro: | Meta: {"error":"Unknown interaction","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:153:17)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[14/06/2025 01:35:16] WARN: COMANDO: warn falhou | Meta: {"command":"warn","user":"558672715243061269","guild":"1381755403326455838","success":false,"error":"Unknown interaction"}
[14/06/2025 01:35:42] INFO: 📝 Sistema de logs inicializado
[14/06/2025 01:35:42] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 01:35:42] INFO: 💾 Banco de dados inicializado
[14/06/2025 01:35:42] INFO: 📋 23 comandos carregados
[14/06/2025 01:35:42] INFO: 🎯 Eventos carregados
[14/06/2025 01:35:42] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 01:35:42] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 01:35:42] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 01:35:42] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 01:35:42] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 01:35:42] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 01:35:42] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 01:35:42] INFO: 🌐 Servidor web inicializado
[14/06/2025 01:35:43] INFO: 🔄 Registrando comandos slash...
[14/06/2025 01:35:44] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 01:35:44] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 01:35:44] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 01:35:44] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 01:35:44] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 01:35:44] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":23,"uptime":3.7112401}
[14/06/2025 01:36:22] INFO: Usuário nodex_dev advertido por emp2866: viadagem extrema (Advertência #1)
[14/06/2025 01:36:22] INFO: COMANDO: warn executado | Meta: {"command":"warn","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 12:11:03] INFO: 📝 Sistema de logs inicializado
[14/06/2025 12:11:03] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 12:11:03] INFO: 💾 Banco de dados inicializado
[14/06/2025 12:11:04] INFO: 📋 23 comandos carregados
[14/06/2025 12:11:04] INFO: 🎯 Eventos carregados
[14/06/2025 12:11:04] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 12:11:04] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 12:11:04] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 12:11:04] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 12:11:04] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 12:11:04] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 12:11:04] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 12:11:04] INFO: 🌐 Servidor web inicializado
[14/06/2025 12:11:06] INFO: 🔄 Registrando comandos slash...
[14/06/2025 12:11:08] INFO: ✅ 23 comandos slash registrados globalmente
[14/06/2025 12:11:08] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 12:11:08] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 12:11:08] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 12:11:08] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 12:11:08] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":23,"uptime":25.5224062}
[14/06/2025 12:13:06] WARN: IA baniu xkssad: Discurso de ódio ou ameaças graves detectadas pela IA
[14/06/2025 12:13:06] ERROR: Erro ao executar ação da IA: | Meta: {"error":"guildConfig is not defined","stack":"ReferenceError: guildConfig is not defined\n    at executeAIAction (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:636:61)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:461:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[14/06/2025 12:13:06] INFO: Mensagem deletada logada: xkssad em #💭・chat-geral
[14/06/2025 12:13:51] INFO: COMANDO: unban executado | Meta: {"command":"unban","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 12:14:11] INFO: COMANDO: unban executado | Meta: {"command":"unban","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 12:14:42] ERROR: Erro no evento guildMemberAdd: | Meta: {"error":"Cannot read properties of undefined (reading 'getGuildConfig')","stack":"TypeError: Cannot read properties of undefined (reading 'getGuildConfig')\n    at Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\guildMemberAdd.js:10:49)\n    at Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:184:58)\n    at Client.emit (node:events:530:35)\n    at module.exports [as GUILD_MEMBER_ADD] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\handlers\\GUILD_MEMBER_ADD.js:17:14)\n    at WebSocketManager.handlePacket (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:351:31)\n    at WebSocketManager.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:235:12)\n    at WebSocketManager.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)\n    at WebSocketShard.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\ws\\dist\\index.js:1190:51)\n    at WebSocketShard.emit (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)\n    at WebSocketShard.onMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\ws\\dist\\index.js:1007:14)"}
[14/06/2025 12:16:28] INFO: COMANDO: warn executado | Meta: {"command":"warn","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 12:17:00] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 12:36:16] INFO: 📝 Sistema de logs inicializado
[14/06/2025 12:36:16] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 12:36:16] INFO: 💾 Banco de dados inicializado
[14/06/2025 12:36:16] INFO: 📋 26 comandos carregados
[14/06/2025 12:36:16] INFO: 🎯 Eventos carregados
[14/06/2025 12:36:16] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 12:36:16] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 12:36:16] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 12:36:16] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 12:36:16] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 12:36:16] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 12:36:16] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 12:36:16] INFO: 🌐 Servidor web inicializado
[14/06/2025 12:36:17] INFO: 🔄 Registrando comandos slash...
[14/06/2025 12:36:18] INFO: ✅ 26 comandos slash registrados globalmente
[14/06/2025 12:36:18] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 12:36:18] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 12:36:18] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 12:36:18] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 12:36:18] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":26,"uptime":4.4561966}
[14/06/2025 12:39:53] INFO: 📝 Sistema de logs inicializado
[14/06/2025 12:39:53] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 12:39:53] INFO: 💾 Banco de dados inicializado
[14/06/2025 12:39:53] INFO: 📋 27 comandos carregados
[14/06/2025 12:39:53] INFO: 🎯 Eventos carregados
[14/06/2025 12:39:53] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 12:39:53] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 12:39:53] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 12:39:53] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 12:39:53] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 12:39:53] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 12:39:53] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 12:39:53] INFO: 🌐 Servidor web inicializado
[14/06/2025 12:39:55] INFO: 🔄 Registrando comandos slash...
[14/06/2025 12:39:56] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 12:39:56] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 12:39:56] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 12:39:56] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 12:39:56] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 12:39:56] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.4689005}
[14/06/2025 12:46:26] INFO: 📝 Sistema de logs inicializado
[14/06/2025 12:46:26] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 12:46:26] INFO: 💾 Banco de dados inicializado
[14/06/2025 12:46:26] INFO: 📋 27 comandos carregados
[14/06/2025 12:46:26] INFO: 🎯 Eventos carregados
[14/06/2025 12:46:26] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 12:46:26] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 12:46:26] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 12:46:26] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 12:46:26] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 12:46:26] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 12:46:26] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 12:46:26] INFO: 🌐 Servidor web inicializado
[14/06/2025 12:46:28] INFO: 🔄 Registrando comandos slash...
[14/06/2025 12:46:28] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 12:46:28] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 12:46:28] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 12:46:28] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 12:46:28] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 12:46:28] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.1453943}
[14/06/2025 12:47:23] INFO: 📝 Sistema de logs inicializado
[14/06/2025 12:47:23] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 12:47:23] INFO: 💾 Banco de dados inicializado
[14/06/2025 12:47:23] INFO: 📋 27 comandos carregados
[14/06/2025 12:47:23] INFO: 🎯 Eventos carregados
[14/06/2025 12:47:23] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 12:47:23] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 12:47:23] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 12:47:23] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 12:47:23] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 12:47:23] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 12:47:23] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 12:47:23] INFO: 🌐 Servidor web inicializado
[14/06/2025 12:47:25] INFO: 🔄 Registrando comandos slash...
[14/06/2025 12:47:25] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 12:47:25] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 12:47:25] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 12:47:25] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 12:47:25] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 12:47:25] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":3.9849975}
[14/06/2025 12:49:06] INFO: 📝 Sistema de logs inicializado
[14/06/2025 12:49:06] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 12:49:06] INFO: 💾 Banco de dados inicializado
[14/06/2025 12:49:06] INFO: 📋 27 comandos carregados
[14/06/2025 12:49:06] INFO: 🎯 Eventos carregados
[14/06/2025 12:49:06] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 12:49:06] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 12:49:06] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 12:49:06] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 12:49:06] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 12:49:06] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 12:49:06] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 12:49:06] INFO: 🌐 Servidor web inicializado
[14/06/2025 12:49:07] INFO: 🔄 Registrando comandos slash...
[14/06/2025 12:49:08] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 12:49:08] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 12:49:08] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 12:49:08] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 12:49:08] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 12:49:08] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.1267599}
[14/06/2025 12:52:09] INFO: 📝 Sistema de logs inicializado
[14/06/2025 12:52:09] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 12:52:09] INFO: 💾 Banco de dados inicializado
[14/06/2025 12:52:09] INFO: 📋 27 comandos carregados
[14/06/2025 12:52:09] INFO: 🎯 Eventos carregados
[14/06/2025 12:52:09] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 12:52:09] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 12:52:09] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 12:52:09] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 12:52:09] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 12:52:09] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 12:52:09] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 12:52:09] INFO: 🌐 Servidor web inicializado
[14/06/2025 12:52:10] INFO: 🔄 Registrando comandos slash...
[14/06/2025 12:52:11] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 12:52:11] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 12:52:11] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 12:52:11] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 12:52:11] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 12:52:11] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.3892918}
[14/06/2025 12:54:49] INFO: COMANDO: clear executado | Meta: {"command":"clear","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 12:55:10] INFO: COMANDO: clear executado | Meta: {"command":"clear","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 12:55:41] INFO: COMANDO: botinfo executado | Meta: {"command":"botinfo","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 12:56:56] ERROR: Erro no comando warnings: | Meta: {"error":"client.database.getAllModerationHistory is not a function","stack":"TypeError: client.database.getAllModerationHistory is not a function\n    at Object.handleStats (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warnings.js:295:44)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warnings.js:99:21)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:104:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[14/06/2025 12:56:56] INFO: COMANDO: warnings executado | Meta: {"command":"warnings","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 13:28:45] INFO: 📝 Sistema de logs inicializado
[14/06/2025 13:28:45] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 13:28:45] INFO: 💾 Banco de dados inicializado
[14/06/2025 13:28:45] INFO: 📋 27 comandos carregados
[14/06/2025 13:28:45] INFO: 🎯 Eventos carregados
[14/06/2025 13:28:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 13:28:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 13:28:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 13:28:45] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 13:28:45] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 13:28:45] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 13:28:45] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 13:28:45] INFO: 🌐 Servidor web inicializado
[14/06/2025 13:28:47] INFO: 🔄 Registrando comandos slash...
[14/06/2025 13:28:48] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 13:28:48] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 13:28:48] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 13:28:48] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 13:28:48] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 13:28:48] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":10.2158683}
[14/06/2025 13:29:30] INFO: 📝 Sistema de logs inicializado
[14/06/2025 13:29:30] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 13:29:30] INFO: 💾 Banco de dados inicializado
[14/06/2025 13:29:30] INFO: 📋 27 comandos carregados
[14/06/2025 13:29:30] INFO: 🎯 Eventos carregados
[14/06/2025 13:29:30] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 13:29:30] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 13:29:30] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 13:29:30] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 13:29:30] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 13:29:30] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 13:29:30] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 13:29:30] INFO: 🌐 Servidor web inicializado
[14/06/2025 13:29:32] INFO: 🔄 Registrando comandos slash...
[14/06/2025 13:29:32] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 13:29:32] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 13:29:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 13:29:32] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 13:29:32] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 13:29:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.2399491}
[14/06/2025 13:32:38] INFO: COMANDO: stats executado | Meta: {"command":"stats","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 13:40:36] INFO: 📝 Sistema de logs inicializado
[14/06/2025 13:40:36] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 13:40:36] INFO: 💾 Banco de dados inicializado
[14/06/2025 13:40:36] INFO: 📋 27 comandos carregados
[14/06/2025 13:40:36] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 13:40:36] INFO: 🎯 Eventos carregados
[14/06/2025 13:40:36] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 13:40:36] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 13:40:36] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 13:40:36] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 13:40:36] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 13:40:36] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 13:40:36] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 13:40:36] INFO: 🌐 Servidor web inicializado
[14/06/2025 13:40:37] INFO: 🔄 Registrando comandos slash...
[14/06/2025 13:40:39] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 13:40:39] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 13:40:39] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 13:40:39] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 13:40:39] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 13:40:39] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":5.3995327}
[14/06/2025 13:46:18] INFO: 📝 Sistema de logs inicializado
[14/06/2025 13:46:18] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 13:46:18] INFO: 💾 Banco de dados inicializado
[14/06/2025 13:46:18] INFO: 📋 27 comandos carregados
[14/06/2025 13:46:18] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 13:46:18] INFO: 🎯 Eventos carregados
[14/06/2025 13:46:18] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 13:46:18] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 13:46:18] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 13:46:18] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 13:46:18] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 13:46:18] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 13:46:18] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 13:46:18] INFO: 🌐 Servidor web inicializado
[14/06/2025 13:46:19] INFO: 🔄 Registrando comandos slash...
[14/06/2025 13:46:20] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 13:46:20] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 13:46:20] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 13:46:20] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 13:46:20] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 13:46:20] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.2739925}
[14/06/2025 13:46:20] ERROR: Uncaught Exception: | Meta: {"error":"SQLITE_ERROR: cannot commit - no transaction is active","stack":"Error: SQLITE_ERROR: cannot commit - no transaction is active"}
[14/06/2025 13:46:34] INFO: 📝 Sistema de logs inicializado
[14/06/2025 13:46:34] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 13:46:34] INFO: 💾 Banco de dados inicializado
[14/06/2025 13:46:34] INFO: 📋 27 comandos carregados
[14/06/2025 13:46:34] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 13:46:34] INFO: 🎯 Eventos carregados
[14/06/2025 13:46:34] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 13:46:34] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 13:46:34] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 13:46:34] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 13:46:34] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 13:46:34] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 13:46:34] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 13:46:34] INFO: 🌐 Servidor web inicializado
[14/06/2025 13:46:36] INFO: 🔄 Registrando comandos slash...
[14/06/2025 13:46:36] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 13:46:36] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 13:46:36] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 13:46:36] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 13:46:36] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 13:46:36] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.2350276}
[14/06/2025 13:46:36] ERROR: Uncaught Exception: | Meta: {"error":"SQLITE_ERROR: cannot commit - no transaction is active","stack":"Error: SQLITE_ERROR: cannot commit - no transaction is active"}
[14/06/2025 13:46:49] INFO: 📝 Sistema de logs inicializado
[14/06/2025 13:46:49] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 13:46:49] INFO: 💾 Banco de dados inicializado
[14/06/2025 13:46:49] INFO: 📋 27 comandos carregados
[14/06/2025 13:46:49] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 13:46:49] INFO: 🎯 Eventos carregados
[14/06/2025 13:46:49] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 13:46:49] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 13:46:49] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 13:46:49] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 13:46:49] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 13:46:49] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 13:46:49] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 13:46:49] INFO: 🌐 Servidor web inicializado
[14/06/2025 13:46:51] INFO: 🔄 Registrando comandos slash...
[14/06/2025 13:46:52] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 13:46:52] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 13:46:52] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 13:46:52] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 13:46:52] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 13:46:52] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.4766719}
[14/06/2025 13:46:52] ERROR: Uncaught Exception: | Meta: {"error":"SQLITE_ERROR: cannot start a transaction within a transaction","stack":"Error: SQLITE_ERROR: cannot start a transaction within a transaction"}
[14/06/2025 13:47:35] INFO: 📝 Sistema de logs inicializado
[14/06/2025 13:47:35] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 13:47:35] INFO: 💾 Banco de dados inicializado
[14/06/2025 13:47:35] INFO: 📋 27 comandos carregados
[14/06/2025 13:47:35] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 13:47:35] INFO: 🎯 Eventos carregados
[14/06/2025 13:47:35] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 13:47:35] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 13:47:35] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 13:47:35] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 13:47:35] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 13:47:35] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 13:47:35] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 13:47:35] INFO: 🌐 Servidor web inicializado
[14/06/2025 13:47:36] INFO: 🔄 Registrando comandos slash...
[14/06/2025 13:47:37] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 13:47:37] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 13:47:37] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 13:47:37] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 13:47:37] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 13:47:37] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.3908229}
[14/06/2025 13:47:37] ERROR: Uncaught Exception: | Meta: {"error":"SQLITE_ERROR: cannot commit - no transaction is active","stack":"Error: SQLITE_ERROR: cannot commit - no transaction is active"}
[14/06/2025 13:47:42] INFO: 📝 Sistema de logs inicializado
[14/06/2025 13:47:42] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 13:47:42] INFO: 💾 Banco de dados inicializado
[14/06/2025 13:47:42] INFO: 📋 27 comandos carregados
[14/06/2025 13:47:42] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 13:47:42] INFO: 🎯 Eventos carregados
[14/06/2025 13:47:42] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 13:47:42] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 13:47:42] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 13:47:42] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 13:47:42] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 13:47:42] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 13:47:42] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 13:47:42] INFO: 🌐 Servidor web inicializado
[14/06/2025 13:47:43] INFO: 🔄 Registrando comandos slash...
[14/06/2025 13:48:21] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 13:48:21] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 13:48:21] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 13:48:21] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 13:48:21] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 13:48:21] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":41.2291541}
[14/06/2025 13:48:21] ERROR: Uncaught Exception: | Meta: {"error":"SQLITE_ERROR: cannot commit - no transaction is active","stack":"Error: SQLITE_ERROR: cannot commit - no transaction is active"}
[14/06/2025 13:49:55] INFO: 📝 Sistema de logs inicializado
[14/06/2025 13:49:55] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 13:49:55] INFO: 💾 Banco de dados inicializado
[14/06/2025 13:49:56] INFO: 📋 27 comandos carregados
[14/06/2025 13:49:56] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 13:49:56] INFO: 🎯 Eventos carregados
[14/06/2025 13:49:56] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 13:49:56] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 13:49:56] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 13:49:56] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 13:49:56] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 13:49:56] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 13:49:56] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 13:49:56] INFO: 🌐 Servidor web inicializado
[14/06/2025 13:49:57] INFO: 🔄 Registrando comandos slash...
[14/06/2025 13:49:57] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 13:49:57] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 13:49:57] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 13:49:57] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 13:49:58] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 13:49:58] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":3.9241574}
[14/06/2025 13:49:58] ERROR: Uncaught Exception: | Meta: {"error":"SQLITE_ERROR: cannot commit - no transaction is active","stack":"Error: SQLITE_ERROR: cannot commit - no transaction is active"}
[14/06/2025 13:51:20] INFO: 📝 Sistema de logs inicializado
[14/06/2025 13:51:21] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 13:51:21] INFO: 💾 Banco de dados inicializado
[14/06/2025 13:51:21] INFO: 📋 27 comandos carregados
[14/06/2025 13:51:21] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 13:51:21] INFO: 🎯 Eventos carregados
[14/06/2025 13:51:21] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 13:51:21] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 13:51:21] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 13:51:21] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 13:51:21] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 13:51:21] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 13:51:21] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 13:51:21] INFO: 🌐 Servidor web inicializado
[14/06/2025 13:51:22] INFO: 🔄 Registrando comandos slash...
[14/06/2025 13:51:23] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 13:51:23] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 13:51:23] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 13:51:23] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 13:51:23] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 13:51:23] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":5.0737043}
[14/06/2025 13:52:42] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 13:54:07] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 13:55:24] INFO: COMANDO: stats executado | Meta: {"command":"stats","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 14:08:28] INFO: 📝 Sistema de logs inicializado
[14/06/2025 14:08:28] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 14:08:28] INFO: 💾 Banco de dados inicializado
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\backup.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\backup.js:13\n        .setDescription('Gerenciar backups do servidor''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\deploy-local.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\deploy-local.js:17\n        .setDescription('Registra comandos slash neste servidor (aparece instantaneamente)''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\setup-panels.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\setup-panels.js:16\n        .setDescription('Configura painéis de demonstração para todos os sistemas''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\stats.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\stats.js:13\n        .setDescription('Ver estatísticas do servidor''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\ticket.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\ticket.js:13\n        .setDescription('Gerenciar sistema de tickets''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:17\n        .setDescription('Abre o dashboard web para configurar o bot''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\configuracao\config.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\configuracao\\config.js:15\n        .setDescription('Configurações do bot para o servidor''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\botinfo.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\botinfo.js:16\n        .setDescription('🤖 Informações sobre o Nodex | Moderação''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:15\n        .setDescription('Mostra informações de ajuda sobre o bot''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\recursos.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\recursos.js:16\n        .setDescription('Mostra todos os recursos de moderação disponíveis''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\stats-moderacao.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\stats-moderacao.js:16\n        .setDescription('Mostra estatísticas de moderação do servidor''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\ban.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\ban.js:16\n        .setDescription('Banir usuário do servidor permanentemente''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\clear.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\clear.js:18\n        .setDescription('Remove mensagens do canal''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\kick.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\kick.js:15\n        .setDescription('Expulsar usuário do servidor''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\mod-logs.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\mod-logs.js:15\n        .setDescription('Visualizar histórico de moderação''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\mute.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\mute.js:15\n        .setDescription('Aplicar timeout/mute em um usuário com sistema premium''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\timeout.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\timeout.js:13\n        .setDescription('Coloca um usuário em timeout''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\unban.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\unban.js:16\n        .setDescription('Remove o banimento de um usuário''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\unmute.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\unmute.js:15\n        .setDescription('Remover timeout/mute de um usuário com sistema premium''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\warn.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warn.js:15\n        .setDescription('Aplica uma advertência a um usuário''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\warnings.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warnings.js:15\n        .setDescription('Sistema de gerenciamento de advertências''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\avatar.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\avatar.js:13\n        .setDescription('Mostra o avatar de um usuário''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\ping.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\ping.js:13\n        .setDescription('Mostra a latência do bot'')),\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\say.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\say.js:16\n        .setDescription('Faz o bot enviar uma mensagem''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\serverinfo.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\serverinfo.js:13\n        .setDescription('Mostra informações detalhadas do servidor com foco em moderação'')),\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\userinfo.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\userinfo.js:13\n        .setDescription('Mostra informações detalhadas de um usuário''))\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:08:28] INFO: 📋 1 comandos carregados
[14/06/2025 14:08:28] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 14:08:28] INFO: 🎯 Eventos carregados
[14/06/2025 14:08:28] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:08:28] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:08:28] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:08:28] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 14:08:28] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 14:08:28] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 14:08:28] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 14:08:28] INFO: 🌐 Servidor web inicializado
[14/06/2025 14:08:29] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:08:30] INFO: ✅ 1 comandos slash registrados globalmente
[14/06/2025 14:08:30] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:08:30] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:08:30] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:08:30] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:08:30] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":1,"uptime":4.0383439}
[14/06/2025 14:09:48] INFO: 📝 Sistema de logs inicializado
[14/06/2025 14:09:48] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 14:09:48] INFO: 💾 Banco de dados inicializado
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\backup.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\backup.js:13\n        .setDescription('Gerenciar backups do servidor'))\n                                                        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\deploy-local.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\deploy-local.js:17\n        .setDescription('Registra comandos slash neste servidor (aparece instantaneamente)'))\n                                                                                            ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\setup-panels.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\setup-panels.js:16\n        .setDescription('Configura painéis de demonstração para todos os sistemas'))\n                                                                                   ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\stats.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\stats.js:13\n        .setDescription('Ver estatísticas do servidor'))\n                                                       ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\ticket.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\ticket.js:13\n        .setDescription('Gerenciar sistema de tickets'))\n                                                       ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:17\n        .setDescription('Abre o dashboard web para configurar o bot'))\n                                                                     ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\configuracao\config.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\configuracao\\config.js:15\n        .setDescription('Configurações do bot para o servidor'))\n                                                               ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\botinfo.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\botinfo.js:16\n        .setDescription('🤖 Informações sobre o Nodex | Moderação'))\n                                                                   ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:15\n        .setDescription('Mostra informações de ajuda sobre o bot'))\n                                                                  ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\recursos.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\recursos.js:16\n        .setDescription('Mostra todos os recursos de moderação disponíveis'))\n                                                                            ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\stats-moderacao.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\stats-moderacao.js:16\n        .setDescription('Mostra estatísticas de moderação do servidor'))\n                                                                       ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\ban.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\ban.js:16\n        .setDescription('Banir usuário do servidor permanentemente'))\n                                                                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\clear.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\clear.js:18\n        .setDescription('Remove mensagens do canal'))\n                                                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\kick.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\kick.js:15\n        .setDescription('Expulsar usuário do servidor'))\n                                                       ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\mod-logs.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\mod-logs.js:15\n        .setDescription('Visualizar histórico de moderação'))\n                                                            ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\mute.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\mute.js:15\n        .setDescription('Aplicar timeout/mute em um usuário com sistema premium'))\n                                                                                 ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\timeout.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\timeout.js:13\n        .setDescription('Coloca um usuário em timeout'))\n                                                       ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\unban.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\unban.js:16\n        .setDescription('Remove o banimento de um usuário'))\n                                                           ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\unmute.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\unmute.js:15\n        .setDescription('Remover timeout/mute de um usuário com sistema premium'))\n                                                                                 ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\warn.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warn.js:15\n        .setDescription('Aplica uma advertência a um usuário'))\n                                                              ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\warnings.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warnings.js:15\n        .setDescription('Sistema de gerenciamento de advertências'))\n                                                                   ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\avatar.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\avatar.js:13\n        .setDescription('Mostra o avatar de um usuário'))\n                                                        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\ping.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\ping.js:13\n        .setDescription('Mostra a latência do bot')),\n                                                   ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\say.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\say.js:16\n        .setDescription('Faz o bot enviar uma mensagem'))\n                                                        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\serverinfo.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\serverinfo.js:13\n        .setDescription('Mostra informações detalhadas do servidor com foco em moderação')),\n                                                                                          ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\userinfo.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\userinfo.js:13\n        .setDescription('Mostra informações detalhadas de um usuário'))\n                                                                      ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:09:48] INFO: 📋 1 comandos carregados
[14/06/2025 14:09:48] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 14:09:48] INFO: 🎯 Eventos carregados
[14/06/2025 14:09:48] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:09:48] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:09:48] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:09:48] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 14:09:48] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 14:09:48] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 14:09:48] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 14:09:48] INFO: 🌐 Servidor web inicializado
[14/06/2025 14:09:49] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:09:50] INFO: ✅ 1 comandos slash registrados globalmente
[14/06/2025 14:09:50] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:09:50] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:09:50] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:09:50] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:09:50] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":1,"uptime":4.0926147}
[14/06/2025 14:11:38] INFO: 📝 Sistema de logs inicializado
[14/06/2025 14:11:38] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 14:11:38] INFO: 💾 Banco de dados inicializado
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\deploy-local.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\deploy-local.js:35\n            for (const command of interaction.client.commands.values() {\n                                                                       ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\setup-panels.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\setup-panels.js:40\n            if (!permissions.has(['SendMessages', 'EmbedLinks']) {\n                                                                 ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\ticket.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\ticket.js:190\n        if (!channel.name.startsWith('ticket-') {\n                                                ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:32\n            if (!member.permissions.has('Administrator') {\n                                                         ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\configuracao\config.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\configuracao\\config.js:76\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\botinfo.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\botinfo.js:122\n                .setTimestamp();\n                              ^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:22\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\recursos.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\recursos.js:129\n                .setTimestamp();\n                              ^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\stats-moderacao.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\stats-moderacao.js:49\n                if (logs && Array.isArray(logs) {\n                                                ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\ban.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\ban.js:38\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\comandos.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\comandos.js:28\n            if (!interaction.member.permissions.has(['ModerateMembers', 'ManageMessages', 'Administrator']) {\n                                                                                                            ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\kick.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\kick.js:27\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\mod-logs.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\mod-logs.js:63\n            if (!moderationHistory || !Array.isArray(moderationHistory) {\n                                                                        ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:38] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\mute.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\mute.js:93\n            if (target.communicationDisabledUntil && target.communicationDisabledUntil > new Date() {\n                                                                                                    ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\timeout.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\timeout.js:40\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\unban.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\unban.js:46\n            if (!/^\\d{17,19}$/.test(userId) {\n                                            ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\unmute.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\unmute.js:70\n            if (!target.communicationDisabledUntil || target.communicationDisabledUntil <= new Date() {\n                                                                                                      ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\warn.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warn.js:27\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\warnings.js: | Meta: {"error":"Unexpected token ';'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warnings.js:127\n        const warnings = (history && Array.isArray(history) ? history.filter(action => action.action === 'warn') : [];\n                                                                                                                     ^\n\nSyntaxError: Unexpected token ';'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\avatar.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\avatar.js:47\n                .setTimestamp();\n                              ^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\ping.js: | Meta: {"error":"Invalid or unexpected token","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\ping.js:30\n            let connectionQuality = ';\n                                    ^^\n\nSyntaxError: Invalid or unexpected token\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\say.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\say.js:53\n                if (!permissions.has('SendMessages') {\n                                                     ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\serverinfo.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\serverinfo.js:43\n            const threads = guild.channels.cache.filter(channel => channel.isThread().size;\n                                                                                      ^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\userinfo.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\userinfo.js:19\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:11:39] INFO: 📋 3 comandos carregados
[14/06/2025 14:11:39] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 14:11:39] INFO: 🎯 Eventos carregados
[14/06/2025 14:11:39] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:11:39] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:11:39] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:11:39] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 14:11:39] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 14:11:39] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 14:11:39] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 14:11:39] INFO: 🌐 Servidor web inicializado
[14/06/2025 14:11:40] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:11:40] INFO: ✅ 3 comandos slash registrados globalmente
[14/06/2025 14:11:40] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:11:40] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:11:40] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:11:40] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:11:40] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":3,"uptime":3.8596658}
[14/06/2025 14:14:08] INFO: 📝 Sistema de logs inicializado
[14/06/2025 14:14:08] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 14:14:08] INFO: 💾 Banco de dados inicializado
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\setup-panels.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\setup-panels.js:39\n            if (!permissions.has(['SendMessages', 'EmbedLinks']) {\n                                                                 ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\admin\ticket.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\ticket.js:189\n        if (!channel.name.startsWith('ticket-') {\n                                                ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:31\n            if (!member.permissions.has('Administrator') {\n                                                         ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\configuracao\config.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\configuracao\\config.js:75\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\botinfo.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\botinfo.js:121\n                .setTimestamp();\n                              ^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:21\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\recursos.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\recursos.js:128\n                .setTimestamp();\n                              ^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\stats-moderacao.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\stats-moderacao.js:48\n                if (logs && Array.isArray(logs) {\n                                                ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\ban.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\ban.js:37\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\comandos.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\comandos.js:27\n            if (!interaction.member.permissions.has(['ModerateMembers', 'ManageMessages', 'Administrator']) {\n                                                                                                            ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\kick.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\kick.js:26\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\mod-logs.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\mod-logs.js:62\n            if (!moderationHistory || !Array.isArray(moderationHistory) {\n                                                                        ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\mute.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\mute.js:92\n            if (target.communicationDisabledUntil && target.communicationDisabledUntil > new Date() {\n                                                                                                    ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\timeout.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\timeout.js:39\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\unban.js: | Meta: {"error":"Invalid regular expression: /^\\d) {17,19}$/: Unmatched ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\unban.js:45\n            if (!/^\\d) {17,19}$/.test(userId) {\n                 ^^^^^^^^^^^^^^^\n\nSyntaxError: Invalid regular expression: /^\\d) {17,19}$/: Unmatched ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\unmute.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\unmute.js:69\n            if (!target.communicationDisabledUntil || target.communicationDisabledUntil <= new Date() {\n                                                                                                      ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\warn.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warn.js:26\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\warnings.js: | Meta: {"error":"Invalid or unexpected token","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\warnings.js:145\n            color: parseInt(embedStyles.colors.warning.replace('#', '), 16),\n                                                                    ^^^^^^^^\n\nSyntaxError: Invalid or unexpected token\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\avatar.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\avatar.js:46\n                .setTimestamp();\n                              ^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\ping.js: | Meta: {"error":"Invalid or unexpected token","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\ping.js:30\n            let color = ';\n                        ^^\n\nSyntaxError: Invalid or unexpected token\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\say.js: | Meta: {"error":"Unexpected token '{'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\say.js:52\n                if (!permissions.has('SendMessages') {\n                                                     ^\n\nSyntaxError: Unexpected token '{'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\serverinfo.js: | Meta: {"error":"Invalid or unexpected token","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\serverinfo.js:47\n            const boostEmojis = [', '🥉', '🥈', '🥇'];\n                                     ^\n\nSyntaxError: Invalid or unexpected token\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\userinfo.js: | Meta: {"error":"missing ) after argument list","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\userinfo.js:18\n    async execute(interaction) {\n    ^^^^^\n\nSyntaxError: missing ) after argument list\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:152:33)"}
[14/06/2025 14:14:08] INFO: 📋 4 comandos carregados
[14/06/2025 14:14:08] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 14:14:08] INFO: 🎯 Eventos carregados
[14/06/2025 14:14:08] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:14:08] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:14:08] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:14:08] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 14:14:08] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 14:14:08] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 14:14:08] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 14:14:08] INFO: 🌐 Servidor web inicializado
[14/06/2025 14:14:10] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:14:10] INFO: ✅ 4 comandos slash registrados globalmente
[14/06/2025 14:14:10] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:14:10] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:14:10] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:14:10] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:14:10] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":4,"uptime":4.381481}
[14/06/2025 14:15:12] INFO: 📝 Sistema de logs inicializado
[14/06/2025 14:15:12] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 14:15:12] INFO: 💾 Banco de dados inicializado
[14/06/2025 14:15:12] INFO: 📋 27 comandos carregados
[14/06/2025 14:15:12] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 14:15:12] INFO: 🎯 Eventos carregados
[14/06/2025 14:15:12] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:15:12] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:15:12] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:15:12] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 14:15:12] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 14:15:12] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 14:15:12] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 14:15:12] INFO: 🌐 Servidor web inicializado
[14/06/2025 14:15:13] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:15:14] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 14:15:14] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:15:14] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:15:14] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:15:14] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:15:14] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.1816177}
[14/06/2025 14:17:10] INFO: 📝 Sistema de logs inicializado
[14/06/2025 14:17:10] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 14:17:10] INFO: 💾 Banco de dados inicializado
[14/06/2025 14:17:10] INFO: 📋 27 comandos carregados
[14/06/2025 14:17:10] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 14:17:10] INFO: 🎯 Eventos carregados
[14/06/2025 14:17:10] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:17:10] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:17:10] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:17:10] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 14:17:10] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 14:17:10] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 14:17:10] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 14:17:10] INFO: 🌐 Servidor web inicializado
[14/06/2025 14:17:11] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:17:12] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 14:17:12] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:17:12] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:17:12] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:17:12] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:17:12] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.2234557}
[14/06/2025 14:18:01] INFO: 📝 Sistema de logs inicializado
[14/06/2025 14:18:01] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 14:18:01] INFO: 💾 Banco de dados inicializado
[14/06/2025 14:18:01] INFO: 📋 27 comandos carregados
[14/06/2025 14:18:01] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 14:18:01] INFO: 🎯 Eventos carregados
[14/06/2025 14:18:01] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:18:01] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:18:01] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:18:01] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 14:18:01] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 14:18:01] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 14:18:01] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 14:18:01] INFO: 🌐 Servidor web inicializado
[14/06/2025 14:18:03] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:18:03] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 14:18:03] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:18:03] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:18:03] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:18:03] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:18:03] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.1332305}
[14/06/2025 14:20:07] INFO: 📝 Sistema de logs inicializado
[14/06/2025 14:20:07] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 14:20:07] INFO: 💾 Banco de dados inicializado
[14/06/2025 14:20:07] INFO: 📋 27 comandos carregados
[14/06/2025 14:20:07] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 14:20:07] INFO: 🎯 Eventos carregados
[14/06/2025 14:20:07] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:20:07] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:20:07] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:20:07] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 14:20:07] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 14:20:07] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 14:20:07] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 14:20:07] INFO: 🌐 Servidor web inicializado
[14/06/2025 14:20:08] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:20:09] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 14:20:09] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:20:09] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:20:09] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:20:09] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:20:09] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.3759906}
[14/06/2025 14:21:01] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 14:30:19] INFO: 📝 Sistema de logs inicializado
[14/06/2025 14:30:19] INFO: ⚙️ Gerenciador de configurações inicializado
[14/06/2025 14:30:19] INFO: 💾 Banco de dados inicializado
[14/06/2025 14:30:20] INFO: 📋 27 comandos carregados
[14/06/2025 14:30:20] INFO: 🔧 Gerenciador de estados de comandos inicializado
[14/06/2025 14:30:20] INFO: 🎯 Eventos carregados
[14/06/2025 14:30:20] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:30:20] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:30:20] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:30:20] INFO: 🛡️ Sistemas de moderação inicializados
[14/06/2025 14:30:20] INFO: 🎫 Sistema de Tickets inicializado
[14/06/2025 14:30:20] INFO: 📊 Sistema de Analytics de Moderação inicializado
[14/06/2025 14:30:20] INFO: 💾 Sistema de Backup inicializado
[14/06/2025 14:30:20] INFO: 🌐 Servidor web inicializado
[14/06/2025 14:30:21] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:30:22] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 14:30:22] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:30:22] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:30:22] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:30:22] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:30:22] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.2213273}
[14/06/2025 14:32:59] INFO: 🔄 Iniciando encerramento gracioso...
[14/06/2025 14:33:10] INFO: ▣ Sistema de logs inicializado
[14/06/2025 14:33:10] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 14:33:10] INFO: ▣ Banco de dados inicializado
[14/06/2025 14:33:10] INFO: ▣ 27 comandos carregados
[14/06/2025 14:33:10] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 14:33:10] INFO: ▣ Eventos carregados
[14/06/2025 14:33:10] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:33:10] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:33:10] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:33:10] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 14:33:10] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 14:33:10] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 14:33:10] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 14:33:10] INFO: ▣ Servidor web inicializado
[14/06/2025 14:33:11] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:33:12] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 14:33:12] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:33:12] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:33:12] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:33:12] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:33:12] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":3.9822016}
[14/06/2025 14:34:44] INFO: COMANDO: comandos executado | Meta: {"command":"comandos","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 14:35:05] ERROR: Erro no comando help: | Meta: {"error":"Invalid Form Body\ndata.components[0].components[0].options[0].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[0].components[0].options[1].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[0].components[0].options[2].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[0].components[0].options[3].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[0].components[0].options[4].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[1].components[0].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[1].components[1].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[1].components[2].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji","stack":"DiscordAPIError[50035]: Invalid Form Body\ndata.components[0].components[0].options[0].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[0].components[0].options[1].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[0].components[0].options[2].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[0].components[0].options[3].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[0].components[0].options[4].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[1].components[0].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[1].components[1].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\ndata.components[1].components[2].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async showGeneralHelp (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:169:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:30:17)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:127:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[14/06/2025 14:35:05] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 14:36:36] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 14:36:47] INFO: ▣ Sistema de logs inicializado
[14/06/2025 14:36:47] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 14:36:47] INFO: ▣ Banco de dados inicializado
[14/06/2025 14:36:47] INFO: ▣ 27 comandos carregados
[14/06/2025 14:36:47] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 14:36:47] INFO: ▣ Eventos carregados
[14/06/2025 14:36:47] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:36:47] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:36:47] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:36:47] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 14:36:47] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 14:36:47] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 14:36:47] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 14:36:47] INFO: ▣ Servidor web inicializado
[14/06/2025 14:36:48] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:36:49] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 14:36:49] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:36:49] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:36:49] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:36:49] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:36:49] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.2952285}
[14/06/2025 14:39:45] INFO: ▣ Sistema de logs inicializado
[14/06/2025 14:39:45] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 14:39:45] INFO: ▣ Banco de dados inicializado
[14/06/2025 14:39:45] INFO: ▣ 27 comandos carregados
[14/06/2025 14:39:45] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 14:39:45] INFO: ▣ Eventos carregados
[14/06/2025 14:39:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:39:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:39:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:39:45] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 14:39:45] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 14:39:45] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 14:39:45] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 14:39:45] INFO: ▣ Servidor web inicializado
[14/06/2025 14:39:46] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:39:47] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 14:39:47] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:39:47] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:39:47] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:39:47] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:39:47] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.3806701}
[14/06/2025 14:40:22] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 14:42:08] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 14:42:17] INFO: ▣ Sistema de logs inicializado
[14/06/2025 14:42:17] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 14:42:17] INFO: ▣ Banco de dados inicializado
[14/06/2025 14:42:17] INFO: ▣ 28 comandos carregados
[14/06/2025 14:42:17] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 14:42:17] INFO: ▣ Eventos carregados
[14/06/2025 14:42:17] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:42:17] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:42:17] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:42:17] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 14:42:17] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 14:42:17] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 14:42:17] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 14:42:17] INFO: ▣ Servidor web inicializado
[14/06/2025 14:42:19] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:42:19] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 14:42:19] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:42:19] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:42:19] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:42:19] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:42:19] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":3.8788195}
[14/06/2025 14:43:07] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 14:44:02] INFO: ▣ Sistema de logs inicializado
[14/06/2025 14:44:02] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 14:44:02] INFO: ▣ Banco de dados inicializado
[14/06/2025 14:44:02] INFO: ▣ 28 comandos carregados
[14/06/2025 14:44:02] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 14:44:02] INFO: ▣ Eventos carregados
[14/06/2025 14:44:02] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:44:02] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:44:02] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:44:02] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 14:44:02] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 14:44:02] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 14:44:02] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 14:44:02] INFO: ▣ Servidor web inicializado
[14/06/2025 14:44:03] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:44:04] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 14:44:04] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:44:04] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:44:04] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:44:04] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:44:04] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.1745341}
[14/06/2025 14:44:52] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 14:47:20] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 14:47:30] INFO: ▣ Sistema de logs inicializado
[14/06/2025 14:47:30] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 14:47:30] INFO: ▣ Banco de dados inicializado
[14/06/2025 14:47:30] INFO: ▣ 28 comandos carregados
[14/06/2025 14:47:30] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 14:47:30] INFO: ▣ Eventos carregados
[14/06/2025 14:47:30] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:47:30] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:47:30] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:47:30] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 14:47:30] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 14:47:30] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 14:47:30] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 14:47:31] INFO: ▣ Servidor web inicializado
[14/06/2025 14:47:32] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:47:32] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 14:47:32] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:47:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:47:32] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:47:32] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:47:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.1054569}
[14/06/2025 14:48:39] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 14:48:49] INFO: ▣ Sistema de logs inicializado
[14/06/2025 14:48:49] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 14:48:49] INFO: ▣ Banco de dados inicializado
[14/06/2025 14:48:49] INFO: ▣ 28 comandos carregados
[14/06/2025 14:48:49] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 14:48:49] INFO: ▣ Eventos carregados
[14/06/2025 14:48:49] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 14:48:49] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 14:48:49] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 14:48:49] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 14:48:49] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 14:48:49] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 14:48:49] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 14:48:49] INFO: ▣ Servidor web inicializado
[14/06/2025 14:48:51] INFO: 🔄 Registrando comandos slash...
[14/06/2025 14:48:51] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 14:48:51] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 14:48:51] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 14:48:51] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 14:48:51] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 14:48:51] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.0744372}
[14/06/2025 14:49:43] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 15:00:31] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 15:00:42] INFO: ▣ Sistema de logs inicializado
[14/06/2025 15:00:42] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 15:00:42] INFO: ▣ Banco de dados inicializado
[14/06/2025 15:00:42] INFO: ▣ 28 comandos carregados
[14/06/2025 15:00:42] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 15:00:42] INFO: ▣ Eventos carregados
[14/06/2025 15:00:42] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 15:00:42] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 15:00:42] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 15:00:42] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 15:00:42] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 15:00:42] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 15:00:42] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 15:00:42] INFO: ▣ Servidor web inicializado
[14/06/2025 15:00:44] INFO: 🔄 Registrando comandos slash...
[14/06/2025 15:00:45] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 15:00:45] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 15:00:45] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 15:00:45] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 15:00:45] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 15:00:45] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.4441491}
[14/06/2025 15:01:29] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 15:01:41] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 15:01:58] INFO: ▣ Sistema de logs inicializado
[14/06/2025 15:01:58] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 15:01:58] INFO: ▣ Banco de dados inicializado
[14/06/2025 15:01:58] INFO: ▣ 27 comandos carregados
[14/06/2025 15:01:58] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 15:01:58] INFO: ▣ Eventos carregados
[14/06/2025 15:01:58] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 15:01:58] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 15:01:58] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 15:01:58] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 15:01:58] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 15:01:58] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 15:01:58] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 15:01:58] INFO: ▣ Servidor web inicializado
[14/06/2025 15:02:01] INFO: 🔄 Registrando comandos slash...
[14/06/2025 15:02:01] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 15:02:01] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 15:02:02] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 15:02:02] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 15:02:02] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 15:02:02] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":5.6061355}
[14/06/2025 15:03:12] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 15:50:19] INFO: ▣ Sistema de logs inicializado
[14/06/2025 15:50:19] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 15:50:19] INFO: ▣ Banco de dados inicializado
[14/06/2025 15:50:19] INFO: ▣ 28 comandos carregados
[14/06/2025 15:50:19] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 15:50:19] INFO: ▣ Eventos carregados
[14/06/2025 15:50:19] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 15:50:19] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 15:50:19] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 15:50:19] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 15:50:45] INFO: ▣ Sistema de logs inicializado
[14/06/2025 15:50:45] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 15:50:45] INFO: ▣ Banco de dados inicializado
[14/06/2025 15:50:45] INFO: ▣ 28 comandos carregados
[14/06/2025 15:50:45] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 15:50:45] INFO: ▣ Eventos carregados
[14/06/2025 15:50:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 15:50:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 15:50:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 15:50:45] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 15:50:45] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 15:50:45] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 15:50:45] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 15:50:45] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 15:52:27] INFO: ▣ Sistema de logs inicializado
[14/06/2025 15:52:27] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 15:52:27] INFO: ▣ Banco de dados inicializado
[14/06/2025 15:52:27] INFO: ▣ 28 comandos carregados
[14/06/2025 15:52:27] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 15:52:27] INFO: ▣ Eventos carregados
[14/06/2025 15:52:27] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 15:52:27] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 15:52:27] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 15:52:27] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 15:52:27] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 15:52:27] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 15:52:27] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 15:52:27] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 15:52:27] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 15:52:27] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 15:52:27] INFO: ▣ Servidor web inicializado
[14/06/2025 15:52:28] INFO: 🔄 Registrando comandos slash...
[14/06/2025 15:52:29] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 15:52:29] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 15:52:29] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 15:52:29] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 15:52:29] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 15:52:29] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.0163683}
[14/06/2025 15:53:15] INFO: ▣ Sistema de logs inicializado
[14/06/2025 15:53:15] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 15:53:15] INFO: ▣ Banco de dados inicializado
[14/06/2025 15:53:15] INFO: ▣ 28 comandos carregados
[14/06/2025 15:53:15] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 15:53:15] INFO: ▣ Eventos carregados
[14/06/2025 15:53:15] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 15:53:15] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 15:53:15] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 15:53:15] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 15:53:15] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 15:53:15] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 15:53:15] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 15:53:15] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 15:53:15] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 15:53:15] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 15:53:15] INFO: ▣ Servidor web inicializado
[14/06/2025 15:53:16] INFO: 🔄 Registrando comandos slash...
[14/06/2025 15:53:17] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 15:53:17] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 15:53:17] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 15:53:17] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 15:53:17] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 15:53:17] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":3.975316}
[14/06/2025 15:55:27] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 16:33:00] INFO: ▣ Sistema de logs inicializado
[14/06/2025 16:33:00] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 16:33:00] INFO: 📊 Coleta de métricas iniciada
[14/06/2025 16:33:00] INFO: 📊 Coletor de métricas inicializado
[14/06/2025 16:33:00] INFO: ▣ Banco de dados inicializado
[14/06/2025 16:33:01] INFO: ▣ 29 comandos carregados
[14/06/2025 16:33:01] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 16:33:01] INFO: ▣ Eventos carregados
[14/06/2025 16:33:01] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 16:33:01] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 16:33:01] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 16:33:01] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 16:33:01] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 16:33:01] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 16:33:01] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 16:33:01] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 16:33:01] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 16:33:01] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 16:33:01] INFO: ▣ Servidor web inicializado
[14/06/2025 16:33:01] INFO: 🔥 Hot-reload ativado
[14/06/2025 16:33:01] INFO: 🔥 Hot-reload ativado (modo desenvolvimento)
[14/06/2025 16:33:02] INFO: 🔄 Registrando comandos slash...
[14/06/2025 16:33:03] INFO: ✅ 29 comandos slash registrados globalmente
[14/06/2025 16:33:03] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 16:33:03] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 16:33:03] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 16:33:03] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 16:33:03] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":29,"uptime":4.2123233}
[14/06/2025 16:34:38] INFO: ▣ Sistema de logs inicializado
[14/06/2025 16:34:38] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 16:34:38] INFO: 📊 Coleta de métricas iniciada
[14/06/2025 16:34:38] INFO: 📊 Coletor de métricas inicializado
[14/06/2025 16:34:38] INFO: ▣ Banco de dados inicializado
[14/06/2025 16:34:38] INFO: ▣ 29 comandos carregados
[14/06/2025 16:34:38] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 16:34:38] INFO: ▣ Eventos carregados
[14/06/2025 16:34:38] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 16:34:38] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 16:34:38] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 16:34:38] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 16:34:38] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 16:34:38] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 16:34:38] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 16:34:38] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 16:34:38] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 16:34:38] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 16:34:38] INFO: ▣ Servidor web inicializado
[14/06/2025 16:34:38] INFO: 🔥 Hot-reload ativado
[14/06/2025 16:34:38] INFO: 🔥 Hot-reload ativado (modo desenvolvimento)
[14/06/2025 16:34:40] INFO: 🔄 Registrando comandos slash...
[14/06/2025 16:34:40] INFO: ✅ 29 comandos slash registrados globalmente
[14/06/2025 16:34:40] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 16:34:40] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 16:34:40] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 16:34:40] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 16:34:40] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":29,"uptime":4.2328977}
[14/06/2025 16:35:36] INFO: ▣ Sistema de logs inicializado
[14/06/2025 16:35:36] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 16:35:36] INFO: 📊 Coleta de métricas iniciada
[14/06/2025 16:35:36] INFO: 📊 Coletor de métricas inicializado
[14/06/2025 16:35:36] INFO: ▣ Banco de dados inicializado
[14/06/2025 16:35:36] INFO: ▣ 29 comandos carregados
[14/06/2025 16:35:36] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 16:35:36] INFO: ▣ Eventos carregados
[14/06/2025 16:35:36] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 16:35:36] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 16:35:36] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 16:35:36] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 16:35:36] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 16:35:36] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 16:35:36] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 16:35:36] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 16:35:36] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 16:35:36] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 16:35:36] INFO: ▣ Servidor web inicializado
[14/06/2025 16:35:36] INFO: 🔥 Hot-reload ativado
[14/06/2025 16:35:36] INFO: 🔥 Hot-reload ativado (modo desenvolvimento)
[14/06/2025 16:35:38] INFO: 🔄 Registrando comandos slash...
[14/06/2025 16:35:38] INFO: ✅ 29 comandos slash registrados globalmente
[14/06/2025 16:35:38] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 16:35:38] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 16:35:38] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 16:35:38] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 16:35:38] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":29,"uptime":4.3040827}
[14/06/2025 16:52:39] INFO: ▣ Sistema de logs inicializado
[14/06/2025 16:52:39] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 16:52:39] INFO: 📊 Coleta de métricas iniciada
[14/06/2025 16:52:39] INFO: 📊 Coletor de métricas inicializado
[14/06/2025 16:52:39] INFO: ▣ Banco de dados inicializado
[14/06/2025 16:52:39] INFO: ▣ 29 comandos carregados
[14/06/2025 16:52:39] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 16:52:39] INFO: ▣ Eventos carregados
[14/06/2025 16:52:39] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 16:53:16] INFO: ▣ Sistema de logs inicializado
[14/06/2025 16:53:16] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 16:53:16] INFO: 📊 Coleta de métricas iniciada
[14/06/2025 16:53:16] INFO: 📊 Coletor de métricas inicializado
[14/06/2025 16:53:16] INFO: ▣ Banco de dados inicializado
[14/06/2025 16:53:16] INFO: ▣ 29 comandos carregados
[14/06/2025 16:53:16] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 16:53:16] INFO: ▣ Eventos carregados
[14/06/2025 16:53:16] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 16:53:16] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 16:53:16] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 16:53:16] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 16:53:16] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 16:53:16] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 16:53:16] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 16:53:16] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 16:53:16] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 16:53:16] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 16:53:16] INFO: ▣ Servidor web inicializado
[14/06/2025 16:53:16] INFO: 🔥 Hot-reload ativado
[14/06/2025 16:53:16] INFO: 🔥 Hot-reload ativado (modo desenvolvimento)
[14/06/2025 16:53:17] INFO: 🔄 Registrando comandos slash...
[14/06/2025 16:53:18] INFO: ✅ 29 comandos slash registrados globalmente
[14/06/2025 16:53:18] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 16:53:18] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 16:53:18] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 16:53:18] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 16:53:18] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":29,"uptime":4.126829}
[14/06/2025 16:54:19] INFO: ▣ Sistema de logs inicializado
[14/06/2025 16:54:19] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 16:54:19] INFO: 📊 Coleta de métricas iniciada
[14/06/2025 16:54:19] INFO: 📊 Coletor de métricas inicializado
[14/06/2025 16:54:19] INFO: ▣ Banco de dados inicializado
[14/06/2025 16:54:19] INFO: ▣ 29 comandos carregados
[14/06/2025 16:54:19] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 16:54:20] INFO: ▣ Eventos carregados
[14/06/2025 16:54:20] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 16:54:20] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 16:54:20] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 16:54:20] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 16:54:20] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 16:54:20] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 16:54:20] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 16:54:20] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 16:54:20] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 16:54:20] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 16:54:20] INFO: ▣ Servidor web inicializado
[14/06/2025 16:54:20] INFO: 🔥 Hot-reload ativado
[14/06/2025 16:54:20] INFO: 🔥 Hot-reload ativado (modo desenvolvimento)
[14/06/2025 16:54:21] INFO: 🔄 Registrando comandos slash...
[14/06/2025 16:54:22] INFO: ✅ 29 comandos slash registrados globalmente
[14/06/2025 16:54:22] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 16:54:22] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 16:54:22] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 16:54:23] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 16:54:23] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":29,"uptime":5.5515586}
[14/06/2025 17:16:29] INFO: ▣ Sistema de logs inicializado
[14/06/2025 17:16:29] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 17:16:29] INFO: 📊 Coleta de métricas iniciada
[14/06/2025 17:16:29] INFO: 📊 Coletor de métricas inicializado
[14/06/2025 17:16:29] INFO: ▣ Banco de dados inicializado
[14/06/2025 17:16:29] INFO: ▣ 29 comandos carregados
[14/06/2025 17:16:29] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 17:16:29] INFO: ▣ Eventos carregados
[14/06/2025 17:16:29] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 17:16:29] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 17:16:29] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 17:16:29] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 17:16:29] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 17:16:29] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 17:16:29] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 17:16:29] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 17:16:29] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 17:16:29] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 17:16:29] INFO: ▣ Servidor web inicializado
[14/06/2025 17:16:29] INFO: 🔥 Hot-reload ativado
[14/06/2025 17:16:29] INFO: 🔥 Hot-reload ativado (modo desenvolvimento)
[14/06/2025 17:16:31] INFO: 🔄 Registrando comandos slash...
[14/06/2025 17:16:32] INFO: ✅ 29 comandos slash registrados globalmente
[14/06/2025 17:16:32] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 17:16:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 17:16:32] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 17:16:32] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 17:16:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":29,"uptime":4.3482602}
[14/06/2025 17:17:38] INFO: ▣ Sistema de logs inicializado
[14/06/2025 17:17:38] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 17:17:38] INFO: 📊 Coleta de métricas iniciada
[14/06/2025 17:17:38] INFO: 📊 Coletor de métricas inicializado
[14/06/2025 17:17:38] INFO: ▣ Banco de dados inicializado
[14/06/2025 17:17:38] INFO: ▣ 29 comandos carregados
[14/06/2025 17:17:38] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 17:17:38] INFO: ▣ Eventos carregados
[14/06/2025 17:17:38] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 17:17:38] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 17:17:38] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 17:17:38] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 17:17:38] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 17:17:38] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 17:17:38] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 17:17:38] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 17:17:38] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 17:17:38] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 17:17:38] INFO: ▣ Servidor web inicializado
[14/06/2025 17:17:38] INFO: 🔥 Hot-reload ativado
[14/06/2025 17:17:38] INFO: 🔥 Hot-reload ativado (modo desenvolvimento)
[14/06/2025 17:17:40] INFO: 🔄 Registrando comandos slash...
[14/06/2025 17:17:40] INFO: ✅ 29 comandos slash registrados globalmente
[14/06/2025 17:17:40] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 17:17:40] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 17:17:40] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 17:17:40] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 17:17:40] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":29,"uptime":4.1846899}
[14/06/2025 18:17:40] INFO: Limpeza de logs concluída: 0 arquivos removidos
[14/06/2025 18:36:47] INFO: 🔄 Evento recarregado: interactionCreate
[14/06/2025 18:36:47] INFO: 🔄 Evento recarregado: interactionCreate
[14/06/2025 18:36:48] INFO: 🔄 Comando recarregado: metrics
[14/06/2025 18:36:48] INFO: 🔄 Comando recarregado: metrics
[14/06/2025 18:37:15] INFO: 🔄 Package.json recarregado (v2.0.0)
[14/06/2025 18:37:15] INFO: 🔄 Package.json recarregado (v2.0.0)
[14/06/2025 18:37:15] INFO: 🔄 Variáveis de ambiente recarregadas
[14/06/2025 18:37:15] INFO: 🔄 Variáveis de ambiente recarregadas
[14/06/2025 18:37:16] INFO: 🔄 Variáveis de ambiente recarregadas
[14/06/2025 18:37:16] INFO: 🔄 Variáveis de ambiente recarregadas
[14/06/2025 18:37:16] INFO: 🔄 Comando recarregado: kick
[14/06/2025 18:37:16] INFO: 🔄 Evento recarregado: interactionCreate
[14/06/2025 18:37:16] INFO: ➖ Comando removido: metrics
[14/06/2025 18:37:16] INFO: 🔄 Evento recarregado: interactionCreate
[14/06/2025 18:37:16] INFO: 🔄 Evento recarregado: interactionCreate
[14/06/2025 18:37:16] INFO: 🔄 Comando recarregado: timeout
[14/06/2025 18:37:16] INFO: 🔄 Comando recarregado: mute
[14/06/2025 18:37:17] INFO: 🔄 Comando recarregado: warn
[14/06/2025 18:37:17] INFO: 🔄 Comando recarregado: mute
[14/06/2025 18:37:36] INFO: 🔄 Comando recarregado: timeout
[14/06/2025 18:37:37] INFO: 🔄 Comando recarregado: kick
[14/06/2025 18:37:37] INFO: 🔄 Comando recarregado: warn
[14/06/2025 18:37:37] INFO: 🔄 Comando recarregado: timeout
[14/06/2025 18:37:37] INFO: 🔄 Comando recarregado: kick
[14/06/2025 18:37:44] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 18:38:34] INFO: ▣ Sistema de logs inicializado
[14/06/2025 18:38:34] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 18:38:34] INFO: ▣ Banco de dados inicializado
[14/06/2025 18:38:34] INFO: ▣ 28 comandos carregados
[14/06/2025 18:38:34] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 18:38:34] INFO: ▣ Eventos carregados
[14/06/2025 18:38:34] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 18:38:34] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 18:38:34] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 18:38:34] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 18:38:34] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 18:38:34] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 18:38:34] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 18:38:34] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 18:38:34] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 18:38:34] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 18:38:34] INFO: ▣ Servidor web inicializado
[14/06/2025 18:38:36] INFO: 🔄 Registrando comandos slash...
[14/06/2025 18:38:36] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 18:38:36] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 18:38:36] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 18:38:36] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 18:38:36] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 18:38:36] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.0488924}
[14/06/2025 18:39:52] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 18:39:58] INFO: ▣ Sistema de logs inicializado
[14/06/2025 18:39:58] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 18:39:58] INFO: ▣ Banco de dados inicializado
[14/06/2025 18:39:58] INFO: ▣ 28 comandos carregados
[14/06/2025 18:39:58] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 18:39:58] INFO: ▣ Eventos carregados
[14/06/2025 18:39:58] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 18:39:58] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 18:39:58] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 18:39:58] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 18:39:58] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 18:39:58] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 18:39:58] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 18:39:58] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 18:39:58] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 18:39:58] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 18:39:58] INFO: ▣ Servidor web inicializado
[14/06/2025 18:39:59] INFO: 🔄 Registrando comandos slash...
[14/06/2025 18:40:00] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 18:40:00] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 18:40:00] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 18:40:00] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 18:40:00] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 18:40:00] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.121224}
[14/06/2025 18:40:15] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 18:40:51] INFO: ▣ Sistema de logs inicializado
[14/06/2025 18:40:51] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 18:40:51] INFO: ▣ Banco de dados inicializado
[14/06/2025 18:40:51] INFO: ▣ 28 comandos carregados
[14/06/2025 18:40:51] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 18:40:51] INFO: ▣ Eventos carregados
[14/06/2025 18:40:51] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 18:40:51] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 18:40:51] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 18:40:51] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 18:40:51] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 18:40:51] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 18:40:51] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 18:40:51] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 18:40:51] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 18:40:51] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 18:40:51] INFO: ▣ Servidor web inicializado
[14/06/2025 18:40:52] INFO: 🔄 Registrando comandos slash...
[14/06/2025 18:40:53] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 18:40:53] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 18:40:53] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 18:40:53] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 18:40:53] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 18:40:53] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.2108226}
[14/06/2025 18:42:59] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 18:43:03] INFO: ▣ Sistema de logs inicializado
[14/06/2025 18:43:03] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 18:43:03] INFO: ▣ Banco de dados inicializado
[14/06/2025 18:43:03] INFO: ▣ 28 comandos carregados
[14/06/2025 18:43:03] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 18:43:03] INFO: ▣ Eventos carregados
[14/06/2025 18:43:03] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 18:43:03] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 18:43:03] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 18:43:03] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 18:43:19] INFO: ▣ Sistema de logs inicializado
[14/06/2025 18:43:19] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 18:43:19] INFO: ▣ Banco de dados inicializado
[14/06/2025 18:43:19] INFO: ▣ 28 comandos carregados
[14/06/2025 18:43:19] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 18:43:19] INFO: ▣ Eventos carregados
[14/06/2025 18:43:19] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 18:43:19] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 18:43:19] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 18:43:19] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 18:48:55] INFO: ▣ Sistema de logs inicializado
[14/06/2025 18:48:55] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 18:48:55] INFO: ▣ Banco de dados inicializado
[14/06/2025 18:48:55] INFO: ▣ 28 comandos carregados
[14/06/2025 18:48:55] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 18:48:56] INFO: ▣ Eventos carregados
[14/06/2025 18:48:56] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 18:48:56] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 18:48:56] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 18:48:56] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 18:49:45] INFO: ▣ Sistema de logs inicializado
[14/06/2025 18:49:45] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 18:49:45] INFO: ▣ Banco de dados inicializado
[14/06/2025 18:49:45] INFO: ▣ 28 comandos carregados
[14/06/2025 18:49:45] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 18:49:45] INFO: ▣ Eventos carregados
[14/06/2025 18:49:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 18:49:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 18:49:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 18:49:45] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 18:50:34] INFO: ▣ Sistema de logs inicializado
[14/06/2025 18:50:34] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 18:50:34] INFO: ▣ Banco de dados inicializado
[14/06/2025 18:50:34] INFO: ▣ 28 comandos carregados
[14/06/2025 18:50:34] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 18:50:34] INFO: ▣ Eventos carregados
[14/06/2025 18:50:34] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 18:50:34] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 18:50:34] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 18:50:34] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 18:50:34] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 18:50:34] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 18:50:34] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 18:50:34] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 18:51:26] INFO: ▣ Sistema de logs inicializado
[14/06/2025 18:51:26] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 18:51:26] INFO: ▣ Banco de dados inicializado
[14/06/2025 18:51:26] INFO: ▣ 28 comandos carregados
[14/06/2025 18:51:26] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 18:51:26] INFO: ▣ Eventos carregados
[14/06/2025 18:51:26] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 18:51:26] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 18:51:26] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 18:51:26] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 18:51:26] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 18:51:26] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 18:51:26] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 18:51:26] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 18:51:26] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 18:51:26] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 18:51:26] INFO: ▣ Servidor web inicializado
[14/06/2025 18:51:27] INFO: 🔄 Registrando comandos slash...
[14/06/2025 18:51:28] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 18:51:28] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 18:51:28] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 18:51:28] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 18:51:28] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 18:51:28] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.6772524}
[14/06/2025 19:12:56] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 19:13:56] INFO: ▣ Sistema de logs inicializado
[14/06/2025 19:13:56] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 19:13:56] INFO: ▣ Banco de dados inicializado
[14/06/2025 19:13:57] INFO: ▣ 28 comandos carregados
[14/06/2025 19:13:57] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 19:13:57] INFO: ▣ Eventos carregados
[14/06/2025 19:13:57] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 19:13:57] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 19:13:57] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 19:13:57] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 19:13:57] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 19:13:57] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 19:13:57] INFO: 🚀 Sistema de Tickets Avançado inicializado
[14/06/2025 19:13:57] INFO: 🛡️ Sistema de Verificação Avançado inicializado
[14/06/2025 19:13:57] INFO: 🎭 Sistema de Auto-Roles Avançado inicializado
[14/06/2025 19:13:57] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 19:13:57] INFO: ▣ Servidor web inicializado
[14/06/2025 19:13:58] INFO: 🔄 Registrando comandos slash...
[14/06/2025 19:13:58] INFO: ✅ 28 comandos slash registrados globalmente
[14/06/2025 19:13:58] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 19:13:58] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 19:13:58] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 19:13:59] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 19:13:59] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":3.8005247}
[14/06/2025 19:15:04] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 19:15:11] INFO: ▣ Sistema de logs inicializado
[14/06/2025 19:15:11] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 19:15:11] INFO: ▣ Banco de dados inicializado
[14/06/2025 19:15:11] INFO: ▣ 27 comandos carregados
[14/06/2025 19:15:11] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 19:15:11] INFO: ▣ Eventos carregados
[14/06/2025 19:15:11] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 19:15:11] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 19:15:11] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 19:15:11] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 19:15:11] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 19:15:11] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 19:15:11] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 19:15:11] INFO: ▣ Servidor web inicializado
[14/06/2025 19:15:12] INFO: 🔄 Registrando comandos slash...
[14/06/2025 19:15:13] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 19:15:13] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 19:15:13] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 19:15:13] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 19:15:13] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 19:15:13] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.3263431}
[14/06/2025 19:16:49] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[14/06/2025 19:18:37] INFO: ▣ Iniciando encerramento gracioso...
[14/06/2025 19:18:42] INFO: ▣ Sistema de logs inicializado
[14/06/2025 19:18:42] INFO: ▣ Gerenciador de configurações inicializado
[14/06/2025 19:18:42] INFO: ▣ Banco de dados inicializado
[14/06/2025 19:18:42] INFO: ▣ 27 comandos carregados
[14/06/2025 19:18:42] INFO: ▣ Gerenciador de estados de comandos inicializado
[14/06/2025 19:18:42] INFO: ▣ Eventos carregados
[14/06/2025 19:18:42] INFO: 🛡️ Sistema Anti-Raid inicializado
[14/06/2025 19:18:42] INFO: 🤖 Sistema de Auto-Moderação inicializado
[14/06/2025 19:18:42] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[14/06/2025 19:18:42] INFO: ▣ Sistemas de moderação inicializados
[14/06/2025 19:18:42] INFO: ▣ Sistema de Tickets inicializado
[14/06/2025 19:18:42] INFO: ▣ Sistema de Analytics de Moderação inicializado
[14/06/2025 19:18:42] INFO: ▣ Sistema de Backup inicializado
[14/06/2025 19:18:42] INFO: ▣ Servidor web inicializado
[14/06/2025 19:18:44] INFO: 🔄 Registrando comandos slash...
[14/06/2025 19:18:45] INFO: ✅ 27 comandos slash registrados globalmente
[14/06/2025 19:18:45] INFO: 🔍 Verificando configurações dos servidores...
[14/06/2025 19:18:45] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[14/06/2025 19:18:45] INFO: 📋 Cache de configurações carregado para 3 servidores
[14/06/2025 19:18:45] INFO: ✅ Tarefas periódicas inicializadas
[14/06/2025 19:18:45] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":27,"uptime":4.5256722}
[14/06/2025 20:18:45] INFO: Limpeza de logs concluída: 0 arquivos removidos
[14/06/2025 21:18:45] INFO: Limpeza de logs concluída: 0 arquivos removidos
[14/06/2025 22:18:45] INFO: Limpeza de logs concluída: 0 arquivos removidos
[14/06/2025 23:18:45] INFO: Limpeza de logs concluída: 0 arquivos removidos
