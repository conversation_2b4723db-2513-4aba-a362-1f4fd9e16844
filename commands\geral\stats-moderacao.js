/**
 * ========================================
 * COMANDO: STATS MODERAÇÃO
 * Estatísticas focadas em moderação
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('stats-moderacao')
        .setNameLocalizations({
            'pt-BR': 'stats-moderacao'
        })
        .setDescription('Mostra estatísticas de moderação do servidor')
        .setDescriptionLocalizations({
            'pt-BR': 'Exibe estatísticas detalhadas sobre moderação, segurança e atividade do servidor'
        }),

    category: 'geral',
    cooldown: 10,

    async execute(interaction) {
        try {
            await interaction.deferReply();

            const guild = interaction.guild;
            const client = interaction.client;

            // Buscar dados de moderação do banco
            let moderationStats = {
                warnings: 0,
                bans: 0,
                kicks: 0,
                timeouts: 0,
                automod_actions: 0,
                ai_detections: 0,
                raid_attempts: 0
            };

            try {
                // Buscar estatísticas do banco de dados
                const logs = await client.database.all(
                    'SELECT action, COUNT(*) as count FROM moderation_logs WHERE guild_id = ? AND created_at > datetime("now", "-30 days") GROUP BY action',
                    [guild.id]
                );

                if (logs && Array.isArray(logs)) {
                    logs.forEach(log => {
                        switch(log.action) {
                            case 'warn':
                                moderationStats.warnings = log.count;
                                break;
                            case 'ban':
                                moderationStats.bans = log.count;
                                break;
                            case 'kick':
                                moderationStats.kicks = log.count;
                                break;
                            case 'timeout':
                                moderationStats.timeouts = log.count;
                                break;
                            case 'automod':
                                moderationStats.automod_actions = log.count;
                                break;
                            case 'ai_detection':
                                moderationStats.ai_detections = log.count;
                                break;
                        }
                    });
                }
            } catch (error) {
                console.log('Erro ao buscar estatísticas:', error);
            }

            // Informações do servidor
            const totalMembers = guild.memberCount;
            const onlineMembers = guild.members.cache.filter(m => m.presence?.status !== 'offline').size;
            const botCount = guild.members.cache.filter(m => m.user.bot).size;
            const humanCount = totalMembers - botCount;

            // Informações de canais
            const textChannels = guild.channels.cache.filter(c => c.type === 0).size;
            const voiceChannels = guild.channels.cache.filter(c => c.type === 2).size;
            const categories = guild.channels.cache.filter(c => c.type === 4).size;

            // Calcular nível de segurança
            let securityLevel = 0;
            let securityFeatures = [];

            // Verificar sistemas ativos
            if (client.autoMod) {
                securityLevel += 25;
                securityFeatures.push('Auto-Moderação');
            }
            if (client.aiMod) {
                securityLevel += 30;
                securityFeatures.push('IA de Moderação');
            }
            if (client.antiRaid) {
                securityLevel += 25;
                securityFeatures.push('Anti-Raid');
            }
            if (client.tickets) {
                securityLevel += 10;
                securityFeatures.push('Sistema de Tickets');
            }
            if (client.backup) {
                securityLevel += 10;
                securityFeatures.push('Sistema de Backup');
            }

            const securityEmoji = securityLevel >= 80 ? '' : securityLevel >= 60 ? '' : '';
            const securityText = securityLevel >= 80 ? 'Excelente' : securityLevel >= 60 ? 'Boa' : 'Básica';

            // Criar embed
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('Estatísticas de Moderação')
                .setDescription(`**${guild.name}**- Relatório dos últimos 30 dias`)
                .setThumbnail(guild.iconURL() || client.user.displayAvatarURL())
                .addFields(
                    {
                        name: 'Informações do Servidor',
                        value: [
                            `**Total de Membros:**${totalMembers.toLocaleString('pt-BR')}`,
                            `**Membros Online:**${onlineMembers.toLocaleString('pt-BR')}`,
                            `**Humanos:**${humanCount.toLocaleString('pt-BR')}`,
                            `**Bots:**${botCount.toLocaleString('pt-BR')}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: 'Canais',
                        value: [
                            `**Texto:**${textChannels}`,
                            `**Voz:**${voiceChannels}`,
                            `**Categorias:**${categories}`,
                            `**Total:**${textChannels + voiceChannels + categories}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: `${securityEmoji} Nível de Segurança`,
                        value: [
                            `**Status:**${securityText} (${securityLevel}%)`,
                            `**Sistemas Ativos:**${securityFeatures.length}/5`,
                            '',
                            securityFeatures.join('\n') || 'Nenhum sistema ativo'
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: 'Ações de Moderação (30 dias)',
                        value: [
                            `**Advertências:**${moderationStats.warnings.toLocaleString('pt-BR')}`,
                            `**Banimentos:**${moderationStats.bans.toLocaleString('pt-BR')}`,
                            `**Expulsões:**${moderationStats.kicks.toLocaleString('pt-BR')}`,
                            `**Timeouts:**${moderationStats.timeouts.toLocaleString('pt-BR')}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: 'Moderação Automática (30 dias)',
                        value: [
                            `**Ações Auto-Mod:**${moderationStats.automod_actions.toLocaleString('pt-BR')}`,
                            `**Detecções de IA:**${moderationStats.ai_detections.toLocaleString('pt-BR')}`,
                            `**Tentativas de Raid:**${moderationStats.raid_attempts.toLocaleString('pt-BR')}`,
                            `**Total Automático:**${(moderationStats.automod_actions + moderationStats.ai_detections + moderationStats.raid_attempts).toLocaleString('pt-BR')}`
                        ].join('\n'),
                        inline: true
                    }
                )
                .setFooter({
                    text: `Nodex | Moderação • Solicitado por ${interaction.user.username}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            // Adicionar campo de recomendações se necessário
            if (securityLevel < 80) {
                let recommendations = [];
                
                if (!client.autoMod) recommendations.push('• Ativar Auto-Moderação');
                if (!client.aiMod) recommendations.push('• Configurar IA de Moderação');
                if (!client.antiRaid) recommendations.push('• Ativar Sistema Anti-Raid');
                if (!client.tickets) recommendations.push('• Configurar Sistema de Tickets');
                if (!client.backup) recommendations.push('• Ativar Sistema de Backup');

                if (recommendations.length > 0) {
                    embed.addFields({
                        name: 'Recomendações para Melhorar a Segurança',
                        value: recommendations.join('\n'),
                        inline: false
                    });
                }
            }

            await interaction.editReply({
                embeds: [embed]
            });

        } catch (error) {
            console.error('Erro no comando stats-moderacao:', error);
            
            const errorMessage = 'Ocorreu um erro ao buscar as estatísticas de moderação!';
            
            if (interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
};
