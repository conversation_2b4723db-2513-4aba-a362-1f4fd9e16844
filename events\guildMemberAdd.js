const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'guildMemberAdd',
    async execute(member, client) {
        try {
            console.log(`📥 [MEMBER JOIN] ${member.user.tag} entrou em ${member.guild.name}`);

            // Obter configurações do servidor
            const guildConfig = await client.db.getGuildConfig(member.guild.id);
            
            // Registrar entrada nos logs
            await logMember<PERSON>oin(member, client, guildConfig);
            
            // Aplicar auto-roles se configurado
            await applyAutoRoles(member, client, guildConfig);

            // Processar sistema de verificação (ATUALIZADO - SEGUINDO PADRÃO DOS MÓDULOS FUNCIONAIS)
            if (client.verificationSystem) {
                console.log(`🛡️ [VERIFICATION] Processando novo membro: ${member.user.tag} em ${member.guild.name}`);
                await client.verificationSystem.processNewMember(member);
            } else {
                console.log(`🛡️ [VERIFICATION] Sistema de verificação não encontrado, usando fallback`);
                await handleVerificationSystem(member, client);
            }

            // Processar sistema anti-raid (SEGUINDO PADRÃO DOS MÓDULOS FUNCIONAIS)
            if (client.antiRaid) {
                await client.antiRaid.processMemberJoin(member);
            }



            // Atualizar estatísticas
            await updateServerStats(member, client);
            
            // Sistema de boas-vindas desativado para foco em moderação
            // await sendWelcomeMessage(member, client, guildConfig);

        } catch (error) {
            client.logger.error('Erro no evento guildMemberAdd:', error);
        }
    }
};

/**
 * Sistema de boas-vindas removido - focado apenas em moderação
 */
async function sendWelcomeMessage(member, client, guildConfig) {
    // Sistema de boas-vindas desativado para foco em moderação
    return;
}

/**
 * Sistema de DM de boas-vindas removido - focado apenas em moderação
 */
async function sendWelcomeDM(member, client, settings) {
    // Sistema de DM de boas-vindas desativado para foco em moderação
    return;
}



/**
 * Registra a entrada no canal de logs
 */
async function logMemberJoin(member, client, guildConfig) {
    try {
        const guild = member.guild;
        const user = member.user;

        // Verificar se logs estão habilitados
        if (!guildConfig?.log_enabled) {
            console.log(`📥 [MEMBER JOIN] Logs desabilitados para ${guild.name}`);
            return;
        }

        // Verificar se canal de logs está configurado
        const logChannelId = guildConfig?.log_channel_id || guildConfig?.log_channel;
        if (!logChannelId) {
            console.log(`📥 [MEMBER JOIN] Canal de logs não configurado para ${guild.name}`);
            return;
        }

        const logChannel = guild.channels.cache.get(logChannelId);
        if (!logChannel || !logChannel.isTextBased()) {
            console.log(`📥 [MEMBER JOIN] Canal de logs não encontrado: ${logChannelId}`);
            return;
        }

        console.log(`📥 [MEMBER JOIN] Enviando log para #${logChannel.name} em ${guild.name}`);

        // Verificar permissões
        const permissions = logChannel.permissionsFor(guild.members.me);
        if (!permissions.has(['SendMessages', 'EmbedLinks'])) {
            return;
        }

        // Calcular idade da conta
        const accountAge = Date.now() - user.createdTimestamp;
        const ageDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
        const ageHours = Math.floor(accountAge / (1000 * 60 * 60));

        // Determinar cor baseada na idade da conta
        let embedColor = '#2ecc71'; // Verde (seguro)
        if (ageDays < 1) embedColor = '#e74c3c'; // Vermelho (muito novo)
        else if (ageDays < 7) embedColor = '#f39c12'; // Laranja (novo)
        else if (ageDays < 30) embedColor = '#f1c40f'; // Amarelo (relativamente novo)

        // Criar embed de log
        const logEmbed = new EmbedBuilder()
            .setColor(embedColor)
            .setTitle('📥 Membro Entrou')
            .setDescription(`${user} entrou no servidor`)
            .addFields(
                { 
                    name: '👤 Usuário', 
                    value: `${user.tag} (${user.id})`, 
                    inline: true 
                },
                { 
                    name: '📅 Conta Criada', 
                    value: ageDays > 0 
                        ? `${ageDays} dia(s) atrás` 
                        : `${ageHours} hora(s) atrás`, 
                    inline: true 
                },
                { 
                    name: '📊 Membro Nº', 
                    value: `${guild.memberCount}`, 
                    inline: true 
                },
                { 
                    name: '🖼️ Avatar', 
                    value: user.avatar ? '✅ Personalizado' : '❌ Padrão', 
                    inline: true 
                },
                { 
                    name: '⏰ Horário', 
                    value: `<t:${Math.floor(Date.now() / 1000)}:F>`, 
                    inline: true 
                }
            )
            .setThumbnail(user.displayAvatarURL({ size: 128 }))
            .setTimestamp()
            .setFooter({ text: 'Nova Moderação Bot' });

        // Adicionar campo de risco se a conta for muito nova
        if (ageDays < 7) {
            logEmbed.addFields({
                name: '⚠️ Aviso',
                value: ageDays < 1 
                    ? '🔴 Conta muito nova (menos de 1 dia)'
                    : '🟡 Conta nova (menos de 7 dias)',
                inline: false
            });
        }

        await logChannel.send({ embeds: [logEmbed] });

        client.logger.debug(`Log de entrada enviado para ${user.tag}`);

    } catch (error) {
        client.logger.error('Erro ao enviar log de entrada:', error);
    }
}

/**
 * Processar sistema de verificação para novos membros
 */
async function handleVerificationSystem(member, client) {
    try {
        const guild = member.guild;

        // Buscar configuração de verificação
        const verificationConfig = client.database.db.prepare(`
            SELECT * FROM verification_config WHERE guild_id = ? AND enabled = 1
        `).get(guild.id);

        if (!verificationConfig) return;

        // Criar registro de verificação para o novo membro
        client.database.db.prepare(`
            INSERT OR REPLACE INTO member_verification
            (guild_id, user_id, verified, attempts, last_attempt)
            VALUES (?, ?, 0, 0, NULL)
        `).run(guild.id, member.id);

        // Aplicar cargo de não verificado se configurado
        if (verificationConfig.unverified_role_id) {
            const unverifiedRole = guild.roles.cache.get(verificationConfig.unverified_role_id);
            if (unverifiedRole && unverifiedRole.position < guild.members.me.roles.highest.position) {
                await member.roles.add(unverifiedRole, 'Novo membro - não verificado');
                client.logger.info(`Cargo de não verificado aplicado a ${member.user.tag}`);
            }
        }

        // Enviar DM com instruções de verificação
        await sendVerificationInstructions(member, verificationConfig);

    } catch (error) {
        client.logger.error('Erro ao processar sistema de verificação:', error);
    }
}



/**
 * Enviar instruções de verificação por DM
 */
async function sendVerificationInstructions(member, config) {
    try {
        const EmbedStyles = require('../utils/EmbedStyles');
        const embedStyles = new EmbedStyles();

        const channel = member.guild.channels.cache.get(config.channel_id);
        if (!channel) return;

        const instructionsEmbed = {
            color: parseInt(embedStyles.colors.info.replace('#', ''), 16),
            title: `${embedStyles.icons.shield} ${embedStyles.format.bold('Bem-vindo(a) ao ' + member.guild.name + '!')}`,
            description: `**Para acessar o servidor, você precisa se verificar primeiro**\n\n${embedStyles.format.italic('Siga as instruções abaixo para completar sua verificação')}`,
            fields: [
                {
                    name: `${embedStyles.icons.info} **Como se Verificar**`,
                    value: `1️⃣ Vá para o canal ${channel}\n2️⃣ ${getMethodInstructions(config.method)}\n3️⃣ Aguarde a confirmação\n4️⃣ Aproveite o servidor!`,
                    inline: false
                },
                {
                    name: `${embedStyles.icons.rules} **Regras Importantes**`,
                    value: config.rules_text || 'Leia e aceite as regras do servidor para continuar.',
                    inline: false
                },
                {
                    name: `${embedStyles.icons.warning} **Atenção**`,
                    value: `• Você tem **${config.timeout_minutes} minutos** para se verificar\n• Sem verificação, o acesso será limitado\n• Em caso de dúvidas, contate a moderação`,
                    inline: false
                }
            ],
            timestamp: new Date().toISOString(),
            footer: {
                text: 'Nodex | Moderação • Sistema de Verificação',
                icon_url: member.guild.iconURL()
            },
            thumbnail: {
                url: member.guild.iconURL({ dynamic: true })
            }
        };

        await member.send({ embeds: [instructionsEmbed] });

    } catch (error) {
        // Usuário não pode receber DM
        member.client.logger.debug(`Não foi possível enviar DM de verificação para ${member.user.tag}`);
    }
}

/**
 * Obter instruções do método de verificação
 */
function getMethodInstructions(method) {
    const instructions = {
        'reaction': 'Clique no botão "Iniciar Verificação"',
        'captcha': 'Use o comando /verificar e resolva o captcha',
        'manual': 'Aguarde a verificação manual da equipe',
        'combined': 'Clique no botão e resolva o captcha'
    };
    return instructions[method] || 'Siga as instruções no canal';
}

/**
 * Atualiza estatísticas do servidor
 */
async function updateServerStats(member, client) {
    try {
        const guild = member.guild;

        // Atualizar estatísticas no banco (se houver tabela de estatísticas)
        // Por enquanto, apenas log
        client.logger.debug(`Estatísticas atualizadas: ${guild.name} agora tem ${guild.memberCount} membros`);

        // Aqui poderia ser implementado um sistema de estatísticas mais complexo
        // como gráficos de crescimento, picos de entrada, etc.

    } catch (error) {
        client.logger.error('Erro ao atualizar estatísticas do servidor:', error);
    }
}
