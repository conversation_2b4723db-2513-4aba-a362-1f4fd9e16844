/**
 * ========================================
 * COMANDO: AVATAR
 * Mostra o avatar de um usuário
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('avatar')
        .setDescription('Mostra o avatar de um usuário')
        .setDescriptionLocalizations({
            'pt-BR': 'Mostra o avatar de um usuário'
        })
        .addUserOption(option =>
            option.setName('usuario')
                .setNameLocalizations({ 'pt-BR': 'usuário' })
                .setDescription('Usuário para ver o avatar')
                .setDescriptionLocalizations({ 'pt-BR': 'Usuário para visualizar o avatar' })
                .setRequired(false)
        ),
    
    category: 'utilidades',
    cooldown: 3,
    
    async execute(interaction) {
        try {
            const targetUser = interaction.options.getUser('usuario') || interaction.user;
            
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle(`️ Avatar de ${targetUser.tag}`)
                .setImage(targetUser.displayAvatarURL({ dynamic: true, size: 512 }))
                .addFields(
                    {
                        name: 'Links',
                        value: `[PNG](${targetUser.displayAvatarURL({ extension: 'png', size: 512 })}) | [JPG](${targetUser.displayAvatarURL({ extension: 'jpg', size: 512 })}) | [WEBP](${targetUser.displayAvatarURL({ extension: 'webp', size: 512 })})`,
                        inline: false
                    }
                )
                .setFooter({
                    text: `ID: ${targetUser.id} • Nodex | Moderação`,
                    iconURL: interaction.client.user.displayAvatarURL()
                })
                .setTimestamp();

            await interaction.reply({ embeds: [embed] });

        } catch (error) {
            console.error('Erro no comando avatar:', error);
            await interaction.reply({
                content: 'Erro ao buscar avatar!',
                ephemeral: true
            });
        }
    }
};
