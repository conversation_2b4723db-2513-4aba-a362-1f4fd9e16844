/**
 * ========================================
 * COMANDO: SETUP-PANELS (CONFIGURAR PAINÉIS)
 * Cria painéis de demonstração para todos os sistemas
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-panels')
        .setNameLocalizations({
            'pt-BR': 'configurar-paineis'
        })
        .setDescription('Configura painéis de demonstração para sistemas de moderação')
        .setDescriptionLocalizations({
            'pt-BR': 'Cria painéis de demonstração para moderação, analytics, etc.'
        })
        .setDefaultMemberPermissions(8) // ADMINISTRATOR
        .addChannelOption(option =>
            option.setName('canal')
                .setDescription('Canal onde criar os painéis')
                .setDescriptionLocalizations({ 'pt-BR': 'Canal onde criar os painéis de demonstração' })
                .setRequired(true)
        ),
    
    category: 'admin',
    cooldown: 10,
    
    async execute(interaction) {
        try {
            const channel = interaction.options.getChannel('canal');
            const guildId = interaction.guild.id;

            await interaction.deferReply({ ephemeral: true });

            // Verificar permissões no canal
            const permissions = channel.permissionsFor(interaction.guild.members.me);
            if (!permissions.has(['SendMessages', 'EmbedLinks'])) {
                return await interaction.editReply({
                    content: 'Não tenho permissão para enviar mensagens nesse canal!'
                });
            }

            let results = [];

            // 🛡️ PAINEL DE MODERAÇÃO
            try {
                const moderationEmbed = new EmbedBuilder()
                    .setColor('#ff6b6b')
                    .setTitle('🛡️ Painel de Moderação')
                    .setDescription('Sistema de moderação ativo e funcionando!')
                    .addFields(
                        { name: '🔨 Auto-Moderação', value: 'Ativa', inline: true },
                        { name: '🛡️ Anti-Raid', value: 'Ativa', inline: true },
                        { name: '🤖 IA Moderação', value: 'Ativa', inline: true }
                    )
                    .setFooter({ text: 'Nodex | Sistema de Moderação' })
                    .setTimestamp();

                await channel.send({ embeds: [moderationEmbed] });
                results.push('✅ Painel de Moderação criado');
            } catch (error) {
                results.push('❌ Erro no painel de Moderação');
                console.error('Erro ao criar painel de moderação:', error);
            }

            // 📊 PAINEL DE ANALYTICS
            try {
                const analyticsEmbed = new EmbedBuilder()
                    .setColor('#4ecdc4')
                    .setTitle('📊 Painel de Analytics')
                    .setDescription('Sistema de analytics e estatísticas!')
                    .addFields(
                        { name: '📈 Mensagens', value: 'Rastreando', inline: true },
                        { name: '👥 Usuários', value: 'Monitorando', inline: true },
                        { name: '📋 Comandos', value: 'Registrando', inline: true }
                    )
                    .setFooter({ text: 'Nodex | Sistema de Analytics' })
                    .setTimestamp();

                await channel.send({ embeds: [analyticsEmbed] });
                results.push('✅ Painel de Analytics criado');
            } catch (error) {
                results.push('❌ Erro no painel de Analytics');
                console.error('Erro ao criar painel de analytics:', error);
            }

            // Criar embed de resultado
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('️ Painéis Configurados')
                .setDescription(`Painéis criados no canal ${channel}:`)
                .addFields({
                    name: 'Resultados',
                    value: results.join('\n'),
                    inline: false
                })
                .setFooter({
                    text: 'Nodex | Moderação • Configuração de Painéis',
                    iconURL: interaction.client.user.displayAvatarURL()
                })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Erro no comando setup-panels:', error);
            
            if (interaction.deferred) {
                await interaction.editReply({
                    content: 'Erro ao configurar painéis!'
                });
            } else {
                await interaction.reply({
                    content: 'Erro ao configurar painéis!',
                    ephemeral: true
                });
            }
        }
    }
};
