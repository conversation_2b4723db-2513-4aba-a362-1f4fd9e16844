/**
 * ========================================
 * COMANDO: SERVERINFO
 * Mostra informações detalhadas do servidor
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('serverinfo')
        .setDescription('Mostra informações detalhadas do servidor com foco em moderação'),

    async execute(interaction) {
        const client = interaction.client;
        const guild = interaction.guild;

        if (!guild) {
            return await interaction.reply({
                content: 'Este comando só pode ser usado em servidores!',
                ephemeral: true
            });
        }

        try {
            // Buscar informações completas do servidor
            const fetchedGuild = await guild.fetch();
            
            // Calcular estatísticas de membros
            const members = await guild.members.fetch();
            const humans = members.filter(member => !member.user.bot).size;
            const bots = members.filter(member => member.user.bot).size;
            const online = members.filter(member => member.presence?.status === 'online').size;
            const idle = members.filter(member => member.presence?.status === 'idle').size;
            const dnd = members.filter(member => member.presence?.status === 'dnd').size;
            const offline = members.size - online - idle - dnd;

            // Contar canais por tipo
            const textChannels = guild.channels.cache.filter(channel => channel.type === 0).size;
            const voiceChannels = guild.channels.cache.filter(channel => channel.type === 2).size;
            const categories = guild.channels.cache.filter(channel => channel.type === 4).size;
            const threads = guild.channels.cache.filter(channel => channel.isThread()).size;

            // Informações de boost
            const boostLevel = guild.premiumTier;
            const boostCount = guild.premiumSubscriptionCount || 0;
            const boostEmojis = ['', '', '', ''];

            // Recursos do servidor
            const features = guild.features.map(feature => {
                const featureNames = {
                    ANIMATED_BANNER: 'Banner Animado',
                    ANIMATED_ICON: 'Ícone Animado',
                    BANNER: '️ Banner',
                    COMMERCE: 'Comércio',
                    COMMUNITY: 'Comunidade',
                    DISCOVERABLE: 'Descobrível',
                    FEATURABLE: 'Destacável',
                    INVITE_SPLASH: 'Splash de Convite',
                    MEMBER_VERIFICATION_GATE_ENABLED: 'Verificação de Membros',
                    NEWS: 'Canais de Notícias',
                    PARTNERED: 'Parceiro',
                    PREVIEW_ENABLED: '️ Preview Habilitado',
                    VANITY_URL: 'URL Personalizada',
                    VERIFIED: 'Verificado',
                    VIP_REGIONS: 'Regiões VIP',
                    WELCOME_SCREEN_ENABLED: 'Tela de Boas-vindas'
                };
                return featureNames[feature] || feature;
            });

            // Criar embed principal
            const serverEmbed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle(`${guild.name}`)
                .setThumbnail(guild.iconURL({ size: 256 }))
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação' });

            // Banner se disponível
            if (guild.bannerURL()) {
                serverEmbed.setImage(guild.bannerURL({ size: 1024 }));
            }

            // Informações básicas
            serverEmbed.addFields(
                {
                    name: '🆔 ID do Servidor',
                    value: `\`${guild.id}\``,
                    inline: true
                },
                {
                    name: 'Proprietário',
                    value: `<@${guild.ownerId}>`,
                    inline: true
                },
                {
                    name: 'Criado em',
                    value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:F>\n(<t:${Math.floor(guild.createdTimestamp / 1000)}:R>)`,
                    inline: false
                }
            );

            // Estatísticas de membros
            serverEmbed.addFields(
                {
                    name: 'Membros',
                    value: `**Total:**${guild.memberCount}\n**Humanos:**${humans}\n**Bots:**${bots}`,
                    inline: true
                },
                {
                    name: 'Status',
                    value: `${online} Online\n ${idle} Ausente\n ${dnd} Ocupado\n ${offline} Offline`,
                    inline: true
                },
                {
                    name: 'Canais',
                    value: `**Total:**${guild.channels.cache.size}\n**Texto:**${textChannels}\n**Voz:**${voiceChannels}\n**Categorias:**${categories}${threads > 0 ?`\n**Threads:** ${threads}`: ''}`,
                    inline: true
                }
            );

            // Informações de boost
            if (boostLevel > 0 || boostCount > 0) {
                serverEmbed.addFields({
                    name: `${boostEmojis[boostLevel]} Boost`,
                    value: `**Nível:**${boostLevel}\n**Boosts:**${boostCount}`,
                    inline: true
                });
            }

            // Cargos
            const roleCount = guild.roles.cache.size - 1; // -1 para excluir @everyone
            serverEmbed.addFields({
                name: 'Cargos',
                value: `${roleCount} cargo(s)`,
                inline: true
            });

            // Emojis
            const emojiCount = guild.emojis.cache.size;
            const animatedEmojis = guild.emojis.cache.filter(emoji => emoji.animated).size;
            const staticEmojis = emojiCount - animatedEmojis;
            
            if (emojiCount > 0) {
                serverEmbed.addFields({
                    name: 'Emojis',
                    value: `**Total:**${emojiCount}\n**Estáticos:**${staticEmojis}\n**Animados:**${animatedEmojis}`,
                    inline: true
                });
            }

            // Nível de verificação
            const verificationLevels = {
                0: 'Nenhuma',
                1: 'Email verificado',
                2: 'Registrado há 5+ minutos',
                3: 'Membro há 10+ minutos',
                4: 'Telefone verificado'
            };

            serverEmbed.addFields({
                name: 'Verificação',
                value: verificationLevels[guild.verificationLevel] || 'Desconhecido',
                inline: true
            });

            // Filtro de conteúdo explícito
            const explicitFilters = {
                0: 'Desabilitado',
                1: 'Membros sem cargo',
                2: 'Todos os membros'
            };

            serverEmbed.addFields({
                name: 'Filtro de Conteúdo',
                value: explicitFilters[guild.explicitContentFilter] || 'Desconhecido',
                inline: true
            });

            // Recursos especiais
            if (features.length > 0) {
                serverEmbed.addFields({
                    name: 'Recursos Especiais',
                    value: features.slice(0, 10).join('\n') + (features.length > 10 ? '\n...' : ''),
                    inline: false
                });
            }

            // URL personalizada se disponível
            if (guild.vanityURLCode) {
                serverEmbed.addFields({
                    name: 'URL Personalizada',
                    value: `discord.gg/${guild.vanityURLCode}`,
                    inline: true
                });
            }

            // Descrição se disponível
            if (guild.description) {
                serverEmbed.addFields({
                    name: 'Descrição',
                    value: guild.description,
                    inline: false
                });
            }

            // Informações de moderação do bot
            const guildConfig = client.database.getGuildConfig(guild.id);
            if (guildConfig) {
                const modInfo = [];
                if (guildConfig.auto_mod_enabled) modInfo.push('Auto-moderação');
                if (guildConfig.anti_raid_enabled) modInfo.push('Anti-raid');
                if (guildConfig.log_channel) modInfo.push('Logs configurados');
                
                if (modInfo.length > 0) {
                    serverEmbed.addFields({
                        name: 'Configurações do Bot',
                        value: modInfo.join('\n'),
                        inline: false
                    });
                }
            }

            await interaction.reply({ embeds: [serverEmbed] });

            client.logger.info(`Comando serverinfo executado por ${interaction.user.tag} no servidor ${guild.name}`);

        } catch (error) {
            client.logger.error('Erro no comando serverinfo:', error);

            await interaction.reply({
                content: 'Ocorreu um erro ao buscar informações do servidor.',
                ephemeral: true
            });
        }
    },

    cooldown: 5,
    category: 'utilidades',
    examples: ['/serverinfo']
};
