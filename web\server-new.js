/**
 * ========================================
 * SERVIDOR WEB NOVO - DASHBOARD FUNCIONAL
 * Versão completamente reescrita do zero
 * ========================================
 */

const express = require('express');
const path = require('path');
const session = require('express-session');
const passport = require('passport');
const cors = require('cors');
const http = require('http');
const RealTimeIntegration = require('./realtime-integration');
const APIRoutes = require('./api-routes');

class WebServerNew {
    constructor(client) {
        this.client = client;
        this.app = express();
        this.port = process.env.WEB_PORT || 3000;
        
        // Criar servidor HTTP para Socket.IO
        this.server = http.createServer(this.app);
        
        // Garantir que o cliente global esteja disponível
        global.client = client;

        this.setupBasicMiddleware();
        this.setupSession();
        this.setupPassport();
        this.setupRealTimeIntegration();
        this.setupAPIRoutes();
        this.setupRoutes();
        this.setupErrorHandling();
    }

    setupBasicMiddleware() {
        // CORS
        this.app.use(cors({
            origin: process.env.NODE_ENV === 'production'
                ? [process.env.FRONTEND_URL]
                : ['http://localhost:3000', 'http://127.0.0.1:3000'],
            credentials: true
        }));

        // Body parsing
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // View engine
        this.app.set('view engine', 'ejs');
        this.app.set('views', path.join(__dirname, 'views'));

        // Static files
        this.app.use(express.static(path.join(__dirname, 'public')));

        // Request logging
        this.app.use((req, res, next) => {
            console.log(`📍 [${new Date().toISOString()}] ${req.method} ${req.url}`);
            next();
        });
    }

    setupSession() {
        const sessionSecret = process.env.SESSION_SECRET || 'fallback-secret-key';
        
        this.app.use(session({
            secret: sessionSecret,
            resave: false,
            saveUninitialized: false,
            name: 'nodex.sid',
            cookie: {
                maxAge: 24 * 60 * 60 * 1000, // 24 horas
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax'
            },
            rolling: true
        }));
    }

    setupPassport() {
        this.app.use(passport.initialize());
        this.app.use(passport.session());

        // Configurar Discord Strategy apenas se as credenciais estiverem disponíveis
        if (process.env.CLIENT_ID && process.env.DISCORD_CLIENT_SECRET && process.env.DISCORD_REDIRECT_URI) {
            try {
                const DiscordStrategy = require('passport-discord').Strategy;
                
                passport.use(new DiscordStrategy({
                    clientID: process.env.CLIENT_ID,
                    clientSecret: process.env.DISCORD_CLIENT_SECRET,
                    callbackURL: process.env.DISCORD_REDIRECT_URI,
                    scope: ['identify', 'guilds']
                }, (accessToken, refreshToken, profile, done) => {
                    console.log(`🔐 [OAUTH] Usuário autenticado: ${profile.username}#${profile.discriminator}`);
                    return done(null, profile);
                }));

                passport.serializeUser((user, done) => {
                    done(null, user);
                });

                passport.deserializeUser((obj, done) => {
                    done(null, obj);
                });

                console.log('✅ [PASSPORT] Discord OAuth configurado');
            } catch (error) {
                console.warn('⚠️ [PASSPORT] Erro ao configurar Discord OAuth:', error.message);
            }
        } else {
            console.warn('⚠️ [PASSPORT] Credenciais OAuth2 não encontradas');
        }

        // Middleware para disponibilizar usuário nas views
        this.app.use((req, res, next) => {
            res.locals.user = req.user || null;
            next();
        });
    }

    setupRealTimeIntegration() {
        // Inicializar sistema de integração em tempo real
        this.realTimeIntegration = new RealTimeIntegration(this.client, this.server);
        console.log('✅ [REALTIME] Sistema de integração em tempo real configurado');
    }

    setupAPIRoutes() {
        // Configurar rotas de API
        this.apiRoutes = new APIRoutes(this.client, this.realTimeIntegration);
        this.app.use('/api', this.apiRoutes.setupRoutes());
        console.log('✅ [API] Rotas de API configuradas');
    }

    setupRoutes() {
        // Página inicial
        this.app.get('/', (req, res) => {
            console.log('✅ [ROUTE] Página inicial acessada');
            try {
                res.render('index', {
                    title: 'Nodex | Moderação',
                    botName: this.client?.user?.username || 'Nodex',
                    serverCount: this.client?.guilds?.cache?.size || 0,
                    userCount: this.client?.guilds?.cache?.reduce((a, g) => a + g.memberCount, 0) || 0
                });
            } catch (error) {
                console.error('❌ [ROUTE] Erro na página inicial:', error);
                res.status(500).send('Erro interno do servidor');
            }
        });

        // Login
        this.app.get('/login', (req, res) => {
            console.log('✅ [ROUTE] Página de login acessada');
            if (req.isAuthenticated && req.isAuthenticated()) {
                return res.redirect('/dashboard');
            }
            try {
                res.render('login', { 
                    title: 'Login - Nodex | Moderação',
                    botName: this.client?.user?.username || 'Nodex',
                    error: req.query.error || null
                });
            } catch (error) {
                console.error('❌ [ROUTE] Erro na página de login:', error);
                res.status(500).send('Erro interno do servidor');
            }
        });

        // Dashboard - VERSÃO SIMPLIFICADA
        this.app.get('/dashboard', (req, res) => {
            console.log('✅ [ROUTE] Dashboard acessado');
            
            try {
                // Verificar autenticação básica
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    console.log('⚠️ [DASHBOARD] Usuário não autenticado');
                    return res.redirect('/login');
                }

                console.log(`✅ [DASHBOARD] Usuário autenticado: ${req.user.username || 'Desconhecido'}`);

                // Dados básicos para o dashboard
                const dashboardData = {
                    title: 'Dashboard - Nodex | Moderação',
                    botName: this.client?.user?.username || 'Nodex',
                    user: {
                        id: req.user.id || 'unknown',
                        username: req.user.username || 'Usuário',
                        avatar: req.user.avatar || null
                    },
                    guilds: this.getSimpleGuilds(req.user),
                    client: {
                        user: {
                            id: this.client?.user?.id || 'unknown',
                            username: this.client?.user?.username || 'Nodex'
                        }
                    }
                };

                console.log(`✅ [DASHBOARD] Renderizando com ${dashboardData.guilds.length} guilds`);
                res.render('dashboard', dashboardData);

            } catch (error) {
                console.error('❌ [DASHBOARD] Erro crítico:', error);
                res.status(500).render('error', {
                    title: 'Erro no Dashboard',
                    error: {
                        status: 500,
                        message: 'Erro interno no dashboard',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    }
                });
            }
        });

        // Rotas de autenticação OAuth
        this.app.get('/auth/discord', (req, res, next) => {
            if (passport.authenticate) {
                passport.authenticate('discord')(req, res, next);
            } else {
                res.redirect('/login?error=oauth_not_configured');
            }
        });

        this.app.get('/auth/discord/callback', (req, res, next) => {
            if (passport.authenticate) {
                passport.authenticate('discord', {
                    failureRedirect: '/login?error=auth_failed'
                })(req, res, next);
            } else {
                res.redirect('/login?error=oauth_not_configured');
            }
        }, (req, res) => {
            res.redirect('/dashboard');
        });

        // Logout
        this.app.get('/logout', (req, res) => {
            console.log('✅ [ROUTE] Logout realizado');
            if (req.logout) {
                req.logout((err) => {
                    if (err) console.error('Erro no logout:', err);
                    res.redirect('/');
                });
            } else {
                res.redirect('/');
            }
        });

        // Outras páginas
        this.app.get('/docs', (req, res) => {
            res.render('docs', {
                title: 'Documentação - Nodex | Moderação',
                botName: this.client?.user?.username || 'Nodex'
            });
        });

        this.app.get('/support', (req, res) => {
            res.render('support', {
                title: 'Suporte - Nodex | Moderação',
                botName: this.client?.user?.username || 'Nodex'
            });
        });

        // Módulo de Backup
        this.app.get('/dashboard/guild/:guildId/backup', (req, res) => {
            console.log(`✅ [ROUTE] Módulo de backup acessado: ${req.params.guildId}`);

            try {
                // Verificar autenticação
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    console.log('⚠️ [BACKUP] Usuário não autenticado');
                    return res.redirect('/login');
                }

                const guildId = req.params.guildId;
                console.log(`✅ [BACKUP] Usuário ${req.user.username} acessando backup da guild ${guildId}`);

                // Verificar se o usuário tem acesso a esta guild
                const userGuilds = this.getSimpleGuilds(req.user);
                const targetGuild = userGuilds.find(g => g.id === guildId);

                if (!targetGuild) {
                    console.log(`❌ [BACKUP] Usuário não tem acesso à guild ${guildId}`);
                    return res.status(403).render('error', {
                        title: 'Acesso Negado',
                        error: {
                            status: 403,
                            message: 'Você não tem permissão para acessar este servidor.',
                            details: 'Verifique se você é administrador do servidor.'
                        }
                    });
                }

                if (!targetGuild.botInGuild) {
                    console.log(`❌ [BACKUP] Bot não está na guild ${guildId}`);
                    return res.render('guild-invite', {
                        title: 'Bot Não Encontrado',
                        guild: targetGuild,
                        botName: this.client?.user?.username || 'Nodex',
                        inviteUrl: `https://discord.com/oauth2/authorize?client_id=${this.client?.user?.id}&scope=bot&permissions=8&guild_id=${guildId}`
                    });
                }

                // Dados para o módulo de backup
                const backupData = {
                    title: `Backup - ${targetGuild.name} - Nodex | Moderação`,
                    botName: this.client?.user?.username || 'Nodex',
                    user: {
                        id: req.user.id || 'unknown',
                        username: req.user.username || 'Usuário',
                        avatar: req.user.avatar || null
                    },
                    guild: targetGuild,
                    client: {
                        user: {
                            id: this.client?.user?.id || 'unknown',
                            username: this.client?.user?.username || 'Nodex'
                        }
                    }
                };

                console.log(`✅ [BACKUP] Renderizando módulo de backup para ${targetGuild.name}`);
                res.render('backup-module', backupData);

            } catch (error) {
                console.error('❌ [BACKUP] Erro crítico:', error);
                res.status(500).render('error', {
                    title: 'Erro no Módulo de Backup',
                    error: {
                        status: 500,
                        message: 'Erro interno ao carregar módulo de backup.',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    }
                });
            }
        });

        // Configuração de servidor específico
        this.app.get('/dashboard/guild/:guildId', async (req, res) => {
            console.log(`✅ [ROUTE] Configuração de guild acessada: ${req.params.guildId}`);
            
            try {
                // Verificar autenticação
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    console.log('⚠️ [GUILD-CONFIG] Usuário não autenticado');
                    return res.redirect('/login');
                }

                const guildId = req.params.guildId;
                console.log(`✅ [GUILD-CONFIG] Usuário ${req.user.username} acessando guild ${guildId}`);

                // Verificar se o usuário tem acesso a esta guild
                const userGuilds = this.getSimpleGuilds(req.user);
                const targetGuild = userGuilds.find(g => g.id === guildId);

                if (!targetGuild) {
                    console.log(`❌ [GUILD-CONFIG] Usuário não tem acesso à guild ${guildId}`);
                    return res.status(403).render('error', {
                        title: 'Acesso Negado',
                        error: {
                            status: 403,
                            message: 'Você não tem permissão para configurar este servidor.',
                            details: 'Verifique se você é administrador do servidor.'
                        }
                    });
                }

                if (!targetGuild.botInGuild) {
                    console.log(`❌ [GUILD-CONFIG] Bot não está na guild ${guildId}`);
                    return res.render('guild-invite', {
                        title: 'Bot Não Encontrado',
                        guild: targetGuild,
                        botName: this.client?.user?.username || 'Nodex',
                        inviteUrl: `https://discord.com/oauth2/authorize?client_id=${this.client?.user?.id}&scope=bot&permissions=8&guild_id=${guildId}`
                    });
                }

                // Obter dados do servidor Discord
                let discordGuild = null;
                let channels = [];
                let roles = [];
                let config = {};

                try {
                    // Tentar obter dados do servidor Discord
                    if (this.client && this.client.guilds && this.client.guilds.cache) {
                        discordGuild = this.client.guilds.cache.get(guildId);
                        
                        if (discordGuild) {
                            // Obter canais de texto
                            channels = discordGuild.channels.cache
                                .filter(channel => channel.type === 0) // GUILD_TEXT
                                .map(channel => ({
                                    id: channel.id,
                                    name: channel.name
                                }))
                                .slice(0, 50); // Limitar a 50 canais

                            // Obter cargos
                            roles = discordGuild.roles.cache
                                .filter(role => !role.managed && role.name !== '@everyone')
                                .map(role => ({
                                    id: role.id,
                                    name: role.name
                                }))
                                .slice(0, 50); // Limitar a 50 cargos

                            console.log(`✅ [GUILD-CONFIG] Carregados ${channels.length} canais e ${roles.length} cargos`);
                        }
                    }
                } catch (error) {
                    console.warn(`⚠️ [GUILD-CONFIG] Erro ao carregar dados do Discord:`, error.message);
                }

                // Carregar configurações do banco de dados
                try {
                    // Carregar configuração geral
                    let guildConfig = await this.client.database.getGuildConfig(guildId);

                    // Carregar configuração de verificação específica
                    const verificationConfig = this.client.database.db.prepare(`
                        SELECT * FROM verification_config WHERE guild_id = ?
                    `).get(guildId);

                    console.log(`🔍 [GUILD-CONFIG] Configuração de verificação encontrada:`, verificationConfig ? 'SIM' : 'NÃO');

                    // Configurações padrão
                    config = {
                        prefix: guildConfig?.prefix || '!',
                        language: guildConfig?.language || 'pt-BR',
                        timezone: guildConfig?.timezone || 'America/Sao_Paulo',
                        auto_mod_enabled: guildConfig?.auto_mod_enabled || true,
                        log_channel_id: guildConfig?.log_channel_id || '',
                        mute_role_id: guildConfig?.mute_role_id || '',

                        // Configurações de verificação do banco
                        verification_enabled: verificationConfig?.enabled || false,
                        verification_channel_id: verificationConfig?.channel_id || '',
                        verified_role_id: verificationConfig?.verified_role_id || '',
                        unverified_role_id: verificationConfig?.unverified_role_id || '',
                        verification_method: verificationConfig?.method || 'captcha_emoji',
                        verification_timeout_minutes: verificationConfig?.timeout_minutes || 30,
                        verification_auto_kick: verificationConfig?.auto_kick || false,
                        verification_kick_time: verificationConfig?.kick_time || 60,
                        verification_dm_enabled: verificationConfig?.dm_enabled !== false, // padrão true
                        verification_log_enabled: verificationConfig?.log_enabled !== false, // padrão true
                        verification_anti_bot: verificationConfig?.anti_bot_enabled !== false, // padrão true
                        verification_ip_check: verificationConfig?.ip_check_enabled || false,
                        verification_max_attempts: verificationConfig?.max_attempts || 3,
                        verification_rules_text: verificationConfig?.rules_text || 'Leia as regras do servidor antes de se verificar.',
                        welcome_message: verificationConfig?.welcome_message || 'Bem-vindo(a) ao servidor! Você foi verificado(a) com sucesso.',
                        verification_captcha_type: verificationConfig?.captcha_type || 'emoji'
                    };

                    console.log(`✅ [GUILD-CONFIG] Configurações carregadas:`, {
                        verification_enabled: config.verification_enabled,
                        verification_auto_kick: config.verification_auto_kick,
                        verification_timeout_minutes: config.verification_timeout_minutes,
                        verification_method: config.verification_method
                    });

                } catch (dbError) {
                    console.error(`❌ [GUILD-CONFIG] Erro ao carregar configurações do banco:`, dbError);

                    // Fallback para configurações padrão
                    config = {
                        prefix: '!',
                        language: 'pt-BR',
                        timezone: 'America/Sao_Paulo',
                        auto_mod_enabled: true,
                        log_channel_id: '',
                        mute_role_id: '',
                        verification_enabled: false,
                        verification_auto_kick: false,
                        verification_timeout_minutes: 30
                    };
                }

                // Dados para a configuração da guild
                const guildConfigData = {
                    title: `Configurar ${targetGuild.name} - Nodex | Moderação`,
                    botName: this.client?.user?.username || 'Nodex',
                    user: {
                        id: req.user.id || 'unknown',
                        username: req.user.username || 'Usuário',
                        avatar: req.user.avatar || null
                    },
                    guild: targetGuild,
                    client: {
                        user: {
                            id: this.client?.user?.id || 'unknown',
                            username: this.client?.user?.username || 'Nodex'
                        }
                    },
                    config: config,
                    channels: channels,
                    roles: roles
                };

                console.log(`✅ [GUILD-CONFIG] Renderizando configuração para ${targetGuild.name}`);
                res.render('guild-config-new', guildConfigData);

            } catch (error) {
                console.error('❌ [GUILD-CONFIG] Erro crítico:', error);
                res.status(500).render('error', {
                    title: 'Erro na Configuração',
                    error: {
                        status: 500,
                        message: 'Erro interno ao carregar configuração do servidor.',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    }
                });
            }
        });

        // APIs para salvar configurações
        this.app.post('/api/guild/:guildId/config/:section', async (req, res) => {
            console.log(`📡 [API] Salvando seção ${req.params.section} para guild ${req.params.guildId}`);

            try {
                // Verificar autenticação
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    return res.status(401).json({ success: false, error: 'Não autenticado' });
                }

                const { guildId, section } = req.params;
                const configData = req.body;

                console.log(`📡 [API] Dados recebidos para ${section}:`, configData);

                // Salvar configurações baseado na seção
                if (section === 'verificacao') {
                    await this.saveVerificationConfig(guildId, configData);
                } else {
                    // Salvar outras configurações no sistema geral
                    await this.saveGeneralConfig(guildId, section, configData);
                }

                res.json({
                    success: true,
                    message: `Configurações de ${section} salvas com sucesso`,
                    section: section,
                    guildId: guildId,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                console.error(`❌ [API] Erro ao salvar seção ${req.params.section}:`, error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor',
                    details: error.message
                });
            }
        });

        // API para carregar configurações
        this.app.get('/api/guild/:guildId/config', async (req, res) => {
            console.log(`📡 [API] Carregando configurações para guild ${req.params.guildId}`);

            try {
                // Verificar autenticação
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    return res.status(401).json({ success: false, error: 'Não autenticado' });
                }

                const { guildId } = req.params;

                // Buscar configuração do banco
                let guildConfig = await this.client.database.getGuildConfig(guildId);

                // Se não existir, criar configuração padrão
                if (!guildConfig) {
                    const defaultConfig = {
                        prefix: '!',
                        language: 'pt-BR',
                        timezone: 'America/Sao_Paulo',
                        auto_mod_enabled: true,
                        anti_raid_enabled: true,
                        settings: {
                            ai_moderation_enabled: false,
                            verification_enabled: false
                        }
                    };

                    await this.client.database.saveGuildConfig(guildId, defaultConfig);
                    guildConfig = defaultConfig;
                }

                // Parse settings se necessário
                let settings = {};
                if (guildConfig.settings) {
                    try {
                        if (typeof guildConfig.settings === 'string') {
                            settings = JSON.parse(guildConfig.settings);
                        } else {
                            settings = guildConfig.settings;
                        }
                    } catch (parseError) {
                        console.error('❌ [API] Erro ao fazer parse das configurações:', parseError);
                        settings = {};
                    }
                }

                // Retornar configuração completa
                const response = {
                    ...guildConfig,
                    settings: settings,
                    // Compatibilidade com o frontend
                    ai_moderation_enabled: settings.ai_moderation_enabled || false,
                    verification_enabled: settings.verification_enabled || false
                };

                res.json(response);

            } catch (error) {
                console.error(`❌ [API] Erro ao carregar configurações:`, error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        // API para comandos
        this.app.get('/api/guild/:guildId/commands', (req, res) => {
            console.log(`📡 [API] Carregando comandos para guild ${req.params.guildId}`);
            
            try {
                // Comandos padrão (todos habilitados)
                const defaultCommands = {
                    ban: { enabled: true },
                    kick: { enabled: true },
                    warn: { enabled: true },
                    warnings: { enabled: true },
                    timeout: { enabled: true },
                    mute: { enabled: true },
                    unmute: { enabled: true },
                    unban: { enabled: true },
                    clear: { enabled: true },
                    'mod-logs': { enabled: true },
                    comandos: { enabled: true },
                    backup: { enabled: true },
                    'deploy-local': { enabled: true },
                    'setup-panels': { enabled: true },
                    stats: { enabled: true },
                    dashboard: { enabled: true },
                    config: { enabled: true },
                    botinfo: { enabled: true },
                    help: { enabled: true },
                    recursos: { enabled: true },
                    'stats-moderacao': { enabled: true },
                    avatar: { enabled: true },
                    ping: { enabled: true },
                    say: { enabled: true },
                    serverinfo: { enabled: true },
                    userinfo: { enabled: true }
                };

                res.json({
                    success: true,
                    commands: defaultCommands,
                    totalCommands: Object.keys(defaultCommands).length
                });

            } catch (error) {
                console.error(`❌ [API] Erro ao carregar comandos:`, error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        this.app.post('/api/guild/:guildId/commands', (req, res) => {
            console.log(`📡 [API] Salvando comandos para guild ${req.params.guildId}`);
            
            try {
                const { commands } = req.body;
                console.log(`📡 [API] Comandos recebidos:`, commands);

                // Simular salvamento
                setTimeout(() => {
                    res.json({
                        success: true,
                        message: 'Estados dos comandos salvos com sucesso',
                        updatedCommands: Object.keys(commands).length,
                        timestamp: new Date().toISOString()
                    });
                }, 500);

            } catch (error) {
                console.error(`❌ [API] Erro ao salvar comandos:`, error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        // API de teste
        this.app.get('/api/test', (req, res) => {
            res.json({
                status: 'OK',
                timestamp: new Date().toISOString(),
                server: 'Funcionando',
                client: this.client?.user?.username || 'Desconectado'
            });
        });
    }

    // Função simplificada para obter guilds
    getSimpleGuilds(user) {
        try {
            if (!user || !user.guilds || !Array.isArray(user.guilds)) {
                console.log('⚠️ [GUILDS] Usuário sem guilds válidas');
                return [];
            }

            const guilds = [];
            for (const guild of user.guilds) {
                if (guild && guild.id && guild.name) {
                    // Verificar se tem permissão de admin (bit 3 = 0x8)
                    const permissions = parseInt(guild.permissions || '0');
                    const hasAdmin = (permissions & 0x8) === 0x8;

                    if (hasAdmin) {
                        guilds.push({
                            id: guild.id,
                            name: guild.name,
                            icon: guild.icon || null,
                            iconURL: guild.icon 
                                ? `https://cdn.discordapp.com/icons/${guild.id}/${guild.icon}.png`
                                : '/img/logo.png',
                            botInGuild: this.client?.guilds?.cache?.has(guild.id) || false
                        });
                    }
                }
            }

            console.log(`✅ [GUILDS] ${guilds.length} guilds com permissão de admin encontradas`);
            return guilds;

        } catch (error) {
            console.error('❌ [GUILDS] Erro ao processar guilds:', error);
            return [];
        }
    }

    setupErrorHandling() {
        // 404 Handler
        this.app.use((req, res) => {
            console.log(`❌ [404] Página não encontrada: ${req.url}`);
            res.status(404).render('error', {
                title: 'Página Não Encontrada',
                error: {
                    status: 404,
                    message: 'A página que você procura não foi encontrada.',
                    details: req.url
                }
            });
        });

        // Error Handler
        this.app.use((err, req, res, next) => {
            console.error('❌ [ERROR] Erro no servidor:', err);
            res.status(500).render('error', {
                title: 'Erro Interno',
                error: {
                    status: 500,
                    message: 'Erro interno do servidor.',
                    details: process.env.NODE_ENV === 'development' ? err.message : null
                }
            });
        });
    }

    /**
     * Salvar configurações de verificação
     */
    async saveVerificationConfig(guildId, configData) {
        try {
            console.log(`🛡️ [VERIFICATION CONFIG] Salvando para guild ${guildId}:`, configData);
            console.log(`🛡️ [VERIFICATION CONFIG] Método recebido: "${configData.verification_method}"`);
            console.log(`🛡️ [VERIFICATION CONFIG] Dados completos:`, JSON.stringify(configData, null, 2));

            // Buscar guild
            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) {
                throw new Error('Servidor não encontrado');
            }

            // Preparar dados para o banco
            const dbData = {
                guild_id: guildId,
                enabled: configData.verification_enabled ? 1 : 0,
                channel_id: configData.verification_channel_id || null,
                verified_role_id: configData.verified_role_id || null,
                unverified_role_id: configData.unverified_role_id || null,
                method: configData.verification_method || 'reaction', // CORRIGIDO: usar 'reaction' como padrão
                timeout_minutes: parseInt(configData.verification_timeout_minutes) || 30,
                auto_kick: configData.verification_auto_kick ? 1 : 0,
                kick_time: parseInt(configData.verification_kick_time) || 60,
                dm_enabled: configData.verification_dm_enabled !== false ? 1 : 0,
                log_enabled: configData.verification_log_enabled !== false ? 1 : 0,
                anti_bot_enabled: configData.verification_anti_bot !== false ? 1 : 0,
                ip_check_enabled: configData.verification_ip_check ? 1 : 0,
                max_attempts: parseInt(configData.verification_max_attempts) || 3,
                rules_text: configData.verification_rules_text || 'Leia as regras do servidor antes de se verificar.',
                welcome_message: configData.welcome_message || 'Bem-vindo(a) ao servidor! Você foi verificado(a) com sucesso.',
                captcha_type: configData.verification_captcha_type || 'emoji'
            };

            console.log(`🛡️ [VERIFICATION CONFIG] Dados preparados para o banco:`, dbData);

            // Salvar no banco de dados
            const stmt = this.client.database.db.prepare(`
                INSERT OR REPLACE INTO verification_config
                (guild_id, enabled, channel_id, verified_role_id, unverified_role_id, method,
                 timeout_minutes, auto_kick, kick_time, dm_enabled, log_enabled,
                 anti_bot_enabled, ip_check_enabled, max_attempts, rules_text,
                 welcome_message, captcha_type, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `);

            stmt.run(
                dbData.guild_id, dbData.enabled, dbData.channel_id, dbData.verified_role_id,
                dbData.unverified_role_id, dbData.method, dbData.timeout_minutes, dbData.auto_kick,
                dbData.kick_time, dbData.dm_enabled, dbData.log_enabled, dbData.anti_bot_enabled,
                dbData.ip_check_enabled, dbData.max_attempts, dbData.rules_text,
                dbData.welcome_message, dbData.captcha_type
            );

            console.log(`✅ [VERIFICATION CONFIG] Configurações salvas no banco de dados`);

            // Emitir evento para o sistema de verificação
            if (this.client.verificationSystem) {
                this.client.emit('verificationUpdated', { guildId, config: configData });
            }

            console.log(`✅ [VERIFICATION CONFIG] Configurações salvas e sistema atualizado`);

        } catch (error) {
            console.error(`❌ [VERIFICATION CONFIG] Erro ao salvar:`, error);
            throw error;
        }
    }

    /**
     * Salvar configurações gerais
     */
    async saveGeneralConfig(guildId, section, configData) {
        try {
            console.log(`⚙️ [GENERAL CONFIG] Salvando ${section} para guild ${guildId}:`, configData);

            // Buscar configuração atual
            let guildConfig = await this.client.database.getGuildConfig(guildId);
            if (!guildConfig) {
                guildConfig = {
                    prefix: '!',
                    language: 'pt-BR',
                    timezone: 'America/Sao_Paulo',
                    settings: {}
                };
            }

            // Parse settings se necessário
            let settings = {};
            if (guildConfig.settings) {
                try {
                    if (typeof guildConfig.settings === 'string') {
                        settings = JSON.parse(guildConfig.settings);
                    } else {
                        settings = guildConfig.settings;
                    }
                } catch (parseError) {
                    settings = {};
                }
            }

            // Atualizar configurações baseado na seção
            Object.assign(settings, configData);
            guildConfig.settings = settings;

            // Salvar no banco
            await this.client.database.saveGuildConfig(guildId, guildConfig);

            // Emitir evento de atualização
            this.client.emit('configUpdated', { guildId, config: configData, section });

            console.log(`✅ [GENERAL CONFIG] ${section} salvo com sucesso`);

        } catch (error) {
            console.error(`❌ [GENERAL CONFIG] Erro ao salvar ${section}:`, error);
            throw error;
        }
    }

    start() {
        this.server.listen(this.port, (error) => {
            if (error) {
                if (error.code === 'EADDRINUSE') {
                    console.log(`⚠️ [WEB] Porta ${this.port} já está em uso. Tentando porta ${this.port + 1}...`);
                    this.port = this.port + 1;
                    this.start();
                    return;
                } else {
                    console.error(`❌ [WEB] Erro ao iniciar servidor:`, error);
                    return;
                }
            }

            console.log(`✅ [WEB] Servidor web NOVO rodando em http://localhost:${this.port}`);
            console.log(`📊 [WEB] Cliente Discord: ${this.client?.user?.username || 'Desconectado'}`);
            console.log(`🔄 [WEB] Sistema de tempo real ativo`);
            console.log(`📡 [WEB] APIs completas disponíveis em /api`);
        });
    }
}

module.exports = WebServerNew;