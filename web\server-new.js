/**
 * ========================================
 * SERVIDOR WEB NOVO - DASHBOARD FUNCIONAL
 * Versão completamente reescrita do zero
 * ========================================
 */

const express = require('express');
const path = require('path');
const session = require('express-session');
const passport = require('passport');
const cors = require('cors');
const http = require('http');
const RealTimeIntegration = require('./realtime-integration');
const APIRoutes = require('./api-routes');

class WebServerNew {
    constructor(client) {
        this.client = client;
        this.app = express();
        this.port = process.env.WEB_PORT || 3000;
        
        // Criar servidor HTTP para Socket.IO
        this.server = http.createServer(this.app);
        
        // Garantir que o cliente global esteja disponível
        global.client = client;

        this.setupBasicMiddleware();
        this.setupSession();
        this.setupPassport();
        this.setupRealTimeIntegration();
        this.setupAPIRoutes();
        this.setupRoutes();
        this.setupErrorHandling();
    }

    setupBasicMiddleware() {
        // CORS
        this.app.use(cors({
            origin: process.env.NODE_ENV === 'production'
                ? [process.env.FRONTEND_URL]
                : ['http://localhost:3000', 'http://127.0.0.1:3000'],
            credentials: true
        }));

        // Body parsing
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // View engine
        this.app.set('view engine', 'ejs');
        this.app.set('views', path.join(__dirname, 'views'));

        // Static files
        this.app.use(express.static(path.join(__dirname, 'public')));

        // Request logging
        this.app.use((req, res, next) => {
            console.log(`📍 [${new Date().toISOString()}] ${req.method} ${req.url}`);
            next();
        });
    }

    setupSession() {
        const sessionSecret = process.env.SESSION_SECRET || 'fallback-secret-key';
        
        this.app.use(session({
            secret: sessionSecret,
            resave: false,
            saveUninitialized: false,
            name: 'nodex.sid',
            cookie: {
                maxAge: 24 * 60 * 60 * 1000, // 24 horas
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax'
            },
            rolling: true
        }));
    }

    setupPassport() {
        this.app.use(passport.initialize());
        this.app.use(passport.session());

        // Configurar Discord Strategy apenas se as credenciais estiverem disponíveis
        if (process.env.CLIENT_ID && process.env.DISCORD_CLIENT_SECRET && process.env.DISCORD_REDIRECT_URI) {
            try {
                const DiscordStrategy = require('passport-discord').Strategy;
                
                passport.use(new DiscordStrategy({
                    clientID: process.env.CLIENT_ID,
                    clientSecret: process.env.DISCORD_CLIENT_SECRET,
                    callbackURL: process.env.DISCORD_REDIRECT_URI,
                    scope: ['identify', 'guilds']
                }, (accessToken, refreshToken, profile, done) => {
                    console.log(`🔐 [OAUTH] Usuário autenticado: ${profile.username}#${profile.discriminator}`);
                    return done(null, profile);
                }));

                passport.serializeUser((user, done) => {
                    done(null, user);
                });

                passport.deserializeUser((obj, done) => {
                    done(null, obj);
                });

                console.log('✅ [PASSPORT] Discord OAuth configurado');
            } catch (error) {
                console.warn('⚠️ [PASSPORT] Erro ao configurar Discord OAuth:', error.message);
            }
        } else {
            console.warn('⚠️ [PASSPORT] Credenciais OAuth2 não encontradas');
        }

        // Middleware para disponibilizar usuário nas views
        this.app.use((req, res, next) => {
            res.locals.user = req.user || null;
            next();
        });
    }

    setupRealTimeIntegration() {
        // Inicializar sistema de integração em tempo real
        this.realTimeIntegration = new RealTimeIntegration(this.client, this.server);
        console.log('✅ [REALTIME] Sistema de integração em tempo real configurado');
    }

    setupAPIRoutes() {
        // Configurar rotas de API
        this.apiRoutes = new APIRoutes(this.client, this.realTimeIntegration);
        this.app.use('/api', this.apiRoutes.setupRoutes());
        console.log('✅ [API] Rotas de API configuradas');
    }

    setupRoutes() {
        // Página inicial
        this.app.get('/', (req, res) => {
            console.log('✅ [ROUTE] Página inicial acessada');
            try {
                res.render('index', {
                    title: 'Nodex | Moderação',
                    botName: this.client?.user?.username || 'Nodex',
                    serverCount: this.client?.guilds?.cache?.size || 0,
                    userCount: this.client?.guilds?.cache?.reduce((a, g) => a + g.memberCount, 0) || 0
                });
            } catch (error) {
                console.error('❌ [ROUTE] Erro na página inicial:', error);
                res.status(500).send('Erro interno do servidor');
            }
        });

        // Login
        this.app.get('/login', (req, res) => {
            console.log('✅ [ROUTE] Página de login acessada');
            if (req.isAuthenticated && req.isAuthenticated()) {
                return res.redirect('/dashboard');
            }
            try {
                res.render('login', { 
                    title: 'Login - Nodex | Moderação',
                    botName: this.client?.user?.username || 'Nodex',
                    error: req.query.error || null
                });
            } catch (error) {
                console.error('❌ [ROUTE] Erro na página de login:', error);
                res.status(500).send('Erro interno do servidor');
            }
        });

        // Dashboard - VERSÃO SIMPLIFICADA
        this.app.get('/dashboard', (req, res) => {
            console.log('✅ [ROUTE] Dashboard acessado');
            
            try {
                // Verificar autenticação básica
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    console.log('⚠️ [DASHBOARD] Usuário não autenticado');
                    return res.redirect('/login');
                }

                console.log(`✅ [DASHBOARD] Usuário autenticado: ${req.user.username || 'Desconhecido'}`);

                // Dados básicos para o dashboard
                const dashboardData = {
                    title: 'Dashboard - Nodex | Moderação',
                    botName: this.client?.user?.username || 'Nodex',
                    user: {
                        id: req.user.id || 'unknown',
                        username: req.user.username || 'Usuário',
                        avatar: req.user.avatar || null
                    },
                    guilds: this.getSimpleGuilds(req.user),
                    client: {
                        user: {
                            id: this.client?.user?.id || 'unknown',
                            username: this.client?.user?.username || 'Nodex'
                        }
                    }
                };

                console.log(`✅ [DASHBOARD] Renderizando com ${dashboardData.guilds.length} guilds`);
                res.render('dashboard', dashboardData);

            } catch (error) {
                console.error('❌ [DASHBOARD] Erro crítico:', error);
                res.status(500).render('error', {
                    title: 'Erro no Dashboard',
                    error: {
                        status: 500,
                        message: 'Erro interno no dashboard',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    }
                });
            }
        });

        // Rotas de autenticação OAuth
        this.app.get('/auth/discord', (req, res, next) => {
            if (passport.authenticate) {
                passport.authenticate('discord')(req, res, next);
            } else {
                res.redirect('/login?error=oauth_not_configured');
            }
        });

        this.app.get('/auth/discord/callback', (req, res, next) => {
            if (passport.authenticate) {
                passport.authenticate('discord', {
                    failureRedirect: '/login?error=auth_failed'
                })(req, res, next);
            } else {
                res.redirect('/login?error=oauth_not_configured');
            }
        }, (req, res) => {
            res.redirect('/dashboard');
        });

        // Logout
        this.app.get('/logout', (req, res) => {
            console.log('✅ [ROUTE] Logout realizado');
            if (req.logout) {
                req.logout((err) => {
                    if (err) console.error('Erro no logout:', err);
                    res.redirect('/');
                });
            } else {
                res.redirect('/');
            }
        });

        // Outras páginas
        this.app.get('/docs', (req, res) => {
            res.render('docs', {
                title: 'Documentação - Nodex | Moderação',
                botName: this.client?.user?.username || 'Nodex'
            });
        });

        this.app.get('/support', (req, res) => {
            res.render('support', {
                title: 'Suporte - Nodex | Moderação',
                botName: this.client?.user?.username || 'Nodex'
            });
        });

        // Configuração de servidor específico
        this.app.get('/dashboard/guild/:guildId', (req, res) => {
            console.log(`✅ [ROUTE] Configuração de guild acessada: ${req.params.guildId}`);
            
            try {
                // Verificar autenticação
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    console.log('⚠️ [GUILD-CONFIG] Usuário não autenticado');
                    return res.redirect('/login');
                }

                const guildId = req.params.guildId;
                console.log(`✅ [GUILD-CONFIG] Usuário ${req.user.username} acessando guild ${guildId}`);

                // Verificar se o usuário tem acesso a esta guild
                const userGuilds = this.getSimpleGuilds(req.user);
                const targetGuild = userGuilds.find(g => g.id === guildId);

                if (!targetGuild) {
                    console.log(`❌ [GUILD-CONFIG] Usuário não tem acesso à guild ${guildId}`);
                    return res.status(403).render('error', {
                        title: 'Acesso Negado',
                        error: {
                            status: 403,
                            message: 'Você não tem permissão para configurar este servidor.',
                            details: 'Verifique se você é administrador do servidor.'
                        }
                    });
                }

                if (!targetGuild.botInGuild) {
                    console.log(`❌ [GUILD-CONFIG] Bot não está na guild ${guildId}`);
                    return res.render('guild-invite', {
                        title: 'Bot Não Encontrado',
                        guild: targetGuild,
                        botName: this.client?.user?.username || 'Nodex',
                        inviteUrl: `https://discord.com/oauth2/authorize?client_id=${this.client?.user?.id}&scope=bot&permissions=8&guild_id=${guildId}`
                    });
                }

                // Obter dados do servidor Discord
                let discordGuild = null;
                let channels = [];
                let roles = [];
                let config = {};

                try {
                    // Tentar obter dados do servidor Discord
                    if (this.client && this.client.guilds && this.client.guilds.cache) {
                        discordGuild = this.client.guilds.cache.get(guildId);
                        
                        if (discordGuild) {
                            // Obter canais de texto
                            channels = discordGuild.channels.cache
                                .filter(channel => channel.type === 0) // GUILD_TEXT
                                .map(channel => ({
                                    id: channel.id,
                                    name: channel.name
                                }))
                                .slice(0, 50); // Limitar a 50 canais

                            // Obter cargos
                            roles = discordGuild.roles.cache
                                .filter(role => !role.managed && role.name !== '@everyone')
                                .map(role => ({
                                    id: role.id,
                                    name: role.name
                                }))
                                .slice(0, 50); // Limitar a 50 cargos

                            console.log(`✅ [GUILD-CONFIG] Carregados ${channels.length} canais e ${roles.length} cargos`);
                        }
                    }
                } catch (error) {
                    console.warn(`⚠️ [GUILD-CONFIG] Erro ao carregar dados do Discord:`, error.message);
                }

                // Configurações padrão (você pode implementar carregamento do banco de dados aqui)
                config = {
                    prefix: '!',
                    language: 'pt-BR',
                    timezone: 'America/Sao_Paulo',
                    auto_mod_enabled: true,
                    log_channel_id: '',
                    mute_role_id: ''
                };

                // Dados para a configuração da guild
                const guildConfigData = {
                    title: `Configurar ${targetGuild.name} - Nodex | Moderação`,
                    botName: this.client?.user?.username || 'Nodex',
                    user: {
                        id: req.user.id || 'unknown',
                        username: req.user.username || 'Usuário',
                        avatar: req.user.avatar || null
                    },
                    guild: targetGuild,
                    client: {
                        user: {
                            id: this.client?.user?.id || 'unknown',
                            username: this.client?.user?.username || 'Nodex'
                        }
                    },
                    config: config,
                    channels: channels,
                    roles: roles
                };

                console.log(`✅ [GUILD-CONFIG] Renderizando configuração para ${targetGuild.name}`);
                res.render('guild-config-new', guildConfigData);

            } catch (error) {
                console.error('❌ [GUILD-CONFIG] Erro crítico:', error);
                res.status(500).render('error', {
                    title: 'Erro na Configuração',
                    error: {
                        status: 500,
                        message: 'Erro interno ao carregar configuração do servidor.',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    }
                });
            }
        });

        // APIs para salvar configurações
        this.app.post('/api/guild/:guildId/config/:section', (req, res) => {
            console.log(`📡 [API] Salvando seção ${req.params.section} para guild ${req.params.guildId}`);
            
            try {
                // Verificar autenticação
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    return res.status(401).json({ success: false, error: 'Não autenticado' });
                }

                const { guildId, section } = req.params;
                const configData = req.body;

                console.log(`📡 [API] Dados recebidos:`, configData);

                // Aqui você pode implementar o salvamento no banco de dados
                // Por enquanto, vamos simular o salvamento
                setTimeout(() => {
                    res.json({
                        success: true,
                        message: `Configurações de ${section} salvas com sucesso`,
                        section: section,
                        guildId: guildId,
                        timestamp: new Date().toISOString()
                    });
                }, 1000); // Simular delay de salvamento

            } catch (error) {
                console.error(`❌ [API] Erro ao salvar seção ${req.params.section}:`, error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        // API para carregar configurações
        this.app.get('/api/guild/:guildId/config', async (req, res) => {
            console.log(`📡 [API] Carregando configurações para guild ${req.params.guildId}`);

            try {
                // Verificar autenticação
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    return res.status(401).json({ success: false, error: 'Não autenticado' });
                }

                const { guildId } = req.params;

                // Buscar configuração do banco
                let guildConfig = await this.client.database.getGuildConfig(guildId);

                // Se não existir, criar configuração padrão
                if (!guildConfig) {
                    const defaultConfig = {
                        prefix: '!',
                        language: 'pt-BR',
                        timezone: 'America/Sao_Paulo',
                        auto_mod_enabled: true,
                        anti_raid_enabled: true,
                        settings: {
                            ai_moderation_enabled: false,
                            verification_enabled: false
                        }
                    };

                    await this.client.database.saveGuildConfig(guildId, defaultConfig);
                    guildConfig = defaultConfig;
                }

                // Parse settings se necessário
                let settings = {};
                if (guildConfig.settings) {
                    try {
                        if (typeof guildConfig.settings === 'string') {
                            settings = JSON.parse(guildConfig.settings);
                        } else {
                            settings = guildConfig.settings;
                        }
                    } catch (parseError) {
                        console.error('❌ [API] Erro ao fazer parse das configurações:', parseError);
                        settings = {};
                    }
                }

                // Retornar configuração completa
                const response = {
                    ...guildConfig,
                    settings: settings,
                    // Compatibilidade com o frontend
                    ai_moderation_enabled: settings.ai_moderation_enabled || false,
                    verification_enabled: settings.verification_enabled || false
                };

                res.json(response);

            } catch (error) {
                console.error(`❌ [API] Erro ao carregar configurações:`, error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        // API para comandos
        this.app.get('/api/guild/:guildId/commands', (req, res) => {
            console.log(`📡 [API] Carregando comandos para guild ${req.params.guildId}`);
            
            try {
                // Comandos padrão (todos habilitados)
                const defaultCommands = {
                    ban: { enabled: true },
                    kick: { enabled: true },
                    warn: { enabled: true },
                    warnings: { enabled: true },
                    timeout: { enabled: true },
                    mute: { enabled: true },
                    unmute: { enabled: true },
                    unban: { enabled: true },
                    clear: { enabled: true },
                    'mod-logs': { enabled: true },
                    comandos: { enabled: true },
                    backup: { enabled: true },
                    'deploy-local': { enabled: true },
                    'setup-panels': { enabled: true },
                    stats: { enabled: true },
                    dashboard: { enabled: true },
                    config: { enabled: true },
                    botinfo: { enabled: true },
                    help: { enabled: true },
                    recursos: { enabled: true },
                    'stats-moderacao': { enabled: true },
                    avatar: { enabled: true },
                    ping: { enabled: true },
                    say: { enabled: true },
                    serverinfo: { enabled: true },
                    userinfo: { enabled: true }
                };

                res.json({
                    success: true,
                    commands: defaultCommands,
                    totalCommands: Object.keys(defaultCommands).length
                });

            } catch (error) {
                console.error(`❌ [API] Erro ao carregar comandos:`, error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        this.app.post('/api/guild/:guildId/commands', (req, res) => {
            console.log(`📡 [API] Salvando comandos para guild ${req.params.guildId}`);
            
            try {
                const { commands } = req.body;
                console.log(`📡 [API] Comandos recebidos:`, commands);

                // Simular salvamento
                setTimeout(() => {
                    res.json({
                        success: true,
                        message: 'Estados dos comandos salvos com sucesso',
                        updatedCommands: Object.keys(commands).length,
                        timestamp: new Date().toISOString()
                    });
                }, 500);

            } catch (error) {
                console.error(`❌ [API] Erro ao salvar comandos:`, error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        // API de teste
        this.app.get('/api/test', (req, res) => {
            res.json({
                status: 'OK',
                timestamp: new Date().toISOString(),
                server: 'Funcionando',
                client: this.client?.user?.username || 'Desconectado'
            });
        });
    }

    // Função simplificada para obter guilds
    getSimpleGuilds(user) {
        try {
            if (!user || !user.guilds || !Array.isArray(user.guilds)) {
                console.log('⚠️ [GUILDS] Usuário sem guilds válidas');
                return [];
            }

            const guilds = [];
            for (const guild of user.guilds) {
                if (guild && guild.id && guild.name) {
                    // Verificar se tem permissão de admin (bit 3 = 0x8)
                    const permissions = parseInt(guild.permissions || '0');
                    const hasAdmin = (permissions & 0x8) === 0x8;

                    if (hasAdmin) {
                        guilds.push({
                            id: guild.id,
                            name: guild.name,
                            icon: guild.icon || null,
                            iconURL: guild.icon 
                                ? `https://cdn.discordapp.com/icons/${guild.id}/${guild.icon}.png`
                                : '/img/logo.png',
                            botInGuild: this.client?.guilds?.cache?.has(guild.id) || false
                        });
                    }
                }
            }

            console.log(`✅ [GUILDS] ${guilds.length} guilds com permissão de admin encontradas`);
            return guilds;

        } catch (error) {
            console.error('❌ [GUILDS] Erro ao processar guilds:', error);
            return [];
        }
    }

    setupErrorHandling() {
        // 404 Handler
        this.app.use((req, res) => {
            console.log(`❌ [404] Página não encontrada: ${req.url}`);
            res.status(404).render('error', {
                title: 'Página Não Encontrada',
                error: {
                    status: 404,
                    message: 'A página que você procura não foi encontrada.',
                    details: req.url
                }
            });
        });

        // Error Handler
        this.app.use((err, req, res, next) => {
            console.error('❌ [ERROR] Erro no servidor:', err);
            res.status(500).render('error', {
                title: 'Erro Interno',
                error: {
                    status: 500,
                    message: 'Erro interno do servidor.',
                    details: process.env.NODE_ENV === 'development' ? err.message : null
                }
            });
        });
    }

    start() {
        this.server.listen(this.port, () => {
            console.log(`✅ [WEB] Servidor web NOVO rodando em http://localhost:${this.port}`);
            console.log(`📊 [WEB] Cliente Discord: ${this.client?.user?.username || 'Desconectado'}`);
            console.log(`🔄 [WEB] Sistema de tempo real ativo`);
            console.log(`📡 [WEB] APIs completas disponíveis em /api`);
        });
    }
}

module.exports = WebServerNew;