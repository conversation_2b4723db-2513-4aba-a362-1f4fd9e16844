const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('fix-verification')
        .setDescription('🔧 Corrigir configurações de verificação com valores inválidos')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction) {
        const { client, guild } = interaction;

        try {
            await interaction.deferReply({ ephemeral: true });

            console.log(`🔧 [FIX-VERIFICATION] Comando executado por ${interaction.user.tag} em ${guild.name}`);

            // Verificar configuração atual
            const stmt = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ?
            `);
            const currentConfig = stmt.get(guild.id);

            if (!currentConfig) {
                return await interaction.editReply({
                    content: '❌ Nenhuma configuração de verificação encontrada para este servidor.'
                });
            }

            console.log(`🔍 [FIX-VERIFICATION] Configuração atual:`, currentConfig);

            // Verificar se há valores problemáticos
            const hasProblems = 
                currentConfig.verified_role_id === 'undefined' || 
                currentConfig.verified_role_id === 'null' ||
                currentConfig.channel_id === 'undefined' || 
                currentConfig.channel_id === 'null';

            if (!hasProblems) {
                return await interaction.editReply({
                    content: '✅ Configuração de verificação está correta. Nenhuma correção necessária.'
                });
            }

            // Corrigir valores problemáticos
            console.log(`🔧 [FIX-VERIFICATION] Corrigindo valores inválidos...`);

            const updateStmt = client.database.db.prepare(`
                UPDATE verification_config 
                SET verified_role_id = CASE 
                        WHEN verified_role_id IN ('undefined', 'null') THEN NULL 
                        ELSE verified_role_id 
                    END,
                    channel_id = CASE 
                        WHEN channel_id IN ('undefined', 'null') THEN NULL 
                        ELSE channel_id 
                    END,
                    enabled = 0
                WHERE guild_id = ?
            `);

            const result = updateStmt.run(guild.id);

            if (result.changes > 0) {
                console.log(`✅ [FIX-VERIFICATION] Configuração corrigida para ${guild.name}`);

                // Verificar configuração após correção
                const fixedStmt = client.database.db.prepare(`
                    SELECT * FROM verification_config WHERE guild_id = ?
                `);
                const fixedConfig = fixedStmt.get(guild.id);

                console.log(`🔍 [FIX-VERIFICATION] Configuração após correção:`, fixedConfig);

                await interaction.editReply({
                    content: `✅ **Configuração de verificação corrigida!**\n\n` +
                            `🔧 Valores inválidos foram removidos\n` +
                            `⚠️ Sistema de verificação foi desativado\n` +
                            `📝 Configure novamente através do dashboard\n\n` +
                            `**Status atual:**\n` +
                            `• Canal: ${fixedConfig.channel_id || 'Não configurado'}\n` +
                            `• Cargo: ${fixedConfig.verified_role_id || 'Não configurado'}\n` +
                            `• Ativo: ${fixedConfig.enabled ? 'Sim' : 'Não'}`
                });
            } else {
                await interaction.editReply({
                    content: '❌ Erro ao corrigir configuração. Tente novamente.'
                });
            }

        } catch (error) {
            console.error('❌ [FIX-VERIFICATION] Erro:', error);
            
            const errorMessage = interaction.deferred ? 
                { content: `❌ Erro ao corrigir configuração: ${error.message}` } :
                { content: `❌ Erro ao corrigir configuração: ${error.message}`, ephemeral: true };

            if (interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    }
};
