/**
 * ========================================
 * COMANDO DE ESTATÍSTICAS
 * Ver analytics do servidor
 * ========================================
 */

const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('stats')
        .setDescription('📊 Ver estatísticas do servidor')
        .addSubcommand(subcommand =>
            subcommand
                .setName('server')
                .setDescription('Estatísticas gerais do servidor')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('user')
                .setDescription('Estatísticas de um usuário')
                .addUserOption(option =>
                    option
                        .setName('usuario')
                        .setDescription('Usuário para ver estatísticas')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('channels')
                .setDescription('Top canais mais ativos')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('report')
                .setDescription('Gerar relatório completo')
                .addStringOption(option =>
                    option
                        .setName('tipo')
                        .setDescription('Tipo de relatório')
                        .addChoices(
                            { name: 'Diário', value: 'daily' },
                            { name: 'Semanal', value: 'weekly' }
                        )
                        .setRequired(false)
                )
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'server':
                    await this.showServerStats(interaction);
                    break;
                case 'user':
                    await this.showUserStats(interaction);
                    break;
                case 'channels':
                    await this.showChannelStats(interaction);
                    break;
                case 'report':
                    await this.generateReport(interaction);
                    break;
            }
        } catch (error) {
            console.error('Erro no comando stats:', error);
            
            const errorMessage = '❌ Ocorreu um erro ao buscar estatísticas!';
            
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    },

    async showServerStats(interaction) {
        await interaction.deferReply();

        const stats = await interaction.client.analytics.getGuildStats(interaction.guild.id);
        
        if (!stats) {
            return await interaction.editReply({
                content: '❌ Não foi possível carregar as estatísticas!'
            });
        }

        const embed = {
            color: 0x00ff7f,
            title: `📊 Estatísticas - ${interaction.guild.name}`,
            fields: [
                {
                    name: '👥 Membros',
                    value: `**Total:** ${stats.basic.totalMembers}\n**Entraram hoje:** ${stats.members.joinedToday}\n**Saíram hoje:** ${stats.members.leftToday}\n**Taxa de retenção:** ${stats.members.retentionRate}%`,
                    inline: true
                },
                {
                    name: '💬 Atividade',
                    value: `**Mensagens hoje:** ${stats.activity.messagesToday}\n**Total de mensagens:** ${stats.activity.messagesTotal.toLocaleString()}\n**Comandos hoje:** ${stats.activity.commandsToday}\n**Total de comandos:** ${stats.activity.commandsTotal}`,
                    inline: true
                },
                {
                    name: '🛡️ Moderação',
                    value: `**Ações hoje:** ${stats.activity.moderationToday}\n**Total de ações:** ${stats.activity.moderationTotal}`,
                    inline: true
                },
                {
                    name: '📈 Servidor',
                    value: `**Canais:** ${stats.basic.totalChannels}\n**Cargos:** ${stats.basic.totalRoles}\n**Boost Nível:** ${stats.basic.boostLevel}\n**Boosts:** ${stats.basic.boostCount}`,
                    inline: true
                }
            ],
            footer: {
                text: `Última atualização: ${new Date(stats.lastUpdated).toLocaleString('pt-BR')}`
            },
            timestamp: new Date().toISOString()
        };

        await interaction.editReply({ embeds: [embed] });
    },

    async showUserStats(interaction) {
        const user = interaction.options.getUser('usuario') || interaction.user;
        
        await interaction.deferReply();

        try {
            // Buscar estatísticas do usuário
            const userQuery = 'SELECT * FROM user_activity WHERE guild_id = ? AND user_id = ?';
            const userData = await interaction.client.database.get(userQuery, [interaction.guild.id, user.id]);

            if (!userData) {
                return await interaction.editReply({
                    content: `❌ Nenhuma estatística encontrada para ${user.username}!`
                });
            }

            const embed = {
                color: 0x00ff7f,
                title: `📊 Estatísticas - ${user.username}`,
                thumbnail: {
                    url: user.displayAvatarURL({ dynamic: true })
                },
                fields: [
                    {
                        name: '💬 Atividade',
                        value: `**Mensagens:** ${userData.messages || 0}\n**Comandos:** ${userData.commands || 0}\n**Tempo em voz:** ${this.formatTime(userData.voice_time || 0)}`,
                        inline: true
                    },
                    {
                        name: '📅 Última Atividade',
                        value: userData.last_activity ? 
                            `<t:${Math.floor(new Date(userData.last_activity).getTime() / 1000)}:R>` : 
                            'Nunca',
                        inline: true
                    }
                ],
                footer: {
                    text: 'Estatísticas do servidor atual'
                }
            };

            // Adicionar dados de XP se disponível
            if (interaction.client.levels) {
                const levelData = await interaction.client.levels.getUserData(user.id, interaction.guild.id);
                embed.fields.push({
                    name: '🏆 Níveis',
                    value: `**Nível:** ${levelData.level}\n**XP:** ${levelData.xp.toLocaleString()}`,
                    inline: true
                });
            }

            // Adicionar dados de economia se disponível
            if (interaction.client.economy) {
                const balance = await interaction.client.economy.getBalance(user.id, interaction.guild.id);
                embed.fields.push({
                    name: '💰 Economia',
                    value: `**Saldo:** ${balance} coins`,
                    inline: true
                });
            }

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Erro ao buscar estatísticas do usuário:', error);
            await interaction.editReply({
                content: '❌ Erro ao buscar estatísticas do usuário!'
            });
        }
    },

    async showChannelStats(interaction) {
        await interaction.deferReply();

        const stats = await interaction.client.analytics.getGuildStats(interaction.guild.id);
        
        if (!stats || !stats.topChannels || stats.topChannels.length === 0) {
            return await interaction.editReply({
                content: '❌ Nenhuma estatística de canal encontrada!'
            });
        }

        const embed = {
            color: 0x00ff7f,
            title: '📊 Top Canais Mais Ativos',
            description: stats.topChannels.map((channel, index) => {
                const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                return `${medal} #${channel.channel} - **${channel.messages}** mensagens`;
            }).join('\n'),
            footer: {
                text: 'Baseado no total de mensagens enviadas'
            }
        };

        await interaction.editReply({ embeds: [embed] });
    },

    async generateReport(interaction) {
        const reportType = interaction.options.getString('tipo') || 'daily';
        
        await interaction.deferReply();

        try {
            let report;
            
            if (reportType === 'daily') {
                report = await interaction.client.analytics.generateDailyReport(interaction.guild.id);
            } else {
                report = await interaction.client.analytics.generateWeeklyReport(interaction.guild.id);
            }

            if (!report) {
                return await interaction.editReply({
                    content: '❌ Não foi possível gerar o relatório!'
                });
            }

            await interaction.editReply({ embeds: [report] });

        } catch (error) {
            console.error('Erro ao gerar relatório:', error);
            await interaction.editReply({
                content: '❌ Erro ao gerar relatório!'
            });
        }
    },

    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m`;
        } else {
            return `${seconds}s`;
        }
    }
};
