/**
 * ========================================
 * COMANDO: COMANDOS DE MODERAÇÃO
 * Lista todos os comandos de moderação
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('comandos')
        .setNameLocalizations({
            'pt-BR': 'comandos'
        })
        .setDescription('Lista todos os comandos de moderação disponíveis')
        .setDescriptionLocalizations({
            'pt-BR': 'Mostra uma lista completa de todos os comandos de moderação com exemplos de uso'
        }),

    category: 'moderacao',
    cooldown: 5,

    async execute(interaction) {
        try {
            // Verificar se o usuário tem permissão de moderador
            if (!interaction.member.permissions.has(['ModerateMembers', 'ManageMessages', 'Administrator'])) {
                return await interaction.reply({
                    content: 'Você precisa ter permissões de**Moderador**para ver os comandos de moderação!',
                    ephemeral: true
                });
            }

            const client = interaction.client;

            // Create professional main embed
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle(SVGIcons.bold('Comandos de Moderação - Nodex | Moderação'))
                .setDescription('**Arsenal completo para moderação profissional**\n\nTodos os comandos necessários para manter sua comunidade segura e organizada.')
                .setThumbnail(client.user.displayAvatarURL({ size: 256 }))
                .addFields(
                    {
                        name: '**Punições Básicas**',
                        value: [
                            '**`/ban`**- Banir usuários permanentemente',
                            '**`/kick`**- Expulsar usuários do servidor',
                            '**`/timeout`**- Aplicar timeout temporário',
                            '**`/warn`**- Advertir usuários (sistema progressivo)',
                            '**`/unban`**- Remover banimento de usuários'
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '**Limpeza e Organização**',
                        value: [
                            '**`/clear`**- Limpar mensagens em massa',
                            '**`/clear user`**- Limpar mensagens de usuário específico',
                            '**`/clear bots`**- Limpar apenas mensagens de bots',
                            '**`/clear embeds`**- Limpar mensagens com embeds',
                            '**`/clear attachments`**- Limpar mensagens com anexos'
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '**Informações e Histórico**',
                        value: [
                            '**`/userinfo`**- Informações detalhadas do usuário',
                            '**`/serverinfo`**- Informações do servidor',
                            '**`/stats-moderacao`**- Estatísticas de moderação',
                            '**`/botinfo`**- Informações do bot',
                            '**`/recursos`**- Lista de recursos disponíveis'
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: 'Configuração e Administração',
                        value: [
                            '**`/config`**- Configurações do servidor',
                            '**`/dashboard`**- Abrir dashboard web',
                            '**`/backup`**- Sistema de backup',
                            '**`/logs`**- Configurar sistema de logs',
                            '**`/help`**- Ajuda e documentação'
                        ].join('\n'),
                        inline: true
                    },

                    {
                        name: 'Utilidades',
                        value: [
                            '**`/ping`**- Verificar latência do bot',
                            '**`/avatar`**- Mostrar avatar de usuário',
                            '**`/say`**- Fazer o bot enviar mensagem',
                            '**`/testlogs`**- Testar sistema de logs',
                            '**`/comandos`**- Este comando'
                        ].join('\n'),
                        inline: true
                    }
                )
                .addFields({
                    name: 'Dicas de Uso',
                    value: [
                        '**Comandos Slash**- Digite `/` e escolha o comando',
                        '**Parâmetros**- Siga as instruções do Discord',
                        '**Ações Rápidas**- Use botões quando disponíveis',
                        '**Permissões**- Alguns comandos requerem permissões específicas',
                        '**Dashboard**- Configure tudo via web com `/dashboard`'
                    ].join('\n'),
                    inline: false
                })
                .addFields({
                    name: 'Recursos Automáticos',
                    value: [
                        '**IA de Moderação**- Detecção automática de toxicidade',
                        '**Anti-Raid**- Proteção contra invasões',
                        '**Auto-Logs**- Registro automático de ações',
                        '**Sistema Progressivo**- Punições escalonadas',
                        '**Sincronização**- Dashboard e Discord em tempo real'
                    ].join('\n'),
                    inline: false
                })
                .setFooter({
                    text: `Nodex | Moderação • ${client.commands.size} comandos disponíveis • Solicitado por ${interaction.user.username}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            // Criar botões
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('Dashboard Completo')
                        .setStyle(ButtonStyle.Link)
                        .setURL(process.env.DASHBOARD_URL || 'http://localhost:3000'),
                    new ButtonBuilder()
                        .setLabel('Documentação')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`${process.env.DASHBOARD_URL || 'http://localhost:3000'}/docs`),
                    new ButtonBuilder()
                        .setLabel('Suporte 24/7')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`${process.env.DASHBOARD_URL || 'http://localhost:3000'}/support`)
                );

            await interaction.reply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error('Erro no comando comandos:', error);
            
            const errorMessage = 'Ocorreu um erro ao buscar os comandos de moderação!';
            
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
};
