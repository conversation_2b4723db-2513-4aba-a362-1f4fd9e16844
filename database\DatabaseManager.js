/**
 * ========================================
 * GERENCIADOR DE BANCO DE DADOS
 * Sistema completo de persistência de dados
 * ========================================
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class DatabaseManager {
    constructor() {
        this.db = null;
        this.dbPath = process.env.DATABASE_PATH || './database/moderacao.db';
        this.isInitialized = false;
    }

    /**
     * Inicializa o banco de dados e cria as tabelas
     */
    async initialize() {
        try {
            // Criar diretório se não existir
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }

            // Conectar ao banco usando better-sqlite3
            this.db = new Database(this.dbPath);
            console.log('✅ Conectado ao banco de dados SQLite');

            // Configurar pragmas
            this.db.pragma('journal_mode = WAL');
            this.db.pragma('synchronous = NORMAL');
            this.db.pragma('cache_size = 1000');
            this.db.pragma('temp_store = MEMORY');

            // Criar tabelas
            this.createTables();
            this.createAdvancedTables();
            this.insertDefaultData();

            this.isInitialized = true;
            console.log('✅ Banco de dados inicializado com sucesso');

        } catch (error) {
            console.error('❌ Erro ao inicializar banco de dados:', error);
            throw error;
        }
    }

    /**
     * Cria todas as tabelas necessárias
     */
    createTables() {
        const tables = [
            // Configurações dos servidores
            `CREATE TABLE IF NOT EXISTS guild_configs (
                guild_id TEXT PRIMARY KEY,
                prefix TEXT DEFAULT '!',
                language TEXT DEFAULT 'pt-BR',
                timezone TEXT DEFAULT 'America/Sao_Paulo',
                auto_mod_enabled BOOLEAN DEFAULT 1,
                anti_raid_enabled BOOLEAN DEFAULT 1,
                log_channel_id TEXT,
                mod_log_channel_id TEXT,
                welcome_channel_id TEXT,
                mute_role_id TEXT,
                quarantine_role_id TEXT,
                mod_roles TEXT DEFAULT '[]',
                admin_roles TEXT DEFAULT '[]',
                ignored_channels TEXT DEFAULT '[]',
                settings TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Sistema de advertências
            `CREATE TABLE IF NOT EXISTS warnings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                moderator_id TEXT NOT NULL,
                reason TEXT NOT NULL,
                severity INTEGER DEFAULT 1,
                expires_at DATETIME,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Logs de moderação
            `CREATE TABLE IF NOT EXISTS moderation_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                moderator_id TEXT,
                action TEXT NOT NULL,
                reason TEXT,
                duration INTEGER,
                channel_id TEXT,
                message_id TEXT,
                metadata TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Sistema anti-raid
            `CREATE TABLE IF NOT EXISTS raid_detections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT NOT NULL,
                detection_type TEXT NOT NULL,
                severity INTEGER DEFAULT 1,
                users_affected TEXT DEFAULT '[]',
                actions_taken TEXT DEFAULT '[]',
                metadata TEXT DEFAULT '{}',
                resolved BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Estatísticas de usuários
            `CREATE TABLE IF NOT EXISTS user_stats (
                guild_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                messages_sent INTEGER DEFAULT 0,
                warnings_received INTEGER DEFAULT 0,
                mutes_received INTEGER DEFAULT 0,
                kicks_received INTEGER DEFAULT 0,
                bans_received INTEGER DEFAULT 0,
                reputation_score INTEGER DEFAULT 0,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                joined_at DATETIME,
                PRIMARY KEY (guild_id, user_id)
            )`
        ];

        tables.forEach((table, index) => {
            try {
                this.db.exec(table);
                console.log(`✅ Tabela ${index + 1}/${tables.length} criada`);
            } catch (err) {
                console.error(`❌ Erro ao criar tabela ${index + 1}:`, err);
            }
        });

        this.createIndexes();
    }

    /**
     * Criar tabelas avançadas para novos sistemas
     */
    createAdvancedTables() {
        const advancedTables = [


            // Sistema de Tickets removido do projeto



            // Analytics - Atividade de Usuários
            `CREATE TABLE IF NOT EXISTS user_activity (
                guild_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                messages INTEGER DEFAULT 0,
                commands INTEGER DEFAULT 0,
                voice_time INTEGER DEFAULT 0,
                last_activity TEXT,
                PRIMARY KEY (guild_id, user_id)
            )`,

            // Analytics - Estatísticas de Canais
            `CREATE TABLE IF NOT EXISTS channel_stats (
                guild_id TEXT NOT NULL,
                channel_id TEXT NOT NULL,
                messages INTEGER DEFAULT 0,
                last_activity TEXT,
                PRIMARY KEY (guild_id, channel_id)
            )`,

            // Analytics - Atividade de Membros
            `CREATE TABLE IF NOT EXISTS member_activity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                action TEXT NOT NULL,
                timestamp TEXT NOT NULL
            )`,

            // Analytics - Ações de Moderação
            `CREATE TABLE IF NOT EXISTS moderation_analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT NOT NULL,
                action TEXT NOT NULL,
                user_id TEXT NOT NULL,
                moderator_id TEXT NOT NULL,
                timestamp TEXT NOT NULL
            )`,

            // Analytics - Estatísticas Gerais
            `CREATE TABLE IF NOT EXISTS guild_analytics (
                guild_id TEXT PRIMARY KEY,
                enabled BOOLEAN DEFAULT 0,
                stats TEXT NOT NULL DEFAULT '{}',
                updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
            )`,

            // Backups - Sistema de Backup
            `CREATE TABLE IF NOT EXISTS backups (
                id TEXT PRIMARY KEY,
                guild_id TEXT NOT NULL,
                guild_name TEXT NOT NULL,
                type TEXT NOT NULL DEFAULT 'full',
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT NOT NULL DEFAULT 'Sistema',
                size INTEGER DEFAULT 0,
                components TEXT DEFAULT '[]'
            )`,

            // Command States - Estados dos Comandos por Servidor
            `CREATE TABLE IF NOT EXISTS command_states (
                guild_id TEXT NOT NULL,
                command_name TEXT NOT NULL,
                enabled BOOLEAN DEFAULT 1,
                disabled_reason TEXT,
                disabled_by TEXT,
                disabled_at DATETIME,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (guild_id, command_name)
            )`,

            // Auto-Roles - Sistema removido do projeto conforme solicitado
            /*
            `CREATE TABLE IF NOT EXISTS auto_roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT NOT NULL,
                role_id TEXT NOT NULL,
                role_name TEXT NOT NULL,
                trigger_type TEXT NOT NULL, -- 'time', 'verification', 'reaction', 'conditional'
                trigger_value TEXT, -- tempo em segundos, message_id para reação, etc
                conditions TEXT DEFAULT '{}', -- JSON com condições extras
                enabled BOOLEAN DEFAULT 1,
                created_by TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            */

            // Verification System - Sistema de Verificação
            `CREATE TABLE IF NOT EXISTS verification_config (
                guild_id TEXT PRIMARY KEY,
                enabled BOOLEAN DEFAULT 0,
                channel_id TEXT,
                method TEXT DEFAULT 'reaction', -- 'reaction', 'captcha_math', 'captcha_text', 'captcha_emoji', 'manual', 'combined'
                message_id TEXT,
                verified_role_id TEXT,
                unverified_role_id TEXT,
                captcha_type TEXT DEFAULT 'math', -- 'math', 'text', 'emoji'
                timeout_minutes INTEGER DEFAULT 30,
                rules_text TEXT,
                welcome_message TEXT,
                dm_enabled BOOLEAN DEFAULT 1,
                log_enabled BOOLEAN DEFAULT 1,
                auto_kick BOOLEAN DEFAULT 0,
                kick_time INTEGER DEFAULT 60,
                combined_captcha BOOLEAN DEFAULT 1,
                combined_rules BOOLEAN DEFAULT 1,
                ip_check_enabled BOOLEAN DEFAULT 0,
                max_attempts INTEGER DEFAULT 3,
                anti_bot_enabled BOOLEAN DEFAULT 1,
                settings TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Verification Logs - Logs de Verificação
            `CREATE TABLE IF NOT EXISTS verification_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                action TEXT NOT NULL, -- 'started', 'completed', 'failed', 'timeout'
                method TEXT NOT NULL,
                details TEXT DEFAULT '{}',
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Auto-Role Assignments - Histórico de Atribuições
            `CREATE TABLE IF NOT EXISTS auto_role_assignments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                role_id TEXT NOT NULL,
                auto_role_id INTEGER NOT NULL,
                assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                assigned_by TEXT DEFAULT 'system',
                FOREIGN KEY (auto_role_id) REFERENCES auto_roles (id)
            )`,

            // Member Verification Status - Status de Verificação dos Membros
            `CREATE TABLE IF NOT EXISTS member_verification (
                guild_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                verified BOOLEAN DEFAULT 0,
                verification_method TEXT,
                verified_at DATETIME,
                attempts INTEGER DEFAULT 0,
                last_attempt DATETIME,
                ip_address TEXT,
                user_agent TEXT,
                suspicious_ip BOOLEAN DEFAULT 0,
                PRIMARY KEY (guild_id, user_id)
            )`,

            // Sistema de Backups
            `CREATE TABLE IF NOT EXISTS backups (
                id TEXT PRIMARY KEY,
                guild_id TEXT NOT NULL,
                guild_name TEXT NOT NULL,
                type TEXT NOT NULL,
                created_at TEXT NOT NULL,
                created_by TEXT NOT NULL,
                size INTEGER DEFAULT 0
            )`
        ];

        if (advancedTables.length === 0) {
            return;
        }

        advancedTables.forEach((sql, index) => {
            try {
                this.db.exec(sql);
                console.log(`✅ Tabela avançada ${index + 1}/${advancedTables.length} criada`);
            } catch (err) {
                console.error(`❌ Erro ao criar tabela avançada ${index + 1}:`, err);
            }
        });

        console.log('✅ Todas as tabelas avançadas criadas com sucesso');
    }

    /**
     * Cria índices para melhorar a performance
     */
    createIndexes() {
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_warnings_guild_user ON warnings(guild_id, user_id)',
            'CREATE INDEX IF NOT EXISTS idx_warnings_active ON warnings(is_active, expires_at)',
            'CREATE INDEX IF NOT EXISTS idx_modlogs_guild_date ON moderation_logs(guild_id, created_at)',
            'CREATE INDEX IF NOT EXISTS idx_modlogs_user ON moderation_logs(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_raids_guild_date ON raid_detections(guild_id, created_at)',
            'CREATE INDEX IF NOT EXISTS idx_userstats_guild ON user_stats(guild_id)'
        ];

        indexes.forEach((index, i) => {
            try {
                this.db.exec(index);
                console.log(`✅ Índice ${i + 1}/${indexes.length} criado`);
            } catch (err) {
                console.error(`❌ Erro ao criar índice ${i + 1}:`, err);
            }
        });
    }

    /**
     * Insere dados padrão necessários
     */
    insertDefaultData() {
        console.log('📊 Dados padrão verificados');
    }

    /**
     * Obtém configuração de um servidor
     */
    getGuildConfig(guildId) {
        try {
            const stmt = this.db.prepare('SELECT * FROM guild_configs WHERE guild_id = ?');
            const row = stmt.get(guildId);

            if (row) {
                // Parse dos campos JSON
                try {
                    if (row.mod_roles && typeof row.mod_roles === 'string') {
                        row.mod_roles = JSON.parse(row.mod_roles);
                    }
                    if (row.admin_roles && typeof row.admin_roles === 'string') {
                        row.admin_roles = JSON.parse(row.admin_roles);
                    }
                    if (row.ignored_channels && typeof row.ignored_channels === 'string') {
                        row.ignored_channels = JSON.parse(row.ignored_channels);
                    }
                    if (row.settings && typeof row.settings === 'string') {
                        row.settings = JSON.parse(row.settings);
                    }
                } catch (parseError) {
                    console.warn(`⚠️ [DB] Erro ao fazer parse dos dados JSON:`, parseError);
                }

                // Buscar configuração de verificação se existir
                const verStmt = this.db.prepare('SELECT * FROM verification_config WHERE guild_id = ?');
                const verRow = verStmt.get(guildId);

                if (verRow) {
                    // Reconstruir método completo se for captcha
                    let fullMethod = verRow.method;
                    if (verRow.method === 'captcha' && verRow.captcha_type) {
                        fullMethod = `captcha_${verRow.captcha_type}`;
                    }

                    row.verification = {
                        enabled: Boolean(verRow.enabled),
                        channel_id: verRow.channel_id,
                        method: fullMethod,
                        verified_role_id: verRow.verified_role_id,
                        unverified_role_id: verRow.unverified_role_id,
                        timeout_minutes: verRow.timeout_minutes,
                        captcha_type: verRow.captcha_type,
                        rules_text: verRow.rules_text,
                        welcome_message: verRow.welcome_message,
                        dm_enabled: Boolean(verRow.dm_enabled),
                        log_enabled: Boolean(verRow.log_enabled),
                        auto_kick: Boolean(verRow.auto_kick),
                        kick_time: verRow.kick_time,
                        combined_captcha: Boolean(verRow.combined_captcha),
                        combined_rules: Boolean(verRow.combined_rules),
                        ip_check_enabled: Boolean(verRow.ip_check_enabled),
                        max_attempts: verRow.max_attempts || 3,
                        anti_bot_enabled: Boolean(verRow.anti_bot_enabled)
                    };
                }
            }

            return row;
        } catch (err) {
            console.error(`❌ [DB] Erro ao buscar configuração:`, err);
            throw err;
        }
    }

    /**
     * Salva configuração de um servidor
     */
    saveGuildConfig(guildId, config) {
        try {
            const stmt = this.db.prepare(`
                INSERT OR REPLACE INTO guild_configs
                (guild_id, prefix, language, timezone, auto_mod_enabled, anti_raid_enabled,
                 log_channel_id, mod_log_channel_id, welcome_channel_id, mute_role_id,
                 quarantine_role_id, mod_roles, admin_roles, ignored_channels, settings, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `);

            const settingsJson = JSON.stringify(config.settings || {});

            stmt.run(
                guildId,
                config.prefix || '!',
                config.language || 'pt-BR',
                config.timezone || 'America/Sao_Paulo',
                config.auto_mod_enabled ? 1 : 0,
                config.anti_raid_enabled ? 1 : 0,
                config.log_channel_id,
                config.mod_log_channel_id,
                config.welcome_channel_id,
                config.mute_role_id,
                config.quarantine_role_id,
                JSON.stringify(config.mod_roles || []),
                JSON.stringify(config.admin_roles || []),
                JSON.stringify(config.ignored_channels || []),
                settingsJson
            );

            console.log(`✅ [DB] Configuração salva para guild ${guildId}`);

            // Notificar o bot sobre a mudança de configuração (com proteção contra loops)
            if (this.client && !config._skipEmit) {
                this.client.emit('configUpdated', guildId, config, this.client);
            }

            return { success: true, guildId, changes: Object.keys(config) };
        } catch (err) {
            console.error(`❌ [DB] Erro ao salvar configuração:`, err);
            throw err;
        }
    }

    /**
     * Atualiza uma seção específica da configuração
     */
    async updateGuildConfigSection(guildId, section, sectionConfig) {
        try {
            console.log(`🔧 [DB] Atualizando seção ${section} para guild ${guildId}`);
            console.log(`🔧 [DB] Dados recebidos:`, sectionConfig);

            // Buscar configuração atual
            let currentConfig = this.getGuildConfig(guildId);

            if (!currentConfig) {
                currentConfig = {
                    guild_id: guildId,
                    prefix: '!',
                    language: 'pt-BR',
                    timezone: 'America/Sao_Paulo',
                    auto_mod_enabled: 1,
                    anti_raid_enabled: 1,
                    settings: {}
                };
            }

            // Parse das configurações atuais
            let settings = {};
            try {
                if (typeof currentConfig.settings === 'string') {
                    settings = JSON.parse(currentConfig.settings || '{}');
                } else {
                    settings = currentConfig.settings || {};
                }
            } catch (e) {
                settings = {};
            }

            console.log(`🔧 [DB] Configurações atuais:`, settings);

            // VALIDAÇÃO CRÍTICA: Apenas atualizar configurações específicas da seção
            // Para evitar ativação acidental de outros módulos
            if (sectionConfig.settings) {
                const allowedSettings = this.getSectionAllowedSettings(section);
                console.log(`🔧 [DB] Configurações permitidas para ${section}:`, allowedSettings);

                let updatedCount = 0;
                let rejectedCount = 0;

                Object.keys(sectionConfig.settings).forEach(key => {
                    const isAllowed = allowedSettings.includes(key) ||
                                    allowedSettings.some(s => s.endsWith('_') && key.startsWith(s));

                    if (isAllowed) {
                        const oldValue = settings[key];
                        settings[key] = sectionConfig.settings[key];
                        console.log(`✅ [DB] ATUALIZADO ${key}: ${oldValue} → ${sectionConfig.settings[key]}`);
                        updatedCount++;
                    } else {
                        console.warn(`🚫 [DB] REJEITADO ${key} - não pertence à seção ${section}`);
                        rejectedCount++;
                    }
                });

                console.log(`🔧 [DB] Resultado: ${updatedCount} atualizadas, ${rejectedCount} rejeitadas`);

                // VALIDAÇÃO EXTRA: Se nenhuma configuração foi atualizada, algo está errado
                if (updatedCount === 0) {
                    console.warn(`⚠️ [DB] AVISO: Nenhuma configuração foi atualizada para a seção ${section}`);
                }
            }

            // Atualizar a configuração
            currentConfig.settings = settings;
            currentConfig.updated_at = new Date().toISOString();

            console.log(`🔧 [DB] Configurações finais:`, settings);

            // Salvar a configuração atualizada
            const result = this.saveGuildConfig(guildId, currentConfig);
            console.log(`✅ [DB] Seção ${section} atualizada com sucesso`);

            return result;
        } catch (error) {
            console.error(`❌ [DB] Erro ao atualizar seção ${section}:`, error);
            throw error;
        }
    }

    /**
     * Obter configurações permitidas para cada seção
     */
    getSectionAllowedSettings(section) {
        const sectionMappings = {
            'geral': ['prefix', 'language', 'timezone', 'bot_color', 'embed_color'],
            'moderacao': ['auto_mod_enabled', 'anti_spam_enabled', 'anti_links_enabled', 'anti_caps_enabled', 'anti_mentions_enabled', 'max_mentions', 'max_message_length'],
            'ai': ['ai_moderation_enabled', 'ai_sensitivity', 'second_chance_enabled', 'ai_dm_user', 'ai_public_message', 'ai_timeout_duration'],
            'antiraid': ['anti_raid_enabled', 'max_joins_per_minute', 'min_account_age', 'quarantine_enabled', 'quarantine_duration'],
            'logs': ['log_enabled', 'log_channel', 'mod_log_enabled', 'mod_log_channel', 'message_log_enabled', 'join_leave_log_enabled'],
            'comandos': ['cmd_', 'mod_role_required', 'admin_only_config'], // cmd_ prefix para comandos
            'cargos': ['admin_role', 'moderator_role', 'helper_role', 'quarantine_role', 'verified_role'],

            'analytics': ['analytics_enabled', 'analytics_retention_days', 'analytics_detailed_logs'],
            'backup': ['backup_enabled', 'backup_interval', 'backup_retention', 'backup_include_messages']
        };

        return sectionMappings[section] || [];
    }

    /**
     * Definir referência do cliente para notificações
     */
    setClient(client) {
        this.client = client;
    }

    /**
     * Adiciona uma advertência
     */
    addWarning(guildId, userId, moderatorId, reason, severity = 1, expiresAt = null) {
        try {
            const stmt = this.db.prepare(`
                INSERT INTO warnings (guild_id, user_id, moderator_id, reason, severity, expires_at)
                VALUES (?, ?, ?, ?, ?, ?)
            `);

            const result = stmt.run(guildId, userId, moderatorId, reason, severity, expiresAt);
            return Promise.resolve({
                lastID: result.lastInsertRowid,
                changes: result.changes
            });
        } catch (error) {
            console.error('Erro ao adicionar advertência:', error);
            return Promise.reject(error);
        }
    }

    /**
     * Obtém advertências ativas de um usuário
     */
    getUserWarnings(guildId, userId) {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM warnings
                WHERE guild_id = ? AND user_id = ? AND is_active = 1
                AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
                ORDER BY created_at DESC
            `);

            const rows = stmt.all(guildId, userId);
            return Promise.resolve(rows);
        } catch (error) {
            console.error('Erro ao obter advertências do usuário:', error);
            return Promise.reject(error);
        }
    }

    /**
     * Registra uma ação de moderação
     */
    logModerationAction(guildId, userId, moderatorId, action, reason, duration = null, channelId = null, messageId = null, metadata = {}) {
        try {
            const stmt = this.db.prepare(`
                INSERT INTO moderation_logs
                (guild_id, user_id, moderator_id, action, reason, duration, channel_id, message_id, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            const result = stmt.run(guildId, userId, moderatorId, action, reason, duration, channelId, messageId, JSON.stringify(metadata));
            return Promise.resolve({
                lastID: result.lastInsertRowid,
                changes: result.changes
            });
        } catch (error) {
            console.error('Erro ao registrar ação de moderação:', error);
            return Promise.reject(error);
        }
    }

    /**
     * Obter histórico de moderação de um usuário
     */
    getModerationHistory(userId, guildId, limit = 10) {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM moderation_logs
                WHERE user_id = ? AND guild_id = ?
                ORDER BY created_at DESC
                LIMIT ?
            `);

            const rows = stmt.all(userId, guildId, limit);

            return rows.map(row => {
                let metadata = {};
                try {
                    metadata = row.metadata ? JSON.parse(row.metadata) : {};
                } catch (e) {
                    metadata = {};
                }

                return {
                    ...row,
                    metadata: metadata,
                    automatic: metadata.automatic || false
                };
            });
        } catch (error) {
            console.error('Erro ao obter histórico de moderação:', error);
            return [];
        }
    }

    /**
     * Obter todo o histórico de moderação de um servidor
     */
    getAllModerationHistory(guildId, limit = 100) {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM moderation_logs
                WHERE guild_id = ?
                ORDER BY created_at DESC
                LIMIT ?
            `);

            const rows = stmt.all(guildId, limit);

            return rows.map(row => {
                let metadata = {};
                try {
                    metadata = row.metadata ? JSON.parse(row.metadata) : {};
                } catch (e) {
                    metadata = {};
                }

                return {
                    ...row,
                    metadata: metadata,
                    automatic: metadata.automatic || false
                };
            });
        } catch (error) {
            console.error('Erro ao obter histórico completo de moderação:', error);
            return [];
        }
    }

    /**
     * Executa uma query que retorna múltiplas linhas
     */
    all(query, params = []) {
        try {
            const stmt = this.db.prepare(query);
            const rows = stmt.all(...params);
            return Promise.resolve(rows || []);
        } catch (error) {
            console.error('Erro na query all:', error);
            return Promise.reject(error);
        }
    }

    /**
     * Executa uma query que retorna uma única linha
     */
    get(query, params = []) {
        try {
            const stmt = this.db.prepare(query);
            const row = stmt.get(...params);
            return Promise.resolve(row);
        } catch (error) {
            console.error('Erro na query get:', error);
            return Promise.reject(error);
        }
    }

    /**
     * Executa uma query de modificação (INSERT, UPDATE, DELETE)
     */
    run(query, params = []) {
        try {
            const stmt = this.db.prepare(query);
            const result = stmt.run(...params);
            return Promise.resolve({
                lastID: result.lastInsertRowid,
                changes: result.changes
            });
        } catch (error) {
            console.error('Erro na query run:', error);
            return Promise.reject(error);
        }
    }

    /**
     * Obter estado de um comando específico
     */
    getCommandState(guildId, commandName) {
        try {
            const result = this.db.get(`
                SELECT * FROM command_states
                WHERE guild_id = ? AND command_name = ?
            `, [guildId, commandName]);

            // Se não existe registro, comando está habilitado por padrão
            if (!result) {
                return { enabled: true, command_name: commandName };
            }

            return {
                ...result,
                enabled: Boolean(result.enabled)
            };
        } catch (error) {
            console.error('Erro ao obter estado do comando:', error);
            return { enabled: true, command_name: commandName };
        }
    }

    /**
     * Obter estados de todos os comandos de um servidor
     */
    getAllCommandStates(guildId) {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM command_states
                WHERE guild_id = ?
            `);
            const results = stmt.all(guildId);

            // Converter para objeto para fácil acesso
            const commandStates = {};
            if (results && Array.isArray(results)) {
                results.forEach(row => {
                    commandStates[row.command_name] = {
                        ...row,
                        enabled: Boolean(row.enabled)
                    };
                });
            }

            return commandStates;
        } catch (error) {
            console.error('Erro ao obter estados dos comandos:', error);
            return {};
        }
    }

    /**
     * Definir estado de um comando
     */
    setCommandState(guildId, commandName, enabled, disabledBy = null, disabledReason = null) {
        try {
            const disabledAt = enabled ? null : new Date().toISOString();

            const stmt = this.db.prepare(`
                INSERT OR REPLACE INTO command_states
                (guild_id, command_name, enabled, disabled_by, disabled_reason, disabled_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `);

            stmt.run(
                guildId,
                commandName,
                enabled ? 1 : 0,
                disabledBy,
                disabledReason,
                disabledAt
            );

            console.log(`✅ [DB] Estado do comando ${commandName} definido como ${enabled ? 'habilitado' : 'desabilitado'} para guild ${guildId}`);

            // Emitir evento para atualizar cache
            if (this.client) {
                this.client.emit('commandStateUpdated', guildId, commandName, enabled);
            }

            return true;
        } catch (error) {
            console.error('Erro ao definir estado do comando:', error);
            return false;
        }
    }

    /**
     * Definir estados de múltiplos comandos
     */
    setMultipleCommandStates(guildId, commandStates, updatedBy = null) {
        try {
            // Usar método síncrono simples sem transação para evitar erros
            for (const [commandName, enabled] of Object.entries(commandStates)) {
                const disabledAt = enabled ? null : new Date().toISOString();
                const disabledReason = enabled ? null : 'Desabilitado via dashboard';

                try {
                    const stmt = this.db.prepare(`
                        INSERT OR REPLACE INTO command_states
                        (guild_id, command_name, enabled, disabled_by, disabled_reason, disabled_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    `);

                    stmt.run(
                        guildId,
                        commandName,
                        enabled ? 1 : 0,
                        updatedBy,
                        disabledReason,
                        disabledAt
                    );
                } catch (error) {
                    console.error(`Erro ao atualizar comando ${commandName}:`, error);
                }
            }

            console.log(`✅ [DB] Estados de ${Object.keys(commandStates).length} comandos atualizados para guild ${guildId}`);

            // Emitir evento para atualizar cache
            if (this.client) {
                this.client.emit('commandStatesUpdated', guildId, commandStates);
            }

            return true;
        } catch (error) {
            console.error('Erro ao definir estados dos comandos:', error);
            return false;
        }
    }

    /**
     * Inicializar estados padrão dos comandos para um servidor
     */
    initializeDefaultCommandStates(guildId, commandList) {
        try {
            // Usar método síncrono simples sem transação para evitar erros
            const stmt = this.db.prepare(`
                INSERT OR IGNORE INTO command_states
                (guild_id, command_name, enabled, updated_at)
                VALUES (?, ?, 1, CURRENT_TIMESTAMP)
            `);

            commandList.forEach(commandName => {
                try {
                    stmt.run(guildId, commandName);
                } catch (error) {
                    // Ignorar erros de duplicação
                    if (!error.message.includes('UNIQUE constraint failed')) {
                        console.error(`Erro ao inserir comando ${commandName}:`, error);
                    }
                }
            });

            console.log(`✅ [DB] Estados padrão inicializados para ${commandList.length} comandos na guild ${guildId}`);
            return true;
        } catch (error) {
            console.error('Erro ao inicializar estados padrão dos comandos:', error);
            return false;
        }
    }

    /**
     * Fecha a conexão com o banco
     */
    async close() {
        if (this.db) {
            try {
                this.db.close();
                console.log('🔒 Banco de dados fechado');
            } catch (err) {
                console.error('Erro ao fechar banco:', err);
            }
            this.isInitialized = false;
        }
    }
}

module.exports = DatabaseManager;
