/**
 * ========================================
 * NOVA MODERAÇÃO BOT
 * Bot de moderação inteligente para Discord
 * 100% em Português Brasileiro
 * ========================================
 */

require('dotenv').config();
const { Client, Collection, GatewayIntentBits, Partials } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Importar sistemas
const DatabaseManager = require('./database/DatabaseManager');
const Logger = require('./utils/Logger');
const ConfigManager = require('./utils/ConfigManager');
const CommandStateManager = require('./utils/CommandStateManager');
const NotificationManager = require('./utils/NotificationManager');
const AntiRaidSystem = require('./systems/AntiRaidSystem');
const AutoModeration = require('./moderation/AutoModeration');
const AIModeration = require('./moderation/AIModeration');

// Importar servidor web NOVO
const WebServerNew = require('./web/server-new.js');

// Verificar variáveis de ambiente obrigatórias
if (!process.env.DISCORD_TOKEN) {
    console.error('▲ DISCORD_TOKEN não encontrado no arquivo .env');
    process.exit(1);
}

if (!process.env.CLIENT_ID) {
    console.error('▲ CLIENT_ID não encontrado no arquivo .env');
    process.exit(1);
}

// Criar cliente Discord
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildPresences,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildMessageReactions,
        GatewayIntentBits.DirectMessages
    ],
    partials: [
        Partials.Message,
        Partials.Channel,
        Partials.Reaction,
        Partials.User,
        Partials.GuildMember
    ]
});

// Coleções para comandos e cooldowns
client.commands = new Collection();
client.cooldowns = new Collection();

// Coleção para verificações pendentes
client.pendingVerifications = new Map();

/**
 * Inicialização do bot
 */
async function initializeBot() {
    try {
        console.log('▣ Iniciando Nova Moderação Bot...');

        // Inicializar logger
        client.logger = new Logger();
        client.logger.info('▣ Sistema de logs inicializado');

        // Inicializar gerenciadores
        client.configManager = new ConfigManager();
        client.notificationManager = new NotificationManager(client);
        client.logger.info('▣ Gerenciador de configurações inicializado');

        // Inicializar banco de dados
        client.database = new DatabaseManager();
        await client.database.initialize();
        client.database.setClient(client); // Conectar cliente para notificações
        client.logger.info('▣ Banco de dados inicializado');

        // Carregar comandos
        await loadCommands();
        client.logger.info(`▣ ${client.commands.size} comandos carregados`);

        // Inicializar gerenciador de estados de comandos
        client.commandStateManager = new CommandStateManager(client);
        client.logger.info('▣ Gerenciador de estados de comandos inicializado');

        // Carregar eventos
        await loadEvents();
        client.logger.info('▣ Eventos carregados');

        // Evento de configuração atualizada
        client.on('configUpdated', async ({ guildId, module, changes, updatedBy }) => {
            try {
                const guild = await client.guilds.fetch(guildId);
                if (!guild) return;

                await client.notificationManager.sendModuleUpdate(
                    guild,
                    module,
                    changes,
                    updatedBy
                );
            } catch (error) {
                console.error('❌ Erro ao processar atualização de configuração:', error);
            }
        });

        // Evento de verificação bem-sucedida
        client.on('verificationComplete', async ({ guildId, userId, method }) => {
            try {
                const guild = await client.guilds.fetch(guildId);
                const member = await guild.members.fetch(userId);

                if (guild && member) {
                    await client.notificationManager.sendVerificationSuccess(guild, member, method);
                }
            } catch (error) {
                console.error('❌ Erro ao processar verificação completa:', error);
            }
        });

        // Evento de moderação por IA
        client.on('aiModerationAction', async ({ guildId, action, target, reason }) => {
            try {
                const guild = await client.guilds.fetch(guildId);
                if (guild) {
                    await client.notificationManager.sendAIModerationLog(guild, action, target, reason);
                }
            } catch (error) {
                console.error('❌ Erro ao processar ação de moderação por IA:', error);
            }
        });

        // Inicializar sistemas de moderação
        client.antiRaid = new AntiRaidSystem(client);
        client.autoMod = new AutoModeration(client);
        client.aiMod = new AIModeration(client);
        client.logger.info('▣ Sistemas de moderação inicializados');

        // Sistemas essenciais de moderação inicializados
        // Sistema de Tickets removido do projeto

        const AnalyticsSystem = require('./systems/AnalyticsSystem');
        client.analytics = new AnalyticsSystem(client);
        client.logger.info('▣ Sistema de Analytics de Moderação inicializado');

        const BackupSystem = require('./systems/BackupSystem');
        client.backup = new BackupSystem(client);
        client.logger.info('▣ Sistema de Backup inicializado');

        // Sistema de Auto-Roles removido do projeto conforme solicitado

        // Inicializar Sistema de Verificação
        const VerificationSystem = require('./systems/VerificationSystem');
        client.verificationSystem = new VerificationSystem(client);
        await client.verificationSystem.initialize();
        client.logger.info('▣ Sistema de Verificação inicializado');

        // Inicializar servidor web
        const webServer = new WebServerNew(client);
        webServer.start();
        client.logger.info('▣ Servidor web inicializado');

        // Fazer login
        await client.login(process.env.DISCORD_TOKEN);

    } catch (error) {
        console.error('▲ Erro fatal durante a inicialização:', error);
        process.exit(1);
    }
}

/**
 * Carrega todos os comandos
 */
async function loadCommands() {
    const commandsPath = path.join(__dirname, 'commands');
    
    if (!fs.existsSync(commandsPath)) {
        client.logger.warn('Pasta de comandos não encontrada');
        return;
    }

    const commandFolders = fs.readdirSync(commandsPath);

    for (const folder of commandFolders) {
        const folderPath = path.join(commandsPath, folder);
        
        if (!fs.statSync(folderPath).isDirectory()) continue;

        const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
            const filePath = path.join(folderPath, file);
            
            try {
                const command = require(filePath);
                
                if ('data' in command && 'execute' in command) {
                    client.commands.set(command.data.name, command);
                    client.logger.debug(`Comando carregado: ${command.data.name}`);
                } else {
                    client.logger.warn(`Comando em ${filePath} está faltando propriedades obrigatórias`);
                }
            } catch (error) {
                client.logger.error(`Erro ao carregar comando ${filePath}:`, error);
            }
        }
    }
}

/**
 * Carrega todos os eventos
 */
async function loadEvents() {
    const eventsPath = path.join(__dirname, 'events');
    
    if (!fs.existsSync(eventsPath)) {
        client.logger.warn('Pasta de eventos não encontrada');
        return;
    }

    const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));

    for (const file of eventFiles) {
        const filePath = path.join(eventsPath, file);
        
        try {
            const event = require(filePath);
            
            if (event.once) {
                client.once(event.name, (...args) => event.execute(...args, client));
            } else {
                client.on(event.name, (...args) => event.execute(...args, client));
            }
            
            client.logger.debug(`Evento carregado: ${event.name}`);
        } catch (error) {
            client.logger.error(`Erro ao carregar evento ${filePath}:`, error);
        }
    }
}

/**
 * Manipulação de erros não capturados
 */
process.on('unhandledRejection', (reason, promise) => {
    console.error('▲ Unhandled Rejection at:', promise, 'reason:', reason);
    if (client.logger) {
        client.logger.error('Unhandled Rejection:', reason);
    }
});

process.on('uncaughtException', (error) => {
    console.error('▲ Uncaught Exception:', error);
    if (client.logger) {
        client.logger.error('Uncaught Exception:', error);
    }
    process.exit(1);
});

/**
 * Manipulação de sinais de sistema
 */
process.on('SIGINT', async () => {
    console.log('\n▣ Recebido SIGINT. Encerrando graciosamente...');
    await gracefulShutdown();
});

process.on('SIGTERM', async () => {
    console.log('\n▣ Recebido SIGTERM. Encerrando graciosamente...');
    await gracefulShutdown();
});

/**
 * Encerramento gracioso
 */
async function gracefulShutdown() {
    try {
        if (client.logger) {
            client.logger.info('▣ Iniciando encerramento gracioso...');
        }

        // Fechar conexão com Discord
        if (client.isReady()) {
            await client.destroy();
            console.log('▣ Conexão com Discord encerrada');
        }

        // Fechar banco de dados
        if (client.database) {
            await client.database.close();
        }

        console.log('● Bot encerrado com sucesso');
        process.exit(0);
    } catch (error) {
        console.error('▲ Erro durante encerramento:', error);
        process.exit(1);
    }
}

/**
 * Informações de inicialização
 */
console.log('▣ Nodex | Moderação Bot');
console.log('▣ Versão: 2.0.0');
console.log('▣ 100% em Português Brasileiro');
console.log('▣ O Bot de Moderação Mais Avançado do Discord');
console.log('▣ Node.js:', process.version);
console.log('▣ Discord.js: v14');
console.log('▣ IA Integrada para Moderação Inteligente');
console.log('=====================================');

// Inicializar o bot
initializeBot();