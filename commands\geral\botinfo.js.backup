/**
 * ========================================
 * COMANDO: BOTINFO
 * Informações do Nodex | Moderação
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('botinfo')
        .setNameLocalizations({
            'pt-BR': 'botinfo'
        })
        .setDescription('🤖 Informações sobre o Nodex | Moderação')
        .setDescriptionLocalizations({
            'pt-BR': 'Mostra informações detalhadas sobre o bot de moderação mais avançado do Discord'
        }),

    category: 'geral',
    cooldown: 5,

    async execute(interaction) {
        try {
            const client = interaction.client;
            const guild = interaction.guild;

            // Calcular uptime
            const uptime = process.uptime();
            const days = Math.floor(uptime / 86400);
            const hours = Math.floor((uptime % 86400) / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = Math.floor(uptime % 60);

            const uptimeString = `${days}d ${hours}h ${minutes}m ${seconds}s`;

            // Informações de sistema
            const memoryUsage = process.memoryUsage();
            const memoryUsed = (memoryUsage.heapUsed / 1024 / 1024).toFixed(2);
            const memoryTotal = (memoryUsage.heapTotal / 1024 / 1024).toFixed(2);

            // Contar comandos
            const totalCommands = client.commands.size;
            const moderationCommands = client.commands.filter(cmd => 
                cmd.category === 'moderacao' || 
                ['ban', 'kick', 'timeout', 'warn', 'clear', 'unban'].includes(cmd.data.name)
            ).size;

            // Verificar sistemas ativos
            const systems = {
                autoMod: !!client.autoMod,
                aiMod: !!client.aiMod,
                antiRaid: !!client.antiRaid,
                tickets: !!client.tickets,
                analytics: !!client.analytics,
                backup: !!client.backup
            };

            const activeSystems = Object.values(systems).filter(Boolean).length;

            // Estatísticas de servidores
            const totalGuilds = client.guilds.cache.size;
            const totalUsers = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);

            // Criar embed principal
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('🛡️ Nodex | Moderação')
                .setDescription('**O Bot de Moderação Mais Avançado do Discord**\n\n🤖 IA Integrada • 🚫 Anti-Raid • 📊 Dashboard Profissional')
                .setThumbnail(client.user.displayAvatarURL({ size: 256 }))
                .addFields(
                    {
                        name: '📊 Estatísticas Gerais',
                        value: [
                            `**Servidores:** ${totalGuilds.toLocaleString('pt-BR')}`,
                            `**Usuários:** ${totalUsers.toLocaleString('pt-BR')}`,
                            `**Comandos:** ${totalCommands} (${moderationCommands} de moderação)`,
                            `**Ping:** ${client.ws.ping}ms`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🛡️ Sistemas de Moderação',
                        value: [
                            `${systems.autoMod ? '✅' : '❌'} **Auto-Moderação**`,
                            `${systems.aiMod ? '✅' : '❌'} **IA de Moderação**`,
                            `${systems.antiRaid ? '✅' : '❌'} **Anti-Raid**`,
                            `${systems.tickets ? '✅' : '❌'} **Sistema de Tickets**`,
                            `${systems.analytics ? '✅' : '❌'} **Analytics**`,
                            `${systems.backup ? '✅' : '❌'} **Sistema de Backup**`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '⚡ Performance',
                        value: [
                            `**Uptime:** ${uptimeString}`,
                            `**Memória:** ${memoryUsed}MB / ${memoryTotal}MB`,
                            `**Node.js:** ${process.version}`,
                            `**Discord.js:** v14`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🚀 Recursos Exclusivos',
                        value: [
                            '🧠 **IA Contextual** - Entende português brasileiro',
                            '🎯 **Moderação Progressiva** - Sistema de advertências',
                            '📈 **Dashboard Real-time** - Configuração web moderna',
                            '🔒 **Quarentena Inteligente** - Proteção contra raids',
                            '📊 **Logs Detalhados** - Histórico completo',
                            '⚙️ **Configuração Simples** - Setup em 2 minutos'
                        ].join('\n'),
                        inline: false
                    }
                )
                .setFooter({
                    text: `Versão 2.0.0 • Desenvolvido para comunidades brasileiras • Solicitado por ${interaction.user.username}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            // Adicionar campo específico do servidor se disponível
            if (guild) {
                let serverStatus = [];
                
                try {
                    const config = await client.database.getGuildConfig(guild.id);
                    if (config) {
                        serverStatus.push(`✅ **Configurado** - Bot ativo neste servidor`);
                        serverStatus.push(`🔧 **Auto-Mod:** ${config.auto_mod_enabled ? 'Ativo' : 'Inativo'}`);
                        serverStatus.push(`🛡️ **Anti-Raid:** ${config.anti_raid_enabled ? 'Ativo' : 'Inativo'}`);
                        
                        if (config.log_channel_id) {
                            serverStatus.push(`📝 **Logs:** Configurado`);
                        }
                    } else {
                        serverStatus.push(`⚠️ **Não configurado** - Use \`/dashboard\` para configurar`);
                    }
                } catch (error) {
                    serverStatus.push(`❓ **Status desconhecido**`);
                }

                embed.addFields({
                    name: `🏠 Status neste Servidor (${guild.name})`,
                    value: serverStatus.join('\n'),
                    inline: false
                });
            }

            // Criar botões
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('🌐 Dashboard')
                        .setStyle(ButtonStyle.Link)
                        .setURL(process.env.DASHBOARD_URL || 'http://localhost:3000'),
                    new ButtonBuilder()
                        .setLabel('📚 Documentação')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`${process.env.DASHBOARD_URL || 'http://localhost:3000'}/docs`),
                    new ButtonBuilder()
                        .setLabel('🎧 Suporte')
                        .setStyle(ButtonStyle.Link)
                        .setURL(`${process.env.DASHBOARD_URL || 'http://localhost:3000'}/support`)
                );

            await interaction.reply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error('Erro no comando botinfo:', error);
            
            const errorMessage = '❌ Ocorreu um erro ao buscar informações do bot!';
            
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
};
