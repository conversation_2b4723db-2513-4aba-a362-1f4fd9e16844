/**
 * ========================================
 * COMANDO: UNBAN (DESBANIR)
 * Remove o banimento de um usuário
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unban')
        .setNameLocalizations({
            'pt-BR': 'desbanir'
        })
        .setDescription('Remove o banimento de um usuário')
        .setDescriptionLocalizations({
            'pt-BR': 'Remove o banimento de um usuário do servidor'
        })
        .setDefaultMemberPermissions(4) // BAN_MEMBERS
        .addStringOption(option =>
            option.setName('usuario')
                .setNameLocalizations({ 'pt-BR': 'usuário' })
                .setDescription('ID do usuário para desbanir')
                .setDescriptionLocalizations({ 'pt-BR': 'ID do usuário para remover o banimento' })
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('motivo')
                .setDescription('Motivo do desbanimento')
                .setDescriptionLocalizations({ 'pt-BR': 'Motivo para remover o banimento' })
                .setRequired(false)
                .setMaxLength(500)
        ),
    
    category: 'moderacao',
    cooldown: 5,
    botPermissions: ['BanMembers'],
    
    async execute(interaction) {
        try {
            const userId = interaction.options.getString('usuario');
            const reason = interaction.options.getString('motivo') || 'Não especificado';

            // Validar ID do usuário
            if (!/^\d{17,19}$/.test(userId)) {
                return await interaction.reply({
                    content: 'ID de usuário inválido! Use um ID válido do Discord.',
                    ephemeral: true
                });
            }

            await interaction.deferReply();

            // Verificar se o usuário está banido
            const bans = await interaction.guild.bans.fetch();
            const bannedUser = bans.get(userId);

            if (!bannedUser) {
                return await interaction.editReply({
                    content: 'Este usuário não está banido do servidor!'
                });
            }

            // Buscar informações do usuário
            let user;
            try {
                user = await interaction.client.users.fetch(userId);
            } catch (error) {
                user = { tag: 'Usuário Desconhecido', id: userId };
            }

            // Remover banimento
            await interaction.guild.bans.remove(userId, `${interaction.user.tag}: ${reason}`);

            // Log da ação
            await interaction.client.database.logModerationAction(
                interaction.guild.id,
                userId,
                interaction.user.id,
                'unban',
                reason,
                null,
                null,
                null,
                {
                    user_tag: user.tag || 'Desconhecido',
                    automatic: false
                }
            );

            // Embed de confirmação
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('Usuário Desbanido')
                .setDescription(`**${user.tag || 'Usuário Desconhecido'}**foi desbanido com sucesso!`)
                .addFields(
                    {
                        name: 'Usuário',
                        value: `${user.tag || 'Desconhecido'}\n\`${userId}\``,
                        inline: true
                    },
                    {
                        name: 'Moderador',
                        value: interaction.user.toString(),
                        inline: true
                    },
                    {
                        name: 'Motivo',
                        value: reason,
                        inline: false
                    }
                )
                .setThumbnail(user.displayAvatarURL ? user.displayAvatarURL() : null)
                .setFooter({
                    text: 'Nodex | Moderação',
                    iconURL: interaction.client.user.displayAvatarURL()
                })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

            // Enviar log para canal de moderação se configurado
            const guildConfig = await interaction.client.database.getGuildConfig(interaction.guild.id);
            if (guildConfig?.mod_log_channel_id) {
                const logChannel = interaction.guild.channels.cache.get(guildConfig.mod_log_channel_id);
                if (logChannel) {
                    const logEmbed = new EmbedBuilder()
                        .setColor('#00ff7f')
                        .setTitle('Usuário Desbanido')
                        .addFields(
                            {
                                name: 'Usuário',
                                value: `${user.tag || 'Desconhecido'} (${userId})`,
                                inline: true
                            },
                            {
                                name: 'Moderador',
                                value: `${interaction.user.tag} (${interaction.user.id})`,
                                inline: true
                            },
                            {
                                name: 'Motivo',
                                value: reason,
                                inline: false
                            }
                        )
                        .setThumbnail(user.displayAvatarURL ? user.displayAvatarURL() : null)
                        .setFooter({
                            text: 'Sistema de Logs • Nodex | Moderação',
                            iconURL: interaction.client.user.displayAvatarURL()
                        })
                        .setTimestamp();

                    await logChannel.send({ embeds: [logEmbed] }).catch(() => {});
                }
            }

        } catch (error) {
            console.error('Erro no comando unban:', error);
            
            let errorMessage = 'Erro ao desbanir usuário!';
            
            if (error.code === 50013) {
                errorMessage = 'Não tenho permissão para desbanir usuários!';
            } else if (error.code === 10026) {
                errorMessage = 'Usuário não encontrado ou não está banido!';
            }

            if (interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    },
    
    // Função para comandos de texto
    executeText: async function(message, args) {
        // Converter para interaction-like object para reutilizar código
        const fakeInteraction = {
            guild: message.guild,
            member: message.member,
            user: message.author,
            channel: message.channel,
            client: message.client,
            options: {
                getUser: () => {
                    // Para unban, precisamos do ID do usuário
                    const userId = args[0];
                    if (!userId || !/^\d+$/.test(userId)) return null;
                    return { id: userId };
                },
                getString: () => {
                    return args.slice(1).join(' ') || 'Nenhum motivo fornecido';
                }
            },
            reply: async (content) => {
                if (typeof content === 'string') {
                    return await message.reply(content);
                }
                return await message.reply(content);
            },
            deferReply: async () => {
                return Promise.resolve();
            },
            editReply: async (content) => {
                return await message.reply(content);
            },
            deferred: false
        };

        // Verificar argumentos básicos
        if (args.length === 0) {
            return await message.reply(`**Uso:** \`!unban [ID do usuário] [motivo]\``);
        }

        // Chamar função execute principal
        return await this.execute(fakeInteraction);
    },};
