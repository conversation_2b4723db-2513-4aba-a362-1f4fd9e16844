/**
 * ========================================
 * COMANDO: KICK
 * Expulsa um usuário do servidor
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const EmbedStyles = require('../../utils/EmbedStyles');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('kick')
        .setDescription('Expulsar usuário do servidor')
        .addUserOption(option =>
            option.setName('usuário')
                .setDescription('Usuário a ser expulso')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('motivo')
                .setDescription('Motivo da expulsão')
                .setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.KickMembers)
        .setDMPermission(false),

    async execute(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const executor = interaction.member;
        const target = interaction.options.getMember('usuário');
        const reason = interaction.options.getString('motivo') || 'Nenhum motivo fornecido';

        try {
            // Verificações de segurança
            if (!target) {
                const errorData = {
                    title: 'Usuário Não Encontrado',
                    description: 'O usuário especificado não foi encontrado no servidor.\n\nVerifique se o usuário ainda está no servidor.'
                };
                const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (target.id === interaction.user.id) {
                const errorData = {
                    title: 'Ação Inválida',
                    description: 'Você não pode se expulsar.\n\nUse este comando em outros usuários.'
                };
                const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (target.id === client.user.id) {
                const errorData = {
                    title: 'Ação Inválida',
                    description: 'Não posso me expulsar.\n\nEsta operação não é permitida pelo sistema.'
                };
                const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Verificar hierarquia
            if (executor.roles.highest.position <= target.roles.highest.position) {
                const errorData = {
                    title: 'Hierarquia Insuficiente',
                    description: 'Você não pode expulsar este usuário.\n\nMotivo: Hierarquia de cargos\n\nO cargo do usuário é igual ou superior ao seu.'
                };
                const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (guild.members.me.roles.highest.position <= target.roles.highest.position) {
                const errorData = {
                    title: 'Hierarquia Insuficiente do Bot',
                    description: 'Não posso expulsar este usuário.\n\nMotivo: Hierarquia de cargos\n\nO cargo do usuário é igual ou superior ao meu.'
                };
                const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Verificar se o usuário pode ser expulso
            if (!target.kickable) {
                const errorData = {
                    title: 'Usuário Não Expulsável',
                    description: 'Este usuário não pode ser expulso.\n\nPossíveis motivos:\n\nProprietário do servidor\nPermissões especiais\nProteção do Discord'
                };
                const errorEmbed = embedStyles.createErrorEmbed(errorData.title, SVGIcons.formatProfessional(errorData.description));
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            let dmSent = false;

            // Tentar enviar DM para o usuário
            try {
                const dmData = {
                    title: 'Notificação de Expulsão',
                    description: `Você foi expulso do servidor ${guild.name}.\n\nEsta é uma notificação automática do sistema de moderação.`,
                    fields: [
                        {
                            name: 'Motivo da Expulsão',
                            value: reason,
                            inline: false
                        },
                        {
                            name: 'Moderador Responsável',
                            value: `${executor.user.tag}\n${executor.user.id}`,
                            inline: true
                        },
                        {
                            name: 'Servidor',
                            value: `${guild.name}\n${guild.id}`,
                            inline: true
                        },
                        {
                            name: 'Informações Importantes',
                            value: 'Você pode retornar ao servidor se tiver um convite.\n\nEntre em contato com a moderação se tiver dúvidas.\n\nRespeite as regras em futuras participações.',
                            inline: false
                        }
                    ]
                };

                const processedDmData = SVGIcons.createProfessionalEmbed(dmData);
                const dmEmbed = new EmbedBuilder()
                    .setColor(embedStyles.colors.warning)
                    .setTitle(processedDmData.title)
                    .setDescription(processedDmData.description)
                    .addFields(processedDmData.fields)
                    .setTimestamp()
                    .setFooter({ text: 'Nodex | Moderação', iconURL: guild.iconURL() })
                    .setThumbnail(guild.iconURL({ dynamic: true }));

                await target.send({ embeds: [dmEmbed] });
                dmSent = true;
            } catch (error) {
                // Usuário não pode receber DM
            }

            // Expulsar o usuário
            await target.kick(reason);

            // Resposta de sucesso
            const successData = {
                title: 'Usuário Expulso com Sucesso',
                description: 'O usuário foi expulso do servidor.\n\nTodas as configurações foram aplicadas conforme solicitado.',
                fields: [
                    {
                        name: 'Usuário Expulso',
                        value: `${target.user.tag}\n${target.user.id}`,
                        inline: true
                    },
                    {
                        name: 'Moderador',
                        value: `${executor.user.tag}\n${executor.user.id}`,
                        inline: true
                    },
                    {
                        name: 'Status da Notificação',
                        value: dmSent ? 'DM enviada com sucesso' : 'DM não pôde ser enviada',
                        inline: true
                    },
                    {
                        name: 'Motivo da Expulsão',
                        value: reason,
                        inline: false
                    }
                ]
            };

            const processedSuccessData = SVGIcons.createProfessionalEmbed(successData);
            const successEmbed = new EmbedBuilder()
                .setColor(embedStyles.colors.success)
                .setTitle(processedSuccessData.title)
                .setDescription(processedSuccessData.description)
                .addFields(processedSuccessData.fields)
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação', iconURL: interaction.client.user.displayAvatarURL() });

            await interaction.reply({ embeds: [successEmbed] });

            // Log no banco de dados
            client.database.logModerationAction(
                guild.id,
                target.id,
                executor.id,
                'kick',
                reason,
                null,
                null,
                null,
                { automatic: false }
            );

            // Log no canal de logs se configurado
            const guildConfig = client.database.getGuildConfig(guild.id);
            if (guildConfig?.log_channel) {
                const logChannel = guild.channels.cache.get(guildConfig.log_channel);
                if (logChannel) {
                    const logEmbed = new EmbedBuilder()
                        .setColor('#ff6b35')
                        .setTitle('Usuário Expulso')
                        .addFields(
                            { name: 'Usuário', value: `${target.user.tag} (${target.id})`, inline: true },
                            { name: 'Moderador', value: `${executor.user.tag} (${executor.id})`, inline: true },
                            { name: 'Motivo', value: reason, inline: false },
                            { name: 'Data', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                        )
                        .setThumbnail(target.user.displayAvatarURL())
                        .setTimestamp()
                        .setFooter({ text: 'Nodex | Moderação' });

                    await logChannel.send({ embeds: [logEmbed] });
                }
            }

            client.logger.info(`Usuário ${target.user.tag} expulso por ${executor.user.tag}: ${reason}`);

        } catch (error) {
            client.logger.error('Erro no comando kick:', error);

            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                `${embedStyles.format.bold('Ocorreu um erro ao expulsar o usuário.')}\n\n${embedStyles.icons.info}**Detalhes:**\n• Verifique as permissões do bot\n• Tente novamente em alguns segundos\n• Contate o suporte se o problema persistir`
            );

            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },

    // Função para comandos de texto
    async executeText(message, args) {
        const embedStyles = new EmbedStyles();
        const client = message.client;
        const guild = message.guild;
        const executor = message.member;

        // Verificar se há argumentos suficientes
        if (args.length < 1) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Uso Incorreto',
                `${embedStyles.format.bold('Comando usado incorretamente!')}\n\n${embedStyles.icons.info}**Uso correto:**\n${embedStyles.format.code('!kick @usuário [motivo]')}\n\n${embedStyles.format.italic('Exemplo: !kick @usuário Comportamento inadequado')}`
            );
            return await message.reply({ embeds: [errorEmbed] });
        }

        // Extrair usuário e motivo
        const userMention = args[0];
        const reason = args.slice(1).join('') || 'Nenhum motivo fornecido';

        // Tentar encontrar o usuário
        let target;

        // Se for uma menção
        if (userMention.startsWith('<@') && userMention.endsWith('>')) {
            const userId = userMention.slice(2, -1).replace('!', '');
            target = guild.members.cache.get(userId);
        }
        // Se for um ID
        else if (/^\d+$/.test(userMention)) {
            target = guild.members.cache.get(userMention);
        }
        // Se for um nome de usuário
        else {
            target = guild.members.cache.find(member =>
                member.user.username.toLowerCase().includes(userMention.toLowerCase()) ||
                member.displayName.toLowerCase().includes(userMention.toLowerCase())
            );
        }

        if (!target) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Usuário Não Encontrado',
                `${embedStyles.format.bold('O usuário especificado não foi encontrado!')}\n\n${embedStyles.icons.info}**Uso correto:**\n${embedStyles.format.code('!kick @usuário [motivo]')}`
            );
            return await message.reply({ embeds: [errorEmbed] });
        }

        // Verificações de segurança com embeds premium
        if (target.id === message.author.id) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Ação Inválida',
                `${embedStyles.format.bold('Você não pode se expulsar!')}\n\n${embedStyles.format.italic('Use este comando em outros usuários.')}`
            );
            return await message.reply({ embeds: [errorEmbed] });
        }

        if (target.id === client.user.id) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Ação Inválida',
                `${embedStyles.format.bold('Não posso me expulsar!')}\n\n${embedStyles.format.italic('Isso seria contraproducente, não acha?')}`
            );
            return await message.reply({ embeds: [errorEmbed] });
        }

        // Verificar permissões
        if (!executor.permissions.has('KickMembers')) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Permissão Insuficiente',
                `${embedStyles.format.bold('Você não tem permissão para expulsar usuários!')}\n\n${embedStyles.icons.shield}**Permissão necessária:**${embedStyles.format.code('Expulsar Membros')}`
            );
            return await message.reply({ embeds: [errorEmbed] });
        }

        // Verificar hierarquia
        if (executor.roles.highest.position <= target.roles.highest.position) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Hierarquia Insuficiente',
                `${embedStyles.format.bold('Você não pode expulsar este usuário!')}\n\n${embedStyles.icons.crown}**Motivo:**Hierarquia de cargos`
            );
            return await message.reply({ embeds: [errorEmbed] });
        }

        if (guild.members.me.roles.highest.position <= target.roles.highest.position) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Hierarquia Insuficiente do Bot',
                `${embedStyles.format.bold('Não posso expulsar este usuário!')}\n\n${embedStyles.icons.crown}**Motivo:**Hierarquia de cargos`
            );
            return await message.reply({ embeds: [errorEmbed] });
        }

        if (!target.kickable) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Usuário Não Expulsável',
                `${embedStyles.format.bold('Este usuário não pode ser expulso!')}\n\n${embedStyles.icons.shield}**Possíveis motivos:**\n• Proprietário do servidor\n• Permissões especiais`
            );
            return await message.reply({ embeds: [errorEmbed] });
        }

        try {
            // Tentar enviar DM para o usuário
            try {
                const { EmbedBuilder } = require('discord.js');
                const dmEmbed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('Você foi expulso!')
                    .setDescription(`Você foi expulso do servidor**${guild.name}**`)
                    .addFields(
                        { name: 'Motivo', value: reason, inline: false },
                        { name: 'Moderador', value: executor.user.tag, inline: true },
                        { name: 'Data', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                    )
                    .setThumbnail(guild.iconURL())
                    .setTimestamp()
                    .setFooter({ text: 'Nodex | Moderação' });

                await target.send({ embeds: [dmEmbed] });
            } catch (error) {
                // Usuário não pode receber DM
            }

            // Expulsar o usuário
            await target.kick(reason);

            // Resposta de sucesso premium
            const successEmbed = embedStyles.createSuccessEmbed(
                'Usuário Expulso com Sucesso',
                `**${embedStyles.format.bold(target.user.tag)}**foi expulso do servidor!\n\n${embedStyles.icons.info}**Motivo:**${embedStyles.format.code(reason)}\n${embedStyles.icons.shield}**Moderador:**${executor.user.tag}`
            );
            await message.reply({ embeds: [successEmbed] });

            // Log no banco de dados
            client.database.logModerationAction(
                guild.id, target.id, executor.id, 'kick', reason, null, null, null,
                { automatic: false }
            );

            // Log no canal de logs se configurado
            await this.logToModerationChannel(guild, {
                action: 'kick',
                target: target.user,
                moderator: executor.user,
                reason: reason
            });

            client.logger.info(`Usuário ${target.user.tag} expulso por ${executor.user.tag}: ${reason}`);

        } catch (error) {
            client.logger.error('Erro no comando kick (texto):', error);

            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                `${embedStyles.format.bold('Ocorreu um erro ao expulsar o usuário.')}\n\n${embedStyles.icons.info}**Detalhes:**\n• Verifique as permissões do bot\n• Tente novamente em alguns segundos`
            );
            await message.reply({ embeds: [errorEmbed] });
        }
    },

    /**
     * Envia log para o canal de moderação
     */
    async logToModerationChannel(guild, data) {
        try {
            // Obter configuração do servidor
            const guildConfig = await guild.client.database.getGuildConfig(guild.id);
            if (!guildConfig) return;

            // Parse das configurações
            let settings = {};
            try {
                if (typeof guildConfig.settings === 'string') {
                    settings = JSON.parse(guildConfig.settings);
                } else if (typeof guildConfig.settings === 'object') {
                    settings = guildConfig.settings;
                }
            } catch (error) {
                console.error('Erro ao fazer parse das configurações de log:', error);
                return;
            }

            // Verificar se logs de moderação estão habilitados
            const logEnabled = settings.log_enabled || settings.log_message_delete || settings.log_moderation_actions || settings.track_moderation_actions;
            if (!logEnabled) {
                console.log(`[LOG] Logs de moderação desabilitados para ${guild.name}`);
                console.log(`[LOG] Settings:`, {
                    log_enabled: settings.log_enabled,
                    log_moderation_actions: settings.log_moderation_actions,
                    track_moderation_actions: settings.track_moderation_actions
                });
                return;
            }

            // Obter canal de logs (tentar várias opções)
            const logChannelId = settings.general_log_channel ||
                                settings.log_channel ||
                                settings.message_log_channel ||
                                guildConfig.log_channel_id ||
                                guildConfig.log_channel;

            console.log(`[LOG] Tentando encontrar canal de logs para ${guild.name}:`, {
                general_log_channel: settings.general_log_channel,
                log_channel: settings.log_channel,
                message_log_channel: settings.message_log_channel,
                log_channel_id: guildConfig.log_channel_id,
                final_channel: logChannelId
            });

            if (!logChannelId) {
                console.log(`[LOG] Canal de logs não configurado para ${guild.name}`);
                return;
            }

            const logChannel = guild.channels.cache.get(logChannelId);
            if (!logChannel || !logChannel.isTextBased()) {
                console.log(`[LOG] Canal de logs não encontrado: ${logChannelId}`);
                return;
            }

            // Verificar permissões
            const permissions = logChannel.permissionsFor(guild.members.me);
            if (!permissions.has(['SendMessages', 'EmbedLinks'])) {
                console.log(`[LOG] Sem permissões no canal de logs: ${logChannel.name}`);
                return;
            }

            console.log(`[LOG] Enviando log de ${data.action} para #${logChannel.name} em ${guild.name}`);

            // Criar embed de log premium
            const { EmbedBuilder } = require('discord.js');
            const embedStyles = new EmbedStyles();

            const logEmbed = {
                color: parseInt(embedStyles.colors.warning.replace('#', ''), 16),
                title: `${embedStyles.icons.kick} ${embedStyles.format.bold('Usuário Expulso')}`,
                description: `**Ação de moderação executada no servidor**\n${embedStyles.format.italic('Log automático do sistema de moderação')}`,
                fields: [
                    {
                        name: `${embedStyles.icons.user}**Usuário Afetado**`,
                        value: `${data.target.tag}\n${embedStyles.format.code(data.target.id)}`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.shield}**Moderador**`,
                        value: `${data.moderator.tag}\n${embedStyles.format.code(data.moderator.id)}`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.info}**Status da DM**`,
                        value: data.dmSent ? `${embedStyles.icons.success} Enviada` : `${embedStyles.icons.error} Não enviada`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.info}**Motivo da Expulsão**`,
                        value: embedStyles.format.code(data.reason),
                        inline: false
                    }
                ],
                timestamp: new Date().toISOString(),
                footer: {
                    text: 'Nodex | Moderação • Sistema de Logs Premium',
                    icon_url: guild.iconURL()
                },
                thumbnail: {
                    url: data.target.displayAvatarURL({ dynamic: true })
                }
            };

            await logChannel.send({ embeds: [logEmbed] });
            console.log(`[LOG] Log de expulsão enviado para ${data.target.tag}`);

        } catch (error) {
            guild.client.logger.error('Erro ao enviar log de expulsão:', error);
        }
    },

    cooldown: 3,
    category: 'moderacao',
    examples: [
        '/kick @usuário Spam excessivo',
        '/kick @usuário',
        '/kick usuário:123456789 Comportamento inadequado',
        '!kick @usuário Comportamento inadequado',
        '!kick 123456789 Spam'
    ]
};
