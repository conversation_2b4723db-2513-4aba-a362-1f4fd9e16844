/**
 * ========================================
 * COMANDO: TIMEOUT
 * Coloca um usuário em timeout
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('timeout')
        .setDescription('Coloca um usuário em timeout')
        .addUserOption(option =>
            option.setName('usuário')
                .setDescription('Usuário a ser colocado em timeout')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('tempo')
                .setDescription('Duração do timeout')
                .setRequired(true)
                .addChoices(
                    { name: '1 minuto', value: '1m' },
                    { name: '5 minutos', value: '5m' },
                    { name: '10 minutos', value: '10m' },
                    { name: '30 minutos', value: '30m' },
                    { name: '1 hora', value: '1h' },
                    { name: '6 horas', value: '6h' },
                    { name: '12 horas', value: '12h' },
                    { name: '1 dia', value: '1d' },
                    { name: '1 semana', value: '7d' }
                ))
        .addStringOption(option =>
            option.setName('motivo')
                .setDescription('Motivo do timeout')
                .setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .setDMPermission(false),

    async execute(interaction) {
        const client = interaction.client;
        const guild = interaction.guild;
        const executor = interaction.member;
        const target = interaction.options.getMember('usuário');
        const timeString = interaction.options.getString('tempo');
        const reason = interaction.options.getString('motivo') || 'Nenhum motivo fornecido';

        try {
            // Verificações de segurança
            if (!target) {
                return await interaction.reply({
                    content: 'Usuário não encontrado no servidor!',
                    ephemeral: true
                });
            }

            if (target.id === interaction.user.id) {
                return await interaction.reply({
                    content: 'Você não pode se colocar em timeout!',
                    ephemeral: true
                });
            }

            if (target.id === client.user.id) {
                return await interaction.reply({
                    content: 'Não posso me colocar em timeout!',
                    ephemeral: true
                });
            }

            // Verificar hierarquia
            if (executor.roles.highest.position <= target.roles.highest.position) {
                return await interaction.reply({
                    content: 'Você não pode colocar este usuário em timeout! (Hierarquia de cargos)',
                    ephemeral: true
                });
            }

            if (guild.members.me.roles.highest.position <= target.roles.highest.position) {
                return await interaction.reply({
                    content: 'Não posso colocar este usuário em timeout! (Hierarquia de cargos)',
                    ephemeral: true
                });
            }

            // Verificar se o usuário pode ser colocado em timeout
            if (!target.moderatable) {
                return await interaction.reply({
                    content: 'Este usuário não pode ser colocado em timeout!',
                    ephemeral: true
                });
            }

            // Converter tempo para milissegundos
            const duration = parseTimeString(timeString);
            if (!duration) {
                return await interaction.reply({
                    content: 'Formato de tempo inválido!',
                    ephemeral: true
                });
            }

            // Verificar se já está em timeout
            if (target.communicationDisabledUntil && target.communicationDisabledUntil > Date.now()) {
                return await interaction.reply({
                    content: 'Este usuário já está em timeout!',
                    ephemeral: true
                });
            }

            // Tentar enviar DM para o usuário
            try {
                const dmEmbed = new EmbedBuilder()
                    .setColor('#ffa500')
                    .setTitle('Você foi colocado em timeout!')
                    .setDescription(`Você foi colocado em timeout no servidor**${guild.name}**`)
                    .addFields(
                        { name: 'Motivo', value: reason, inline: false },
                        { name: '⏱️ Duração', value: formatDuration(duration), inline: true },
                        { name: 'Moderador', value: executor.user.tag, inline: true },
                        { name: 'Expira em', value: `<t:${Math.floor((Date.now() + duration) / 1000)}:F>`, inline: false }
                    )
                    .setThumbnail(guild.iconURL())
                    .setTimestamp()
                    .setFooter({ text: 'Nodex | Moderação' });

                await target.send({ embeds: [dmEmbed] });
            } catch (error) {
                // Usuário não pode receber DM
            }

            // Aplicar timeout
            await target.timeout(duration, reason);

            // Embed de confirmação
            const successEmbed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle('Usuário em Timeout')
                .setDescription(`**${target.user.tag}**foi colocado em timeout`)
                .addFields(
                    { name: 'Usuário', value: `${target.user.tag} (${target.id})`, inline: true },
                    { name: 'Moderador', value: executor.user.tag, inline: true },
                    { name: '⏱️ Duração', value: formatDuration(duration), inline: true },
                    { name: 'Motivo', value: reason, inline: false },
                    { name: 'Expira em', value: `<t:${Math.floor((Date.now() + duration) / 1000)}:F>`, inline: false }
                )
                .setThumbnail(target.user.displayAvatarURL())
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação' });

            await interaction.reply({ embeds: [successEmbed] });

            // Log no banco de dados
            client.database.logModerationAction(
                guild.id,
                target.id,
                executor.id,
                'timeout',
                reason,
                duration,
                null,
                null,
                { automatic: false }
            );

            // Log no canal de logs se configurado
            const guildConfig = client.database.getGuildConfig(guild.id);
            if (guildConfig?.log_channel) {
                const logChannel = guild.channels.cache.get(guildConfig.log_channel);
                if (logChannel) {
                    const logEmbed = new EmbedBuilder()
                        .setColor('#ffa500')
                        .setTitle('Usuário em Timeout')
                        .addFields(
                            { name: 'Usuário', value: `${target.user.tag} (${target.id})`, inline: true },
                            { name: 'Moderador', value: `${executor.user.tag} (${executor.id})`, inline: true },
                            { name: '⏱️ Duração', value: formatDuration(duration), inline: true },
                            { name: 'Motivo', value: reason, inline: false },
                            { name: 'Expira em', value: `<t:${Math.floor((Date.now() + duration) / 1000)}:F>`, inline: false }
                        )
                        .setThumbnail(target.user.displayAvatarURL())
                        .setTimestamp()
                        .setFooter({ text: 'Nodex | Moderação' });

                    await logChannel.send({ embeds: [logEmbed] });
                }
            }

            client.logger.info(`Usuário ${target.user.tag} colocado em timeout por ${executor.user.tag}: ${reason} (${formatDuration(duration)})`);

        } catch (error) {
            client.logger.error('Erro no comando timeout:', error);

            await interaction.reply({
                content: 'Ocorreu um erro ao colocar o usuário em timeout.',
                ephemeral: true
            });
        }
    },

    // Função para comandos de texto
    async executeText(message, args) {
        const client = message.client;
        const guild = message.guild;
        const executor = message.member;

        // Verificar se há argumentos suficientes
        if (args.length < 2) {
            return await message.reply('**Uso correto:**`!timeout @usuário <tempo> [motivo]`\n**Tempos válidos:**1m, 5m, 10m, 30m, 1h, 6h, 12h, 1d, 7d');
        }

        // Extrair usuário, tempo e motivo
        const userMention = args[0];
        const timeStr = args[1];
        const reason = args.slice(2).join('') || 'Nenhum motivo fornecido';

        // Verificar se o tempo é válido
        const duration = parseTimeString(timeStr);
        if (!duration) {
            return await message.reply('**Tempo inválido!**Use: 1m, 5m, 10m, 30m, 1h, 6h, 12h, 1d, 7d');
        }

        // Tentar encontrar o usuário
        let target;

        // Se for uma menção
        if (userMention.startsWith('<@') && userMention.endsWith('>')) {
            const userId = userMention.slice(2, -1).replace('!', '');
            target = guild.members.cache.get(userId);
        }
        // Se for um ID
        else if (/^\d+$/.test(userMention)) {
            target = guild.members.cache.get(userMention);
        }
        // Se for um nome de usuário
        else {
            target = guild.members.cache.find(member =>
                member.user.username.toLowerCase().includes(userMention.toLowerCase()) ||
                member.displayName.toLowerCase().includes(userMention.toLowerCase())
            );
        }

        if (!target) {
            return await message.reply('Usuário não encontrado! Use `!timeout @usuário <tempo> [motivo]`');
        }

        // Verificações de segurança
        if (target.id === message.author.id) {
            return await message.reply('Você não pode se colocar em timeout!');
        }

        if (target.id === client.user.id) {
            return await message.reply('Não posso me colocar em timeout!');
        }

        // Verificar permissões
        if (!executor.permissions.has('ModerateMembers')) {
            return await message.reply('Você não tem permissão para colocar usuários em timeout!');
        }

        // Verificar hierarquia
        if (executor.roles.highest.position <= target.roles.highest.position) {
            return await message.reply('Você não pode colocar este usuário em timeout! (Hierarquia de cargos)');
        }

        if (guild.members.me.roles.highest.position <= target.roles.highest.position) {
            return await message.reply('Não posso colocar este usuário em timeout! (Hierarquia de cargos)');
        }

        if (!target.moderatable) {
            return await message.reply('Este usuário não pode ser colocado em timeout!');
        }

        try {
            // Tentar enviar DM para o usuário
            try {
                const { EmbedBuilder } = require('discord.js');
                const dmEmbed = new EmbedBuilder()
                    .setColor('#ffa500')
                    .setTitle('Você foi colocado em timeout!')
                    .setDescription(`Você foi colocado em timeout no servidor**${guild.name}**`)
                    .addFields(
                        { name: 'Motivo', value: reason, inline: false },
                        { name: '⏱️ Duração', value: formatDuration(duration), inline: true },
                        { name: 'Moderador', value: executor.user.tag, inline: true },
                        { name: 'Expira em', value: `<t:${Math.floor((Date.now() + duration) / 1000)}:F>`, inline: false }
                    )
                    .setThumbnail(guild.iconURL())
                    .setTimestamp()
                    .setFooter({ text: 'Nodex | Moderação' });

                await target.send({ embeds: [dmEmbed] });
            } catch (error) {
                // Usuário não pode receber DM
            }

            // Aplicar timeout
            await target.timeout(duration, reason);

            // Resposta de sucesso
            await message.reply(`**${target.user.tag}**foi colocado em timeout!\n**Motivo:**${reason}\n⏱️**Duração:**${formatDuration(duration)}\n**Expira em:**<t:${Math.floor((Date.now() + duration) / 1000)}:F>`);

            // Log no banco de dados
            client.database.logModerationAction(
                guild.id, target.id, executor.id, 'timeout', reason, duration, null, null,
                { automatic: false }
            );

            // Log no canal de logs se configurado
            await this.logToModerationChannel(guild, {
                action: 'timeout',
                target: target.user,
                moderator: executor.user,
                reason: reason,
                duration: duration
            });

            client.logger.info(`Usuário ${target.user.tag} colocado em timeout por ${executor.user.tag}: ${reason} (${formatDuration(duration)})`);

        } catch (error) {
            client.logger.error('Erro no comando timeout (texto):', error);
            await message.reply('Ocorreu um erro ao colocar o usuário em timeout.');
        }
    },

    /**
     * Envia log para o canal de moderação
     */
    async logToModerationChannel(guild, data) {
        try {
            // Obter configuração do servidor
            const guildConfig = await guild.client.database.getGuildConfig(guild.id);
            if (!guildConfig) return;

            // Parse das configurações
            let settings = {};
            try {
                if (typeof guildConfig.settings === 'string') {
                    settings = JSON.parse(guildConfig.settings);
                } else if (typeof guildConfig.settings === 'object') {
                    settings = guildConfig.settings;
                }
            } catch (error) {
                console.error('Erro ao fazer parse das configurações de log:', error);
                return;
            }

            // Verificar se logs de moderação estão habilitados
            const logEnabled = settings.log_enabled || settings.log_message_delete || settings.log_moderation_actions || settings.track_moderation_actions;
            if (!logEnabled) {
                console.log(`[LOG] Logs de moderação desabilitados para ${guild.name}`);
                return;
            }

            // Obter canal de logs (tentar várias opções)
            const logChannelId = settings.general_log_channel ||
                                settings.log_channel ||
                                settings.message_log_channel ||
                                guildConfig.log_channel_id ||
                                guildConfig.log_channel;

            console.log(`[LOG] Tentando encontrar canal de logs para timeout em ${guild.name}: ${logChannelId}`);

            if (!logChannelId) {
                console.log(`[LOG] Canal de logs não configurado para ${guild.name}`);
                return;
            }

            const logChannel = guild.channels.cache.get(logChannelId);
            if (!logChannel || !logChannel.isTextBased()) {
                console.log(`[LOG] Canal de logs não encontrado: ${logChannelId}`);
                return;
            }

            // Verificar permissões
            const permissions = logChannel.permissionsFor(guild.members.me);
            if (!permissions.has(['SendMessages', 'EmbedLinks'])) {
                console.log(`[LOG] Sem permissões no canal de logs: ${logChannel.name}`);
                return;
            }

            console.log(`[LOG] Enviando log de ${data.action} para #${logChannel.name} em ${guild.name}`);

            // Criar embed de log
            const { EmbedBuilder } = require('discord.js');
            const logEmbed = new EmbedBuilder()
                .setColor('#ffa500')
                .setTitle('Usuário em Timeout')
                .addFields(
                    { name: 'Usuário', value: `${data.target.tag} (${data.target.id})`, inline: true },
                    { name: 'Moderador', value: `${data.moderator.tag} (${data.moderator.id})`, inline: true },
                    { name: 'Motivo', value: data.reason, inline: false },
                    { name: '⏱️ Duração', value: formatDuration(data.duration), inline: true },
                    { name: 'Expira em', value: `<t:${Math.floor((Date.now() + data.duration) / 1000)}:F>`, inline: true }
                )
                .setThumbnail(data.target.displayAvatarURL())
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação' });

            await logChannel.send({ embeds: [logEmbed] });
            console.log(`[LOG] Log de timeout enviado para ${data.target.tag}`);

        } catch (error) {
            guild.client.logger.error('Erro ao enviar log de timeout:', error);
        }
    },

    cooldown: 3,
    category: 'moderacao',
    examples: [
        '/timeout @usuário 1h Spam excessivo',
        '/timeout @usuário 30m',
        '/timeout usuário:123456789 1d Comportamento inadequado',
        '!timeout @usuário 1h Comportamento inadequado',
        '!timeout 123456789 30m Spam'
    ]
};

/**
 * Converte string de tempo para milissegundos
 */
function parseTimeString(timeStr) {
    const timeMap = {
        '1m': 1 * 60 * 1000,
        '5m': 5 * 60 * 1000,
        '10m': 10 * 60 * 1000,
        '30m': 30 * 60 * 1000,
        '1h': 1 * 60 * 60 * 1000,
        '6h': 6 * 60 * 60 * 1000,
        '12h': 12 * 60 * 60 * 1000,
        '1d': 1 * 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000
    };

    return timeMap[timeStr] || null;
}

/**
 * Formata duração em milissegundos para string legível
 */
function formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} dia(s)`;
    if (hours > 0) return `${hours} hora(s)`;
    if (minutes > 0) return `${minutes} minuto(s)`;
    return `${seconds} segundo(s)`;
}
