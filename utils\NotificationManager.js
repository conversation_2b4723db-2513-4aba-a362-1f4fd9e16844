/**
 * ========================================
 * GERENCIADOR DE NOTIFICAÇÕES
 * Sistema de notificações do Discord e Dashboard
 * ========================================
 */

const { EmbedBuilder } = require('discord.js');

class NotificationManager {
    constructor(client) {
        this.client = client;
    }

    /**
     * Envia notificação de atualização de módulo
     */
    async sendModuleUpdate(guild, moduleName, settings, userId) {
        try {
            // Buscar canal de logs
            const guildConfig = await this.client.database.getGuildConfig(guild.id);
            if (!guildConfig?.log_channel_id) {
                console.log(`⚠️ [NOTIFICATION] Canal de logs não configurado para guild ${guild.id}`);
                return;
            }

            const logChannel = await guild.channels.fetch(guildConfig.log_channel_id);
            if (!logChannel) {
                console.log(`⚠️ [NOTIFICATION] Canal de logs não encontrado: ${guildConfig.log_channel_id}`);
                return;
            }

            // Criar embed base
            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle(`⚙️ ${moduleName} Atualizado`)
                .setDescription('As configurações foram atualizadas com sucesso!')
                .addFields(
                    { name: 'Status', value: settings.enabled ? '✅ Ativado' : '❌ Desativado', inline: true },
                    { name: 'Atualizado por', value: `<@${userId}>`, inline: true },
                    { name: 'Data', value: new Date().toLocaleString('pt-BR'), inline: true },
                    { name: 'Origem', value: '🌐 Dashboard Web', inline: true }
                )
                .setTimestamp();

            // Adicionar campos específicos por módulo
            switch (moduleName.toLowerCase()) {
                case 'verificação':
                    embed.addFields(
                        { name: 'Método', value: this.getVerificationMethod(settings.method), inline: true },
                        { name: 'Canal', value: `<#${settings.channelId}>`, inline: true }
                    );
                    break;

                case 'moderação por ia':
                    embed.addFields(
                        { name: 'Nível', value: settings.level || 'Padrão', inline: true },
                        { name: 'Filtros Ativos', value: this.getActiveFilters(settings.filters), inline: true }
                    );
                    break;

                case 'anti-raid':
                    embed.addFields(
                        { name: 'Limite de Joins', value: String(settings.maxJoinsPerMinute) || 'Padrão', inline: true },
                        { name: 'Idade Mínima', value: `${settings.minAccountAge || 0} dias`, inline: true }
                    );
                    break;

                // Adicionar outros módulos conforme necessário
            }

            // Enviar mensagem
            await logChannel.send({ embeds: [embed] });

            // Se for módulo de verificação e estiver ativado, enviar mensagem de verificação
            if (moduleName.toLowerCase() === 'verificação' && settings.enabled && settings.channelId) {
                const verificationChannel = await guild.channels.fetch(settings.channelId);
                if (verificationChannel) {
                    const verificationEmbed = await this.createVerificationEmbed(settings);
                    await verificationChannel.send(verificationEmbed);
                }
            }

            return true;
        } catch (error) {
            console.error(`❌ Erro ao enviar notificação de atualização do módulo ${moduleName}:`, error);
            return false;
        }
    }

    /**
     * Cria embed de verificação
     */
    async createVerificationEmbed(settings) {
        const embed = new EmbedBuilder()
            .setColor('#00ff7f')
            .setTitle('Sistema de Verificação')
            .setDescription(settings.welcomeMessage || 'Bem-vindo! Por favor, verifique-se para acessar o servidor.')
            .addFields(
                { name: 'Como se verificar:', value: this.getVerificationInstructions(settings.method) }
            )
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('verify')
                    .setLabel('Verificar')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('✅')
            );

        return { embeds: [embed], components: [row] };
    }

    /**
     * Retorna instruções de verificação baseado no método
     */
    getVerificationInstructions(method) {
        const instructions = {
            reaction: '🎯 Clique no botão abaixo para se verificar.',
            captcha: '🧮 Clique no botão e resolva o captcha para se verificar.',
            manual: '👥 Aguarde um moderador verificar você.',
            combined: '🔗 Clique no botão e complete todas as etapas de verificação.'
        };
        return instructions[method] || 'Siga as instruções para se verificar.';
    }

    /**
     * Retorna nome amigável do método de verificação
     */
    getVerificationMethod(method) {
        const methods = {
            reaction: '🎯 Reação',
            captcha: '🧮 Captcha',
            manual: '👥 Manual',
            combined: '🔗 Combinado'
        };
        return methods[method] || 'Desconhecido';
    }

    /**
     * Retorna lista de filtros ativos formatada
     */
    getActiveFilters(filters) {
        if (!filters || Object.keys(filters).length === 0) {
            return 'Nenhum filtro ativo';
        }

        return Object.entries(filters)
            .filter(([, enabled]) => enabled)
            .map(([name]) => {
                const filterNames = {
                    spam: '🚫 Anti-Spam',
                    profanity: '🤬 Palavrões',
                    links: '🔗 Links',
                    mentions: '@️ Menções',
                    caps: '🔠 CAPS',
                    flood: '📝 Flood'
                };
                return filterNames[name] || name;
            })
            .join('\n');
    }

    /**
     * Envia notificação de configuração salva via dashboard
     */
    async sendDashboardConfigUpdate(guild, section, configData, userId) {
        try {
            const guildConfig = await this.client.database.getGuildConfig(guild.id);
            if (!guildConfig?.log_channel_id) return;

            const logChannel = await guild.channels.fetch(guildConfig.log_channel_id);
            if (!logChannel) return;

            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle(`🌐 Configuração Atualizada via Dashboard`)
                .setDescription(`A seção **${section}** foi atualizada através do dashboard web.`)
                .addFields(
                    { name: 'Seção', value: this.getSectionDisplayName(section), inline: true },
                    { name: 'Atualizado por', value: `<@${userId}>`, inline: true },
                    { name: 'Data/Hora', value: new Date().toLocaleString('pt-BR'), inline: true }
                )
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação - Dashboard Web' });

            // Adicionar campos específicos baseados na seção
            this.addSectionSpecificFields(embed, section, configData);

            await logChannel.send({ embeds: [embed] });
            console.log(`✅ [NOTIFICATION] Notificação de dashboard enviada para ${guild.name}`);

        } catch (error) {
            console.error(`❌ [NOTIFICATION] Erro ao enviar notificação de dashboard:`, error);
        }
    }

    /**
     * Envia notificação de backup criado/restaurado
     */
    async sendBackupNotification(guild, action, backupData, userId) {
        try {
            const guildConfig = await this.client.database.getGuildConfig(guild.id);
            if (!guildConfig?.log_channel_id) return;

            const logChannel = await guild.channels.fetch(guildConfig.log_channel_id);
            if (!logChannel) return;

            const actionEmojis = {
                created: '💾',
                restored: '🔄',
                deleted: '🗑️'
            };

            const actionNames = {
                created: 'Backup Criado',
                restored: 'Backup Restaurado',
                deleted: 'Backup Deletado'
            };

            const embed = new EmbedBuilder()
                .setColor(action === 'deleted' ? '#ff4757' : '#00ff7f')
                .setTitle(`${actionEmojis[action]} ${actionNames[action]}`)
                .setDescription(`Um backup foi ${action === 'created' ? 'criado' : action === 'restored' ? 'restaurado' : 'deletado'} via dashboard web.`)
                .addFields(
                    { name: 'Executado por', value: `<@${userId}>`, inline: true },
                    { name: 'Data/Hora', value: new Date().toLocaleString('pt-BR'), inline: true }
                )
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação - Sistema de Backup' });

            if (backupData) {
                if (action === 'created') {
                    embed.addFields(
                        { name: 'ID do Backup', value: `\`${backupData.id}\``, inline: false },
                        { name: 'Tipo', value: backupData.type === 'full' ? '🔄 Completo' : '📋 Seletivo', inline: true }
                    );
                } else if (action === 'restored') {
                    embed.addFields(
                        { name: 'Backup Restaurado', value: `\`${backupData.backupId}\``, inline: false }
                    );
                }
            }

            await logChannel.send({ embeds: [embed] });
            console.log(`✅ [NOTIFICATION] Notificação de backup (${action}) enviada para ${guild.name}`);

        } catch (error) {
            console.error(`❌ [NOTIFICATION] Erro ao enviar notificação de backup:`, error);
        }
    }

    /**
     * Retorna nome amigável da seção
     */
    getSectionDisplayName(section) {
        const sectionNames = {
            geral: '⚙️ Configurações Gerais',
            moderacao: '🛡️ Moderação',
            ai: '🧠 IA & Auto-Moderação',
            antiraid: '🛡️ Anti-Raid',
            logs: '📝 Logs & Auditoria',
            comandos: '💻 Comandos',
            verificacao: '✅ Verificação',
            analytics: '📊 Analytics',
            backup: '💾 Backup & Restore'
        };
        return sectionNames[section] || section;
    }

    /**
     * Adiciona campos específicos baseados na seção
     */
    addSectionSpecificFields(embed, section, configData) {
        switch (section) {
            case 'moderacao':
                if (configData.auto_mod_enabled !== undefined) {
                    embed.addFields({
                        name: 'Auto-Moderação',
                        value: configData.auto_mod_enabled ? '✅ Ativada' : '❌ Desativada',
                        inline: true
                    });
                }
                break;

            case 'ai':
                if (configData.settings?.ai_moderation_enabled !== undefined) {
                    embed.addFields({
                        name: 'IA de Moderação',
                        value: configData.settings.ai_moderation_enabled ? '✅ Ativada' : '❌ Desativada',
                        inline: true
                    });
                }
                break;

            case 'antiraid':
                if (configData.anti_raid_enabled !== undefined) {
                    embed.addFields({
                        name: 'Anti-Raid',
                        value: configData.anti_raid_enabled ? '✅ Ativado' : '❌ Desativado',
                        inline: true
                    });
                }
                break;

            case 'verificacao':
                if (configData.settings?.verification_enabled !== undefined) {
                    embed.addFields({
                        name: 'Sistema de Verificação',
                        value: configData.settings.verification_enabled ? '✅ Ativado' : '❌ Desativado',
                        inline: true
                    });
                }
                break;
        }
    }
}

module.exports = NotificationManager;
