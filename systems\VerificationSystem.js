/**
 * ========================================
 * VERIFICATION SYSTEM - SISTEMA DE VERIFICAÇÃO
 * Sistema completo para verificação de novos membros
 * ========================================
 */

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const IPVerificationSystem = require('./IPVerificationSystem');

class VerificationSystem {
    constructor(client) {
        this.client = client;
        this.pendingVerifications = new Map(); // Cache de verificações pendentes
        this.captchaCache = new Map(); // Cache de captchas
        this.ipVerification = new IPVerificationSystem(client); // Sistema de verificação de IP

        // Configurações
        this.config = {
            maxAttempts: 3,
            captchaTimeout: 300000, // 5 minutos
            dmTimeout: 30000, // 30 segundos para responder DM
            cleanupInterval: 600000 // 10 minutos
        };
    }

    /**
     * Inicializar sistema de verificação
     */
    async initialize() {
        try {
            this.client.logger.info('▣ Inicializando Sistema de Verificação...');

            // Iniciar limpeza automática
            this.startCleanup();

            // Configurar listeners para mudanças de configuração (PADRÃO DOS MÓDULOS FUNCIONAIS)
            this.setupConfigListeners();

            this.client.logger.info('▣ Sistema de Verificação inicializado com sucesso');
        } catch (error) {
            this.client.logger.error('Erro ao inicializar Sistema de Verificação:', error);
        }
    }

    /**
     * Configurar listeners para mudanças de configuração (SEGUINDO PADRÃO DO AI MODERATION)
     */
    setupConfigListeners() {
        // Listener para mudanças de verificação
        this.client.on('verificationUpdated', async ({ guildId, config }) => {
            console.log(`🔄 [VERIFICATION] Configurações atualizadas para servidor ${guildId}`);
            try {
                // Processar mudanças de verificação
                await this.handleConfigUpdate(guildId, config);

                // Notificar canal de log se configurado
                const guild = this.client.guilds.cache.get(guildId);
                if (guild && config.verification_log_enabled && config.log_channel_id) {
                    const logChannel = guild.channels.cache.get(config.log_channel_id);
                    if (logChannel) {
                        const embed = new EmbedBuilder()
                            .setColor('#00ff7f')
                            .setTitle('🛡️ Sistema de Verificação Atualizado')
                            .setDescription('As configurações de verificação foram atualizadas via dashboard.')
                            .addFields(
                                { name: '📡 Status', value: config.verification_enabled ? '✅ Ativado' : '❌ Desativado' },
                                { name: '🔒 Método', value: config.verification_method || 'Padrão' }
                            )
                            .setTimestamp();

                        await logChannel.send({ embeds: [embed] });
                    }
                }
            } catch (error) {
                console.error('❌ [VERIFICATION] Erro ao processar atualização:', error);
            }
        });
    }

    /**
     * Processar atualizações de configuração (NOVO - SEGUINDO PADRÃO DOS MÓDULOS FUNCIONAIS)
     */
    async handleConfigUpdate(guildId, config) {
        try {
            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) return;

            // Verificar se a verificação foi ativada/desativada
            if (config.verification_enabled !== undefined) {
                if (config.verification_enabled) {
                    console.log(`🛡️ [VERIFICATION] Sistema ATIVADO para ${guild.name}`);
                    // Criar mensagem de verificação se tiver configurações necessárias
                    if (config.verification_channel_id && config.verified_role_id) {
                        console.log(`🔍 [VERIFICATION DEBUG] Config recebida:`, {
                            channel: config.verification_channel_id,
                            role: config.verified_role_id,
                            enabled: config.verification_enabled
                        });
                        await this.createVerificationMessage(guild, config);
                    } else {
                        console.log(`⚠️ [VERIFICATION] Configurações incompletas:`, {
                            channel: config.verification_channel_id,
                            role: config.verified_role_id
                        });
                    }
                } else {
                    console.log(`❌ [VERIFICATION] Sistema DESATIVADO para ${guild.name}`);
                    await this.removeVerificationMessage(guild);
                }
            }
        } catch (error) {
            this.client.logger.error('Erro ao processar atualização de configuração:', error);
        }
    }

    /**
     * Iniciar limpeza automática de verificações expiradas
     */
    startCleanup() {
        setInterval(() => {
            this.cleanupExpiredVerifications();
        }, this.config.cleanupInterval);
    }

    /**
     * Limpar verificações expiradas
     */
    cleanupExpiredVerifications() {
        const now = Date.now();
        
        for (const [key, verification] of this.pendingVerifications) {
            if (now - verification.startTime > this.config.captchaTimeout) {
                this.pendingVerifications.delete(key);
            }
        }

        for (const [key, captcha] of this.captchaCache) {
            if (now - captcha.createdAt > this.config.captchaTimeout) {
                this.captchaCache.delete(key);
            }
        }
    }

    /**
     * Criar mensagem de verificação (NOVO MÉTODO - SEGUINDO PADRÃO DOS MÓDULOS FUNCIONAIS)
     */
    async createVerificationMessage(guild, config) {
        try {
            console.log(`🛡️ [VERIFICATION] Criando mensagem para ${guild.name}...`);

            // VALIDAÇÃO RIGOROSA - PREVENIR VALORES UNDEFINED
            if (!config.verification_channel_id || config.verification_channel_id === 'undefined' || config.verification_channel_id === 'null' || config.verification_channel_id === '') {
                throw new Error(`ID do canal de verificação inválido: "${config.verification_channel_id}"`);
            }

            if (!config.verified_role_id || config.verified_role_id === 'undefined' || config.verified_role_id === 'null' || config.verified_role_id === '') {
                throw new Error(`ID do cargo de verificado inválido: "${config.verified_role_id}"`);
            }

            // CORREÇÃO AUTOMÁTICA NO BANCO DE DADOS SE DETECTAR VALORES INVÁLIDOS
            try {
                const invalidCount = this.client.database.db.prepare(`
                    SELECT COUNT(*) as count FROM verification_config
                    WHERE guild_id = ? AND (
                        verified_role_id = 'undefined' OR verified_role_id = 'null' OR
                        channel_id = 'undefined' OR channel_id = 'null'
                    )
                `).get(guild.id);

                if (invalidCount.count > 0) {
                    console.log(`🔧 [VERIFICATION] Corrigindo ${invalidCount.count} valores inválidos no banco...`);
                    this.client.database.db.prepare(`
                        UPDATE verification_config
                        SET verified_role_id = CASE
                                WHEN verified_role_id IN ('undefined', 'null') THEN NULL
                                ELSE verified_role_id
                            END,
                            channel_id = CASE
                                WHEN channel_id IN ('undefined', 'null') THEN NULL
                                ELSE channel_id
                            END
                        WHERE guild_id = ?
                    `).run(guild.id);
                    console.log(`✅ [VERIFICATION] Valores inválidos corrigidos no banco`);
                }
            } catch (error) {
                console.error(`❌ [VERIFICATION] Erro ao corrigir banco:`, error);
            }

            console.log(`🔍 [VERIFICATION DEBUG] Validação passou - IDs válidos:`, {
                channel: config.verification_channel_id,
                role: config.verified_role_id
            });

            const channel = guild.channels.cache.get(config.verification_channel_id);
            if (!channel) {
                throw new Error(`Canal de verificação não encontrado: ${config.verification_channel_id}`);
            }

            const role = guild.roles.cache.get(config.verified_role_id);
            if (!role) {
                throw new Error(`Cargo de verificado não encontrado: ${config.verified_role_id}`);
            }

            console.log(`✅ [VERIFICATION] Canal: #${channel.name}`);
            console.log(`✅ [VERIFICATION] Cargo: @${role.name}`);
            console.log(`✅ [VERIFICATION] Método: ${config.verification_method || 'reaction'}`);

            // Remover mensagem anterior se existir
            await this.removeVerificationMessage(guild);

            // Criar embed de verificação
            const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

            const embed = new EmbedBuilder()
                .setTitle('🛡️ Verificação do Servidor')
                .setDescription(config.verification_rules_text || 'Clique no botão abaixo para se verificar no servidor!')
                .setColor('#00ff7f')
                .addFields(
                    { name: '📋 Como funciona', value: 'Clique no botão verde abaixo para iniciar o processo de verificação.', inline: false },
                    { name: '🎯 Cargo obtido', value: `@${role.name}`, inline: true },
                    { name: '🔧 Método', value: config.verification_method || 'Botão de Reação', inline: true }
                )
                .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                .setTimestamp();

            // Criar botão de verificação
            const button = new ButtonBuilder()
                .setCustomId('verify_user')
                .setLabel('✅ Verificar-me')
                .setStyle(ButtonStyle.Success);

            const row = new ActionRowBuilder().addComponents(button);

            // Enviar mensagem
            const message = await channel.send({
                embeds: [embed],
                components: [row]
            });

            // Salvar TODAS as configurações no banco de dados (CORRIGIDO - NOMES DAS COLUNAS)
            this.client.database.db.prepare(`
                INSERT OR REPLACE INTO verification_config
                (guild_id, enabled, channel_id, verified_role_id, unverified_role_id, method,
                 captcha_type, timeout_minutes, auto_kick, kick_time, dm_enabled,
                 log_enabled, anti_bot_enabled, ip_check_enabled, max_attempts, rules_text,
                 welcome_message, message_id, combined_captcha, combined_rules)
                VALUES (?, 1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `).run(
                guild.id,
                config.verification_channel_id,
                config.verified_role_id,
                config.unverified_role_id || null,
                config.verification_method || 'reaction',
                config.verification_captcha_type || 'emoji',
                config.verification_timeout_minutes || 30,
                config.verification_auto_kick ? 1 : 0,
                config.verification_kick_time || 60,
                config.verification_dm_enabled !== false ? 1 : 0,
                config.verification_log_enabled !== false ? 1 : 0,
                config.verification_anti_bot !== false ? 1 : 0,
                config.verification_ip_check ? 1 : 0,
                config.verification_max_attempts || 3,
                config.verification_rules_text || 'Clique no botão abaixo para se verificar!',
                config.welcome_message || 'Bem-vindo(a) ao servidor! Você foi verificado(a) com sucesso.',
                message.id,
                config.combined_captcha ? 1 : 0,
                config.combined_rules ? 1 : 0
            );

            console.log(`🎉 [VERIFICATION] MENSAGEM CRIADA COM SUCESSO!`);
            console.log(`🎉 [VERIFICATION] ID da mensagem: ${message.id}`);
            console.log(`🎉 [VERIFICATION] URL: https://discord.com/channels/${guild.id}/${channel.id}/${message.id}`);

            return message;

        } catch (error) {
            console.error('❌ [VERIFICATION] Erro ao criar mensagem:', error);
            throw error;
        }
    }

    /**
     * Remover mensagem de verificação existente
     */
    async removeVerificationMessage(guild) {
        try {
            const existingConfig = this.client.database.db.prepare(`
                SELECT message_id, channel_id FROM verification_config WHERE guild_id = ?
            `).get(guild.id);

            if (existingConfig?.message_id && existingConfig?.channel_id) {
                const channel = guild.channels.cache.get(existingConfig.channel_id);
                if (channel) {
                    try {
                        const message = await channel.messages.fetch(existingConfig.message_id);
                        await message.delete();
                        console.log(`🗑️ [VERIFICATION] Mensagem anterior removida`);
                    } catch (error) {
                        console.log(`⚠️ [VERIFICATION] Não foi possível remover mensagem anterior:`, error.message);
                    }
                }
            }
        } catch (error) {
            console.log(`⚠️ [VERIFICATION] Erro ao remover mensagem:`, error.message);
        }
    }

    /**
     * Configurar mensagem de verificação no canal (MÉTODO ORIGINAL - MANTIDO PARA COMPATIBILIDADE)
     */
    async setupVerificationMessage(guild, config) {
        // Redirecionar para o novo método que funciona
        return await this.createVerificationMessage(guild, config);
    }

    /**
     * Obter instruções de verificação baseado no método
     */
    getVerificationInstructions(method) {
        const instructions = {
            'reaction': '• Clique no botão "✅ Verificar" abaixo\n• Aguarde a confirmação',
            'captcha': '• Clique no botão "🧮 Resolver Captcha"\n• Resolva o desafio matemático\n• Envie a resposta',
            'manual': '• Aguarde um moderador aprovar sua entrada\n• Seja paciente, isso pode levar alguns minutos',
            'combined': '• Clique no botão "🔗 Iniciar Verificação"\n• Complete todos os passos solicitados'
        };

        return instructions[method] || 'Siga as instruções fornecidas pelos moderadores.';
    }

    /**
     * Criar componentes de verificação
     */
    createVerificationComponents(method) {
        const row = new ActionRowBuilder();

        switch (method) {
            case 'reaction':
                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId('verify_reaction')
                        .setLabel('✅ Verificar')
                        .setStyle(ButtonStyle.Success)
                );
                break;

            case 'captcha':
                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId('verify_captcha')
                        .setLabel('🧮 Resolver Captcha')
                        .setStyle(ButtonStyle.Primary)
                );
                break;

            case 'combined':
                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId('verify_combined')
                        .setLabel('🔗 Iniciar Verificação')
                        .setStyle(ButtonStyle.Secondary)
                );
                break;

            case 'manual':
                // Sem botões para verificação manual
                return [];
        }

        return [row];
    }

    /**
     * Processar novo membro (ATUALIZADO - PROCESSAMENTO COMPLETO DE TODAS AS CONFIGURAÇÕES)
     */
    async processNewMember(member, ipAddress = null, userAgent = null) {
        try {
            const guild = member.guild;

            // Buscar configuração de verificação
            const config = this.client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ? AND enabled = 1
            `).get(guild.id);

            if (!config) {
                console.log(`🛡️ [VERIFICATION] Sistema não configurado para ${guild.name}`);
                return;
            }

            console.log(`🛡️ [VERIFICATION] Processando ${member.user.tag} com configurações:`, {
                method: config.method,
                dm_enabled: config.dm_enabled,
                timeout_minutes: config.timeout_minutes,
                auto_kick: config.auto_kick,
                ip_check: config.ip_check,
                anti_bot: config.anti_bot
            });

            // Verificar IP suspeito se habilitado
            let ipCheckResult = null;
            if (config.ip_check && ipAddress) {
                console.log(`🔍 [VERIFICATION] Verificando IP para ${member.user.tag}`);
                ipCheckResult = await this.ipVerification.checkSuspiciousIP(ipAddress, member.id, guild.id);

                // Se IP for muito suspeito, bloquear imediatamente
                if (ipCheckResult.suspicious && ipCheckResult.confidence > 80) {
                    console.log(`🚫 [VERIFICATION] IP suspeito detectado para ${member.user.tag}`);
                    await this.handleSuspiciousIP(member, ipCheckResult);
                    return;
                }
            }

            // Verificar proteção anti-bot
            if (config.anti_bot_enabled) {
                const accountAge = Date.now() - member.user.createdTimestamp;
                const ageDays = accountAge / (1000 * 60 * 60 * 24);

                console.log(`🤖 [VERIFICATION] Verificação anti-bot: ${member.user.tag} (${ageDays.toFixed(1)} dias)`);

                if (ageDays < 1) {
                    console.log(`🚨 [VERIFICATION] Conta muito nova detectada: ${member.user.tag} (${ageDays.toFixed(1)} dias)`);

                    // Log da detecção
                    this.client.database.db.prepare(`
                        INSERT INTO verification_logs
                        (guild_id, user_id, action, method, details, timestamp)
                        VALUES (?, ?, 'anti_bot_detected', 'anti_bot', ?, CURRENT_TIMESTAMP)
                    `).run(guild.id, member.id, JSON.stringify({
                        account_age_days: ageDays,
                        account_age_hours: accountAge / (1000 * 60 * 60),
                        flagged_as_suspicious: true
                    }));

                    // Enviar log para canal
                    await this.sendVerificationLogToChannel(member, config, 'anti_bot_detected', 'anti_bot', {
                        account_age_days: ageDays.toFixed(1),
                        flagged_as_suspicious: true
                    });

                    // Aplicar medidas extras de segurança
                    // Por exemplo, exigir verificação manual ou captcha mais complexo
                    if (config.method === 'reaction') {
                        // Forçar captcha para contas muito novas
                        config.method = 'captcha_math';
                        console.log(`🔄 [VERIFICATION] Método alterado para captcha devido à conta nova`);
                    }
                }
            }

            // Criar registro de verificação
            this.client.database.db.prepare(`
                INSERT OR REPLACE INTO member_verification
                (guild_id, user_id, verified, attempts, last_attempt, ip_address, user_agent, suspicious_ip)
                VALUES (?, ?, 0, 0, CURRENT_TIMESTAMP, ?, ?, ?)
            `).run(guild.id, member.id, ipAddress, userAgent, ipCheckResult?.suspicious ? 1 : 0);

            // Atribuir cargo de não verificado se configurado
            if (config.unverified_role_id) {
                const unverifiedRole = guild.roles.cache.get(config.unverified_role_id);
                if (unverifiedRole) {
                    await member.roles.add(unverifiedRole, 'Novo membro - aguardando verificação');
                    console.log(`🏷️ [VERIFICATION] Cargo de não verificado aplicado: @${unverifiedRole.name}`);
                }
            }

            // Enviar DM de boas-vindas com instruções (se habilitado)
            if (config.dm_enabled) {
                console.log(`📩 [VERIFICATION] Enviando DM de instruções para ${member.user.tag}`);
                await this.sendWelcomeDM(member, config);
            } else {
                console.log(`📩 [VERIFICATION] DM desabilitado para ${member.user.tag}`);
            }

            // Log da entrada (se habilitado)
            if (config.log_enabled) {
                this.client.database.db.prepare(`
                    INSERT INTO verification_logs
                    (guild_id, user_id, action, method, details, timestamp)
                    VALUES (?, ?, 'started', ?, ?, CURRENT_TIMESTAMP)
                `).run(guild.id, member.id, config.method, JSON.stringify({
                    joinedAt: member.joinedAt.toISOString(),
                    dm_sent: config.dm_enabled,
                    ip_checked: !!config.ip_check,
                    anti_bot_active: !!config.anti_bot
                }));

                // Enviar log para canal
                await this.sendVerificationLogToChannel(member, config, 'started', config.method, {
                    dm_sent: config.dm_enabled,
                    ip_checked: !!config.ip_check,
                    anti_bot_active: !!config.anti_bot
                });

                console.log(`📝 [VERIFICATION] Log de entrada registrado para ${member.user.tag}`);
            }

            // Configurar timeout se especificado
            if (config.timeout_minutes > 0) {
                console.log(`⏰ [VERIFICATION] Timeout configurado: ${config.timeout_minutes} minutos para ${member.user.tag}`);
                setTimeout(async () => {
                    await this.handleVerificationTimeout(member, config);
                }, config.timeout_minutes * 60 * 1000);
            }

            console.log(`✅ [VERIFICATION] Processamento completo para ${member.user.tag} no servidor ${guild.name}`);

        } catch (error) {
            console.error(`❌ [VERIFICATION] Erro ao processar ${member.user.tag}:`, error);
            this.client.logger.error('Erro ao processar novo membro para verificação:', error);
        }
    }

    /**
     * Enviar DM de boas-vindas (ATUALIZADO - USA CONFIGURAÇÕES PERSONALIZADAS DO DASHBOARD)
     */
    async sendWelcomeDM(member, config) {
        try {
            // Buscar canal de verificação para incluir no DM
            const verificationChannel = member.guild.channels.cache.get(config.channel_id);
            const channelMention = verificationChannel ? `<#${config.channel_id}>` : 'canal de verificação';

            const embed = new EmbedBuilder()
                .setColor('#00ff7f')
                .setTitle(`🎉 Bem-vindo(a) ao ${member.guild.name}!`)
                .setDescription(config.rules_text || 'Para ter acesso completo ao servidor, você precisa completar a verificação.')
                .addFields(
                    {
                        name: '📋 Como se Verificar',
                        value: `1️⃣ Vá até o ${channelMention}\n2️⃣ ${this.getMethodDescription(config.method)}\n3️⃣ Aguarde a confirmação\n4️⃣ Aproveite o servidor!`,
                        inline: false
                    },
                    {
                        name: '⏰ Tempo Limite',
                        value: config.timeout_minutes > 0
                            ? `Você tem **${config.timeout_minutes} minutos** para completar a verificação.`
                            : 'Sem limite de tempo - verifique-se quando puder.',
                        inline: true
                    },
                    {
                        name: '🔧 Método de Verificação',
                        value: this.getMethodDescription(config.method),
                        inline: true
                    }
                )
                .setThumbnail(member.guild.iconURL())
                .setFooter({
                    text: 'Nodex | Moderação - Sistema de Verificação',
                    iconURL: this.client.user.displayAvatarURL()
                })
                .setTimestamp();

            // Adicionar campo de aviso se auto-kick estiver ativado
            if (config.auto_kick && config.kick_time > 0) {
                embed.addFields({
                    name: '⚠️ Importante',
                    value: `Se você não se verificar em **${config.kick_time} minutos**, será removido(a) automaticamente do servidor.`,
                    inline: false
                });
            }

            // Adicionar campo de tentativas máximas
            if (config.max_attempts > 0) {
                embed.addFields({
                    name: '🎯 Tentativas',
                    value: `Você tem **${config.max_attempts} tentativas** para completar a verificação.`,
                    inline: true
                });
            }

            await member.send({ embeds: [embed] });

            console.log(`✅ [VERIFICATION] DM de boas-vindas enviado para ${member.user.tag}`);

            // Log do envio de DM
            if (config.log_enabled) {
                this.client.database.db.prepare(`
                    INSERT INTO verification_logs
                    (guild_id, user_id, action, method, details, timestamp)
                    VALUES (?, ?, 'dm_sent', 'welcome', ?, CURRENT_TIMESTAMP)
                `).run(member.guild.id, member.id, JSON.stringify({
                    message_type: 'welcome_instructions',
                    channel_mentioned: !!verificationChannel,
                    timeout_minutes: config.timeout_minutes,
                    auto_kick: config.auto_kick
                }));
            }

        } catch (error) {
            console.log(`❌ [VERIFICATION] Não foi possível enviar DM para ${member.user.tag}: ${error.message}`);

            // Log do erro de DM
            if (config.log_enabled) {
                this.client.database.db.prepare(`
                    INSERT INTO verification_logs
                    (guild_id, user_id, action, method, details, timestamp)
                    VALUES (?, ?, 'dm_failed', 'welcome', ?, CURRENT_TIMESTAMP)
                `).run(member.guild.id, member.id, JSON.stringify({
                    error: error.message,
                    error_code: error.code
                }));
            }
        }
    }

    /**
     * Obter descrição do método de verificação
     */
    getMethodDescription(method) {
        const descriptions = {
            'reaction': 'clicar no botão de verificação',
            'captcha_math': 'resolver o captcha matemático',
            'captcha_text': 'digitar a palavra do captcha',
            'captcha_emoji': 'identificar o emoji do captcha',
            'manual': 'aguardar aprovação manual',
            'combined': 'completar todos os passos de verificação'
        };

        return descriptions[method] || 'seguir as instruções';
    }

    /**
     * Lidar com IP suspeito
     */
    async handleSuspiciousIP(member, ipCheckResult) {
        try {
            const guild = member.guild;

            // Log da atividade suspeita
            this.client.database.db.prepare(`
                INSERT INTO verification_logs
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'blocked_suspicious_ip', 'ip_check', ?, CURRENT_TIMESTAMP)
            `).run(guild.id, member.id, JSON.stringify({
                reasons: ipCheckResult.reasons,
                confidence: ipCheckResult.confidence,
                ip_blocked: true
            }));

            // Enviar DM explicativo
            try {
                const embed = new EmbedBuilder()
                    .setColor(0xff4757)
                    .setTitle('🚫 Acesso Negado')
                    .setDescription('Sua tentativa de entrada foi bloqueada por medidas de segurança.')
                    .addFields(
                        {
                            name: '⚠️ Motivo',
                            value: 'Detectamos atividade suspeita associada à sua conexão.',
                            inline: false
                        },
                        {
                            name: '📞 Suporte',
                            value: 'Se você acredita que isso é um erro, entre em contato com os moderadores.',
                            inline: false
                        }
                    )
                    .setFooter({
                        text: 'Nodex | Moderação - Sistema de Segurança',
                        iconURL: this.client.user.displayAvatarURL()
                    })
                    .setTimestamp();

                await member.send({ embeds: [embed] });
            } catch (error) {
                // Ignorar erro de DM
            }

            // Remover do servidor
            await member.kick('IP suspeito detectado pelo sistema de segurança');

            this.client.logger.warn(`Membro bloqueado por IP suspeito: ${member.user.tag} (${ipCheckResult.confidence}% confiança)`);

        } catch (error) {
            this.client.logger.error('Erro ao lidar com IP suspeito:', error);
        }
    }

    /**
     * Completar verificação
     */
    async completeVerification(member, config, method, verifiedBy = 'system') {
        try {
            const guild = member.guild;

            // Atribuir cargos
            const verifiedRole = guild.roles.cache.get(config.verified_role_id);
            const unverifiedRole = config.unverified_role_id ? guild.roles.cache.get(config.unverified_role_id) : null;

            if (verifiedRole) {
                await member.roles.add(verifiedRole, `Verificação concluída via ${method}`);
            }

            if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
                await member.roles.remove(unverifiedRole, 'Verificação concluída');
            }

            // Atualizar banco
            this.client.database.db.prepare(`
                UPDATE member_verification 
                SET verified = 1, verification_method = ?, verified_at = CURRENT_TIMESTAMP
                WHERE guild_id = ? AND user_id = ?
            `).run(method, guild.id, member.id);

            // Log da verificação
            this.client.database.db.prepare(`
                INSERT INTO verification_logs
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'completed', ?, ?, CURRENT_TIMESTAMP)
            `).run(guild.id, member.id, method, JSON.stringify({
                verifiedBy,
                success: true
            }));

            // Enviar log para canal se configurado
            await this.sendVerificationLogToChannel(member, config, 'completed', method, {
                verifiedBy,
                success: true,
                roleAdded: verifiedRole?.name
            });

            // Enviar DM de sucesso
            await this.sendSuccessDM(member, config);

            // Sistema de auto-roles removido do projeto conforme solicitado

            this.client.logger.info(`Membro verificado: ${member.user.tag} no servidor ${guild.name} via ${method}`);

        } catch (error) {
            this.client.logger.error('Erro ao completar verificação:', error);
            throw error;
        }
    }

    /**
     * Enviar DM de sucesso
     */
    async sendSuccessDM(member, config) {
        try {
            // Verificar se DM está habilitado
            if (!config.dm_enabled) return;

            const embed = new EmbedBuilder()
                .setColor(0x00ff7f)
                .setTitle('✅ Verificação Concluída!')
                .setDescription(config.welcome_message || 'Bem-vindo(a) ao servidor! Você foi verificado(a) com sucesso.')
                .addFields(
                    {
                        name: '🎉 Parabéns!',
                        value: 'Agora você tem acesso completo ao servidor e pode participar de todas as atividades.',
                        inline: false
                    },
                    {
                        name: '🛡️ Sistema de Verificação',
                        value: 'Sua conta foi verificada com sucesso pelo Nodex | Moderação.',
                        inline: false
                    }
                )
                .setThumbnail(member.guild.iconURL())
                .setFooter({
                    text: 'Nodex | Moderação',
                    iconURL: this.client.user.displayAvatarURL()
                })
                .setTimestamp();

            await member.send({ embeds: [embed] });

            // Log do envio de DM
            this.client.database.db.prepare(`
                INSERT INTO verification_logs
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'dm_sent', 'success', ?, CURRENT_TIMESTAMP)
            `).run(member.guild.id, member.id, JSON.stringify({
                message_type: 'welcome',
                dm_enabled: true
            }));

        } catch (error) {
            // Log erro de DM
            this.client.database.db.prepare(`
                INSERT INTO verification_logs
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'dm_failed', 'success', ?, CURRENT_TIMESTAMP)
            `).run(member.guild.id, member.id, JSON.stringify({
                error: error.message,
                dm_enabled: true
            }));
        }
    }

    /**
     * Enviar mensagem de erro de verificação
     */
    async sendVerificationErrorDM(member, config, errorType, details = {}) {
        try {
            if (!config.dm_enabled) return;

            const errorMessages = {
                'max_attempts': {
                    title: '❌ Tentativas Esgotadas',
                    description: 'Você excedeu o número máximo de tentativas de verificação.',
                    field: 'Entre em contato com um moderador para assistência.'
                },
                'timeout': {
                    title: '⏰ Tempo Esgotado',
                    description: 'O tempo para completar a verificação expirou.',
                    field: 'Você pode tentar novamente entrando no servidor.'
                },
                'invalid_captcha': {
                    title: '🧮 Captcha Incorreto',
                    description: 'A resposta do captcha estava incorreta.',
                    field: 'Tente novamente com mais atenção.'
                },
                'system_error': {
                    title: '⚠️ Erro do Sistema',
                    description: 'Ocorreu um erro técnico durante a verificação.',
                    field: 'Entre em contato com os moderadores.'
                }
            };

            const errorInfo = errorMessages[errorType] || errorMessages['system_error'];

            const embed = new EmbedBuilder()
                .setColor(0xff4757)
                .setTitle(errorInfo.title)
                .setDescription(errorInfo.description)
                .addFields(
                    {
                        name: '📞 Próximos Passos',
                        value: errorInfo.field,
                        inline: false
                    }
                )
                .setThumbnail(member.guild.iconURL())
                .setFooter({
                    text: 'Nodex | Moderação',
                    iconURL: this.client.user.displayAvatarURL()
                })
                .setTimestamp();

            await member.send({ embeds: [embed] });

            // Log do erro
            this.client.database.db.prepare(`
                INSERT INTO verification_logs
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'error_dm_sent', ?, ?, CURRENT_TIMESTAMP)
            `).run(member.guild.id, member.id, errorType, JSON.stringify(details));

        } catch (error) {
            // Ignorar erro de DM
        }
    }

    /**
     * Gerar captcha matemático
     */
    generateMathCaptcha() {
        const operations = ['+', '-', '*'];
        const operation = operations[Math.floor(Math.random() * operations.length)];
        
        let num1, num2, answer;
        
        switch (operation) {
            case '+':
                num1 = Math.floor(Math.random() * 50) + 1;
                num2 = Math.floor(Math.random() * 50) + 1;
                answer = num1 + num2;
                break;
            case '-':
                num1 = Math.floor(Math.random() * 50) + 25;
                num2 = Math.floor(Math.random() * 25) + 1;
                answer = num1 - num2;
                break;
            case '*':
                num1 = Math.floor(Math.random() * 10) + 1;
                num2 = Math.floor(Math.random() * 10) + 1;
                answer = num1 * num2;
                break;
        }

        return {
            question: `${num1} ${operation} ${num2} = ?`,
            answer: answer
        };
    }

    /**
     * Processar timeout de verificação
     */
    async handleVerificationTimeout(member, config) {
        try {
            // Verificar se ainda não foi verificado
            const verification = this.client.database.db.prepare(`
                SELECT verified FROM member_verification 
                WHERE guild_id = ? AND user_id = ?
            `).get(member.guild.id, member.id);

            if (verification?.verified) return; // Já foi verificado

            // Log do timeout
            this.client.database.db.prepare(`
                INSERT INTO verification_logs
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'timeout', ?, ?, CURRENT_TIMESTAMP)
            `).run(member.guild.id, member.id, config.method, JSON.stringify({
                timeoutMinutes: config.timeout_minutes
            }));

            // Enviar log para canal
            await this.sendVerificationLogToChannel(member, config, 'timeout', config.method, {
                timeoutMinutes: config.timeout_minutes
            });

            // Remover do servidor (opcional, baseado na configuração)
            const settings = JSON.parse(config.settings || '{}');
            if (settings.kick_on_timeout !== false) {
                await member.kick('Tempo de verificação esgotado');
                this.client.logger.info(`Membro removido por timeout de verificação: ${member.user.tag}`);
            }

        } catch (error) {
            this.client.logger.error('Erro ao processar timeout de verificação:', error);
        }
    }

    /**
     * Enviar log de verificação para canal configurado
     */
    async sendVerificationLogToChannel(member, config, action, method, details = {}) {
        try {
            // Verificar se logs estão habilitados
            if (!config.log_enabled) return;

            // Buscar configuração geral do servidor para obter canal de logs
            const guildConfig = await this.client.database.getGuildConfig(member.guild.id);
            const logChannelId = guildConfig?.log_channel_id || guildConfig?.general_log_channel;

            if (!logChannelId) {
                console.log(`📝 [VERIFICATION LOG] Canal de logs não configurado para ${member.guild.name}`);
                return;
            }

            const logChannel = member.guild.channels.cache.get(logChannelId);
            if (!logChannel || !logChannel.isTextBased()) {
                console.log(`📝 [VERIFICATION LOG] Canal de logs não encontrado: ${logChannelId}`);
                return;
            }

            // Verificar permissões
            const permissions = logChannel.permissionsFor(member.guild.members.me);
            if (!permissions.has(['SendMessages', 'EmbedLinks'])) {
                console.log(`📝 [VERIFICATION LOG] Sem permissões no canal de logs`);
                return;
            }

            const { EmbedBuilder } = require('discord.js');
            let embed;

            // Criar embed baseado na ação
            switch (action) {
                case 'completed':
                    embed = new EmbedBuilder()
                        .setColor('#00ff7f')
                        .setTitle('✅ Verificação Concluída')
                        .setDescription(`${member.user} foi verificado com sucesso`)
                        .addFields(
                            { name: '👤 Usuário', value: `${member.user.tag} (${member.user.id})`, inline: true },
                            { name: '🔧 Método', value: this.getMethodDescription(method), inline: true },
                            { name: '🎯 Cargo Obtido', value: details.roleAdded || 'N/A', inline: true },
                            { name: '⏰ Verificado em', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: false }
                        )
                        .setThumbnail(member.user.displayAvatarURL())
                        .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                        .setTimestamp();
                    break;

                case 'started':
                    embed = new EmbedBuilder()
                        .setColor('#ffa500')
                        .setTitle('🛡️ Verificação Iniciada')
                        .setDescription(`${member.user} iniciou o processo de verificação`)
                        .addFields(
                            { name: '👤 Usuário', value: `${member.user.tag} (${member.user.id})`, inline: true },
                            { name: '🔧 Método', value: this.getMethodDescription(method), inline: true },
                            { name: '📅 Conta Criada', value: `<t:${Math.floor(member.user.createdTimestamp / 1000)}:R>`, inline: true }
                        )
                        .setThumbnail(member.user.displayAvatarURL())
                        .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                        .setTimestamp();
                    break;

                case 'failed_max_attempts':
                    embed = new EmbedBuilder()
                        .setColor('#ff4757')
                        .setTitle('❌ Verificação Falhada')
                        .setDescription(`${member.user} excedeu o número máximo de tentativas`)
                        .addFields(
                            { name: '👤 Usuário', value: `${member.user.tag} (${member.user.id})`, inline: true },
                            { name: '🔧 Método', value: this.getMethodDescription(method), inline: true },
                            { name: '🎯 Tentativas', value: `${details.attempts}/${details.max_attempts}`, inline: true }
                        )
                        .setThumbnail(member.user.displayAvatarURL())
                        .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                        .setTimestamp();
                    break;

                case 'timeout':
                    embed = new EmbedBuilder()
                        .setColor('#ff6b35')
                        .setTitle('⏰ Verificação Expirada')
                        .setDescription(`${member.user} não completou a verificação no tempo limite`)
                        .addFields(
                            { name: '👤 Usuário', value: `${member.user.tag} (${member.user.id})`, inline: true },
                            { name: '🔧 Método', value: this.getMethodDescription(method), inline: true },
                            { name: '⏰ Tempo Limite', value: `${details.timeoutMinutes} minutos`, inline: true }
                        )
                        .setThumbnail(member.user.displayAvatarURL())
                        .setFooter({ text: 'Nodex | Moderação - Sistema de Verificação' })
                        .setTimestamp();
                    break;

                case 'anti_bot_detected':
                    embed = new EmbedBuilder()
                        .setColor('#ff9500')
                        .setTitle('🤖 Proteção Anti-Bot Ativada')
                        .setDescription(`${member.user} foi detectado como conta suspeita`)
                        .addFields(
                            { name: '👤 Usuário', value: `${member.user.tag} (${member.user.id})`, inline: true },
                            { name: '📅 Idade da Conta', value: `${details.account_age_days} dias`, inline: true },
                            { name: '⚠️ Status', value: 'Verificação reforçada aplicada', inline: true }
                        )
                        .setThumbnail(member.user.displayAvatarURL())
                        .setFooter({ text: 'Nodex | Moderação - Sistema de Segurança' })
                        .setTimestamp();
                    break;

                default:
                    return; // Ação não reconhecida
            }

            await logChannel.send({ embeds: [embed] });
            console.log(`📝 [VERIFICATION LOG] Log enviado para #${logChannel.name}: ${action} - ${member.user.tag}`);

        } catch (error) {
            console.error(`❌ [VERIFICATION LOG] Erro ao enviar log:`, error);
        }
    }

    /**
     * Verificar se usuário pode tentar verificação
     */
    async canAttemptVerification(member, config) {
        try {
            // Buscar tentativas atuais
            const verification = this.client.database.db.prepare(`
                SELECT attempts, verified FROM member_verification
                WHERE guild_id = ? AND user_id = ?
            `).get(member.guild.id, member.id);

            // Se já verificado, não pode tentar novamente
            if (verification?.verified) {
                return { canAttempt: false, reason: 'already_verified' };
            }

            // Verificar limite de tentativas
            const maxAttempts = config.max_attempts || 3;
            const currentAttempts = verification?.attempts || 0;

            if (currentAttempts >= maxAttempts) {
                return {
                    canAttempt: false,
                    reason: 'max_attempts_exceeded',
                    attempts: currentAttempts,
                    maxAttempts: maxAttempts
                };
            }

            return {
                canAttempt: true,
                attempts: currentAttempts,
                maxAttempts: maxAttempts,
                remainingAttempts: maxAttempts - currentAttempts
            };

        } catch (error) {
            console.error('❌ [VERIFICATION] Erro ao verificar tentativas:', error);
            return { canAttempt: true }; // Permitir em caso de erro
        }
    }

    /**
     * Incrementar tentativas de verificação
     */
    async incrementAttempts(member) {
        try {
            this.client.database.db.prepare(`
                UPDATE member_verification
                SET attempts = attempts + 1, last_attempt = CURRENT_TIMESTAMP
                WHERE guild_id = ? AND user_id = ?
            `).run(member.guild.id, member.id);

            console.log(`📊 [VERIFICATION] Tentativas incrementadas para ${member.user.tag}`);

        } catch (error) {
            console.error('❌ [VERIFICATION] Erro ao incrementar tentativas:', error);
        }
    }

    /**
     * Obter estatísticas de verificação
     */
    async getStats(guildId) {
        try {
            const stats = this.client.database.db.prepare(`
                SELECT 
                    COUNT(*) as total_members,
                    SUM(CASE WHEN verified = 1 THEN 1 ELSE 0 END) as verified,
                    SUM(CASE WHEN verified = 0 THEN 1 ELSE 0 END) as pending
                FROM member_verification WHERE guild_id = ?
            `).get(guildId);

            const recentLogs = this.client.database.db.prepare(`
                SELECT COUNT(*) as recent_verifications
                FROM verification_logs 
                WHERE guild_id = ? AND action = 'completed' 
                AND timestamp > datetime('now', '-24 hours')
            `).get(guildId);

            return {
                totalMembers: stats?.total_members || 0,
                totalVerified: stats?.verified || 0,
                totalPending: stats?.pending || 0,
                recentVerifications: recentLogs?.recent_verifications || 0
            };

        } catch (error) {
            this.client.logger.error('Erro ao obter estatísticas de verificação:', error);
            return {
                totalMembers: 0,
                totalVerified: 0,
                totalPending: 0,
                recentVerifications: 0
            };
        }
    }
}

module.exports = VerificationSystem;