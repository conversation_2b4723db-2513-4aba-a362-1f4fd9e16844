/**
 * ========================================
 * COMANDO: WARN
 * Aplica uma advertência a um usuário
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');
const EmbedStyles = require('../../utils/EmbedStyles');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('warn')
        .setDescription('Aplica uma advertência a um usuário')
        .addUserOption(option =>
            option.setName('usuário')
                .setDescription('Usuário a ser advertido')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('motivo')
                .setDescription('Motivo da advertência')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .setDMPermission(false),

    async execute(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const executor = interaction.member;
        const target = interaction.options.getMember('usuário');
        const reason = interaction.options.getString('motivo');

        try {
            // Verificações de segurança com embeds premium
            if (!target) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Usuário Não Encontrado',
                    `${embedStyles.format.bold('O usuário especificado não foi encontrado no servidor!')}\n\n${embedStyles.icons.info}**Dica:**Verifique se o usuário ainda está no servidor.`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (target.id === interaction.user.id) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Ação Inválida',
                    `${embedStyles.format.bold('Você não pode advertir a si mesmo!')}\n\n${embedStyles.format.italic('Isso seria um pouco estranho, não acha?')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (target.id === client.user.id) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Ação Inválida',
                    `${embedStyles.format.bold('Não posso advertir a mim mesmo!')}\n\n${embedStyles.format.italic('Isso seria uma crise existencial!')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Verificar hierarquia
            if (executor.roles.highest.position <= target.roles.highest.position) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Hierarquia Insuficiente',
                    `${embedStyles.format.bold('Você não pode advertir este usuário!')}\n\n${embedStyles.icons.crown}**Motivo:**Hierarquia de cargos\n${embedStyles.format.italic('O cargo do usuário é igual ou superior ao seu.')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Verificar se o usuário é um bot
            if (target.user.bot) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Usuário Inválido',
                    `${embedStyles.format.bold('Não posso advertir outros bots!')}\n\n${embedStyles.icons.info}**Motivo:**Bots não podem receber advertências\n${embedStyles.format.italic('Use este comando apenas em usuários humanos.')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Obter histórico de advertências
            let warnings = [];
            try {
                const history = client.database.getModerationHistory(target.id, guild.id);
                if (history && Array.isArray(history)) {
                    warnings = history.filter(action => action.action === 'warn');
                } else {
                    warnings = [];
                }
            } catch (error) {
                client.logger.error('Erro ao obter histórico de moderação:', error);
                warnings = [];
            }

            const warningCount = warnings.length + 1;

            let dmSent = false;

            // Tentar enviar DM premium para o usuário
            try {
                const dmEmbed = {
                    color: parseInt(embedStyles.colors.warning.replace('#', ''), 16),
                    title: `${embedStyles.icons.warn} ${embedStyles.format.bold('Você recebeu uma advertência!')}`,
                    description: `**Você foi advertido no servidor ${embedStyles.format.bold(guild.name)}**\n\n${embedStyles.format.italic('Esta é uma notificação automática do sistema de moderação.')}`,
                    fields: [
                        {
                            name: `${embedStyles.icons.info}**Motivo da Advertência**`,
                            value: embedStyles.format.code(reason),
                            inline: false
                        },
                        {
                            name: `${embedStyles.icons.shield}**Moderador Responsável**`,
                            value: `${executor.user.tag}\n${embedStyles.format.code(executor.user.id)}`,
                            inline: true
                        },
                        {
                            name: `${embedStyles.icons.analytics}**Total de Advertências**`,
                            value: `${embedStyles.format.bold(warningCount.toString())}\n${embedStyles.format.italic('Advertências acumuladas')}`,
                            inline: true
                        },
                        {
                            name: `${embedStyles.icons.info}**Informações Importantes**`,
                            value: `• Esta advertência fica registrada permanentemente\n• Acúmulo de advertências pode resultar em punições\n• ${warningCount >= 3 ? '**Atenção:**Você está próximo de punições automáticas!' : 'Mantenha um comportamento adequado'}\n• Entre em contato com a moderação se tiver dúvidas`,
                            inline: false
                        }
                    ],
                    timestamp: new Date().toISOString(),
                    footer: {
                        text: 'Nodex | Moderação • Sistema de Advertências Premium',
                        icon_url: guild.iconURL()
                    },
                    thumbnail: {
                        url: guild.iconURL({ dynamic: true })
                    }
                };

                await target.send({ embeds: [dmEmbed] });
                dmSent = true;
            } catch (error) {
                // Usuário não pode receber DM
            }

            // Registrar advertência no banco de dados
            client.database.logModerationAction(
                guild.id,
                target.id,
                executor.id,
                'warn',
                reason,
                null,
                null,
                null,
                { automatic: false, warningNumber: warningCount }
            );

            // Embed de confirmação premium
            const successFields = [
                {
                    name: `${embedStyles.icons.user}**Usuário Advertido**`,
                    value: `${target.user.tag}\n${embedStyles.format.code(target.user.id)}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.shield}**Moderador**`,
                    value: `${executor.user.tag}\n${embedStyles.format.code(executor.user.id)}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.analytics}**Advertência #${warningCount}**`,
                    value: `${embedStyles.format.bold('Total:')} ${warningCount} advertência(s)\n${dmSent ?`${embedStyles.icons.success} DM enviada`:`${embedStyles.icons.error} DM não enviada`}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.info}**Motivo da Advertência**`,
                    value: embedStyles.format.code(reason),
                    inline: false
                }
            ];

            // Adicionar aviso sobre ações automáticas
            if (warningCount >= 5) {
                successFields.push({
                    name: `${embedStyles.icons.error}**ALERTA CRÍTICO**`,
                    value: `${embedStyles.format.bold('Este usuário possui 5+ advertências!')}\n**Próxima ação:**Banimento automático\n${embedStyles.format.italic('Considere aplicar punições manuais imediatamente.')}`,
                    inline: false
                });
            } else if (warningCount >= 3) {
                successFields.push({
                    name: `${embedStyles.icons.warning}**Atenção Especial**`,
                    value: `${embedStyles.format.bold('Este usuário possui 3+ advertências!')}\n**Próxima ação:**Timeout automático\n${embedStyles.format.italic('Monitore o comportamento deste usuário.')}`,
                    inline: false
                });
            } else {
                successFields.push({
                    name: `${embedStyles.icons.info}**Status do Usuário**`,
                    value: `${embedStyles.format.bold('Advertência registrada com sucesso!')}\n${embedStyles.format.italic('Continue monitorando o comportamento do usuário.')}`,
                    inline: false
                });
            }

            const successEmbed = embedStyles.createModerationEmbed('warn', target.user, executor.user, reason, successFields);

            await interaction.reply({ embeds: [successEmbed] });

            // Log premium no canal de logs se configurado
            const guildConfig = client.database.getGuildConfig(guild.id);
            if (guildConfig?.log_channel) {
                const logChannel = guild.channels.cache.get(guildConfig.log_channel);
                if (logChannel) {
                    const logEmbed = {
                        color: parseInt(embedStyles.colors.warning.replace('#', ''), 16),
                        title: `${embedStyles.icons.warn} ${embedStyles.format.bold('Advertência Aplicada')}`,
                        description: `**Ação de moderação executada no servidor**\n${embedStyles.format.italic('Log automático do sistema de moderação')}`,
                        fields: [
                            {
                                name: `${embedStyles.icons.user}**Usuário Advertido**`,
                                value: `${target.user.tag}\n${embedStyles.format.code(target.user.id)}`,
                                inline: true
                            },
                            {
                                name: `${embedStyles.icons.shield}**Moderador**`,
                                value: `${executor.user.tag}\n${embedStyles.format.code(executor.user.id)}`,
                                inline: true
                            },
                            {
                                name: `${embedStyles.icons.analytics}**Advertência #${warningCount}**`,
                                value: `${embedStyles.format.bold('Total:')} ${warningCount}\n${dmSent ?`${embedStyles.icons.success} DM enviada`:`${embedStyles.icons.error} DM não enviada`}`,
                                inline: true
                            },
                            {
                                name: `${embedStyles.icons.info}**Motivo da Advertência**`,
                                value: embedStyles.format.code(reason),
                                inline: false
                            }
                        ],
                        timestamp: new Date().toISOString(),
                        footer: {
                            text: 'Nodex | Moderação • Sistema de Logs Premium',
                            icon_url: guild.iconURL()
                        },
                        thumbnail: {
                            url: target.user.displayAvatarURL({ dynamic: true })
                        }
                    };

                    await logChannel.send({ embeds: [logEmbed] });
                }
            }

            client.logger.info(`Usuário ${target.user.tag} advertido por ${executor.user.tag}: ${reason} (Advertência #${warningCount})`);

            // Verificar se deve aplicar ação automática baseada no número de advertências
            if (warningCount >= 5) {
                // Auto-ban após 5 advertências
                try {
                    await target.ban({ reason: `Auto-ban: ${warningCount} advertências acumuladas` });
                    
                    const autoBanEmbed = new EmbedBuilder()
                        .setColor('#ff0000')
                        .setTitle('Auto-Ban Aplicado')
                        .setDescription(`**${target.user.tag}**foi automaticamente banido por acumular ${warningCount} advertências.`)
                        .setTimestamp()
                        .setFooter({ text: 'Nodex | Moderação - Sistema Automático' });

                    await interaction.followUp({ embeds: [autoBanEmbed] });
                } catch (error) {
                    client.logger.error('Erro ao aplicar auto-ban:', error);
                }
            } else if (warningCount >= 3) {
                // Auto-timeout após 3 advertências
                try {
                    const timeoutDuration = 2 * 60 * 60 * 1000; // 2 horas
                    await target.timeout(timeoutDuration, `Auto-timeout: ${warningCount} advertências acumuladas`);
                    
                    const autoTimeoutEmbed = new EmbedBuilder()
                        .setColor('#ffa500')
                        .setTitle('Auto-Timeout Aplicado')
                        .setDescription(`**${target.user.tag}**foi automaticamente colocado em timeout por 2 horas devido a ${warningCount} advertências.`)
                        .setTimestamp()
                        .setFooter({ text: 'Nodex | Moderação - Sistema Automático' });

                    await interaction.followUp({ embeds: [autoTimeoutEmbed] });
                } catch (error) {
                    client.logger.error('Erro ao aplicar auto-timeout:', error);
                }
            }

        } catch (error) {
            client.logger.error('Erro no comando warn:', error);

            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                `${embedStyles.format.bold('Ocorreu um erro ao aplicar a advertência.')}\n\n${embedStyles.icons.info}**Detalhes:**\n• Verifique as permissões do bot\n• Tente novamente em alguns segundos\n• Contate o suporte se o problema persistir\n\n${embedStyles.format.italic('Erro técnico: ' + error.message)}`
            );

            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },

    cooldown: 3,
    category: 'moderacao',
    examples: [
        '/warn @usuário Spam no chat',
        '/warn @usuário Linguagem inadequada',
        '/warn usuário:123456789 Desrespeito às regras'
    ],

    // Função para comandos de texto
    executeText: async function(message, args) {
        // Converter para interaction-like object para reutilizar código
        const fakeInteraction = {
            guild: message.guild,
            member: message.member,
            user: message.author,
            channel: message.channel,
            client: message.client,
            options: {
                getUser: () => {
                    const mention = args[0];
                    if (!mention) return null;
                    
                    if (mention.startsWith('<@') && mention.endsWith('>')) {
                        const userId = mention.slice(2, -1).replace('!', '');
                        return message.guild.members.cache.get(userId)?.user;
                    }
                    
                    if (/^\d+$/.test(mention)) {
                        return message.guild.members.cache.get(mention)?.user;
                    }
                    
                    return message.guild.members.cache.find(member =>
                        member.user.username.toLowerCase().includes(mention.toLowerCase()) ||
                        member.displayName.toLowerCase().includes(mention.toLowerCase())
                    )?.user;
                },
                getString: () => {
                    return args.slice(1).join(' ') || 'Nenhum motivo fornecido';
                }
            },
            reply: async (content) => {
                if (typeof content === 'string') {
                    return await message.reply(content);
                }
                return await message.reply(content);
            },
            deferReply: async () => {
                return Promise.resolve();
            },
            editReply: async (content) => {
                return await message.reply(content);
            },
            deferred: false
        };

        // Verificar argumentos básicos
        if (args.length === 0) {
            return await message.reply(`**Uso:** \`!warn @usuário [motivo]\``);
        }

        // Chamar função execute principal
        return await this.execute(fakeInteraction);
    }
};
