{"error":"Server is not running.","level":"error","message":"Error during server shutdown","timestamp":"2025-06-16T16:32:21.475Z"}
{"level":"error","message":"Unhandled Rejection","promise":{},"reason":{},"timestamp":"2025-06-16T17:26:05.943Z"}
{"level":"error","message":"Unhandled Rejection","promise":{},"reason":{},"timestamp":"2025-06-16T17:39:53.473Z"}
{"level":"error","message":"Unhandled Rejection","promise":{},"reason":{},"timestamp":"2025-06-16T18:31:10.133Z"}
{"error":{"message":"Unexpected token 'else' in C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\dashboard.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-<PERSON>t\nOr, if you meant to create an async function, pass `async: true` as an option.","name":"SyntaxError","stack":"SyntaxError: Unexpected token 'else' in C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\dashboard.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.\n    at new Function (<anonymous>)\n    at Template.compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:398:16)\n    at handleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:235:18)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:16)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T00:36:49.646Z","user":{"id":"558672715243061269","username":"emp2866"}}
{"error":{"message":"Unexpected token 'else' in C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\dashboard.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.","name":"SyntaxError","stack":"SyntaxError: Unexpected token 'else' in C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\dashboard.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.\n    at new Function (<anonymous>)\n    at Template.compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:398:16)\n    at handleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:235:18)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:16)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T00:37:10.536Z","user":{"id":"558672715243061269","username":"emp2866"}}
{"error":{"message":"Unexpected token 'else' in C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\dashboard.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.","name":"SyntaxError","stack":"SyntaxError: Unexpected token 'else' in C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\dashboard.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.\n    at new Function (<anonymous>)\n    at Template.compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:398:16)\n    at handleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:235:18)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:16)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T00:37:44.492Z","user":{"id":"558672715243061269","username":"emp2866"}}
{"error":{"message":"Unexpected token 'else' in C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\dashboard.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.","name":"SyntaxError","stack":"SyntaxError: Unexpected token 'else' in C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\dashboard.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.\n    at new Function (<anonymous>)\n    at Template.compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:398:16)\n    at handleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:235:18)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:16)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T01:10:57.295Z","user":{"id":"558672715243061269","username":"emp2866"}}
{"error":{"message":"Unexpected token 'else' in C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\dashboard.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.","name":"SyntaxError","stack":"SyntaxError: Unexpected token 'else' in C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\dashboard.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.\n    at new Function (<anonymous>)\n    at Template.compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:398:16)\n    at handleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:235:18)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:16)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T01:11:01.898Z","user":{"id":"558672715243061269","username":"emp2866"}}
{"error":{"message":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined","name":"ReferenceError","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined\n    at eval (eval at compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12), <anonymous>:15:8)\n    at login (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:117:17\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T02:24:07.217Z","user":null}
{"error":{"message":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined","name":"ReferenceError","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined\n    at eval (eval at compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12), <anonymous>:15:8)\n    at login (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:117:17\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T02:24:24.777Z","user":null}
{"error":{"message":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined","name":"ReferenceError","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined\n    at eval (eval at compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12), <anonymous>:15:8)\n    at login (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:117:17\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T02:24:56.858Z","user":null}
{"error":{"message":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined","name":"ReferenceError","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined\n    at eval (eval at compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12), <anonymous>:15:8)\n    at login (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:156:17\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T03:06:55.999Z","user":null}
{"error":{"message":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined","name":"ReferenceError","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined\n    at eval (eval at compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12), <anonymous>:15:8)\n    at login (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:156:17\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T03:07:06.412Z","user":null}
{"error":{"message":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined","name":"ReferenceError","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined\n    at eval (eval at compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12), <anonymous>:15:8)\n    at login (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:156:17\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T03:07:32.285Z","user":null}
{"error":{"message":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined","name":"ReferenceError","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined\n    at eval (eval at compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12), <anonymous>:15:8)\n    at login (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:156:17\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T03:08:06.956Z","user":null}
{"error":{"message":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined","name":"ReferenceError","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\views\\login.ejs:59\n    57| \n    58|                 <!-- Error Message -->\n >> 59|                 <% if (error) { %>\n    60|                 <div class=\"error-message\">\n    61|                     <i class=\"fas fa-exclamation-triangle\"></i>\n    62|                     <span><%= error %></span>\n\nerror is not defined\n    at eval (eval at compile (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:673:12), <anonymous>:15:8)\n    at login (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\response.js:1049:7)\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:156:17\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Application Error","request":{"body":{},"ip":"::1","method":"GET","params":{},"query":{},"url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-17T03:13:09.241Z","user":null}
