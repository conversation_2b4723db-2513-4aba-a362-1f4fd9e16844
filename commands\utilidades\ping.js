/**
 * ========================================
 * COMANDO: PING
 * Mostra latência do bot
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ping')
        .setDescription('Mostra a latência e status do bot'),

    async execute(interaction) {
        const client = interaction.client;

        try {
            // Initial response to calculate latency
            const sent = await interaction.reply({
                content: 'Calculando latência...',
                fetchReply: true
            });

            // Calculate latencies
            const roundtripLatency = sent.createdTimestamp - interaction.createdTimestamp;
            const websocketLatency = client.ws.ping;

            // Determine connection quality
            let connectionQuality = '';
            let color = '';

            if (roundtripLatency < 100) {
                connectionQuality = SVGIcons.status(true, 'Excelente');
                color = '#00ff7f';
            } else if (roundtripLatency < 200) {
                connectionQuality = SVGIcons.status(true, 'Boa');
                color = '#ffff00';
            } else if (roundtripLatency < 500) {
                connectionQuality = SVGIcons.status(false, 'Regular');
                color = '#ffa500';
            } else {
                connectionQuality = SVGIcons.status(false, 'Ruim');
                color = '#ff0000';
            }

            // Professional embed with detailed information
            const pingEmbed = new EmbedBuilder()
                .setColor(color)
                .setTitle(SVGIcons.bold('Latência do Bot'))
                .setDescription('Informações de conectividade e performance')
                .addFields(
                    {
                        name: '**Latência da API**',
                        value: SVGIcons.code(`${roundtripLatency}ms`),
                        inline: true
                    },
                    {
                        name: '**Latência WebSocket**',
                        value: SVGIcons.code(`${websocketLatency}ms`),
                        inline: true
                    },
                    {
                        name: '**Qualidade da Conexão**',
                        value: connectionQuality,
                        inline: true
                    },
                    {
                        name: '**Tempo Online**',
                        value: `<t:${Math.floor((Date.now() - client.uptime) / 1000)}:R>`,
                        inline: true
                    },
                    {
                        name: '**Plataforma do Servidor**',
                        value: SVGIcons.code(process.platform),
                        inline: true
                    },
                    {
                        name: '**Uso de Memória**',
                        value: SVGIcons.code(`${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`),
                        inline: true
                    }
                )
                .setThumbnail(client.user.displayAvatarURL())
                .setTimestamp()
                .setFooter({ 
                    text: 'Nodex | Moderação', 
                    iconURL: client.user.displayAvatarURL() 
                });

            // Add server information if in a guild
            if (interaction.guild) {
                pingEmbed.addFields({
                    name: '**Servidor Atual**',
                    value: `${SVGIcons.bold(interaction.guild.name)}\n\n**Membros:**${interaction.guild.memberCount}`,
                    inline: false
                });
            }

            // Update the response
            await interaction.editReply({
                content: null,
                embeds: [pingEmbed]
            });

            // Log execution
            client.logger.info(`Comando ping executado por ${interaction.user.tag} - Latência: ${roundtripLatency}ms`);

        } catch (error) {
            client.logger.error('Erro no comando ping:', error);

            await interaction.editReply({
                content: 'Ocorreu um erro ao calcular a latência.',
                embeds: []
            });
        }
    },

    cooldown: 5,
    category: 'utilidades',
    examples: ['/ping']
};
