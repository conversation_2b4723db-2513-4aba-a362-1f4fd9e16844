/**
 * ========================================
 * PREMIUM UNMUTE COMMAND SYSTEM
 * Remove timeout/mute with premium styling
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');
const EmbedStyles = require('../../utils/EmbedStyles');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unmute')
        .setDescription('Remover timeout/mute de um usuário com sistema premium')
        .addUserOption(option =>
            option.setName('usuário')
                .setDescription('Usuário para remover o mute')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('motivo')
                .setDescription('Motivo da remoção do mute')
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('silencioso')
                .setDescription('Não enviar DM para o usuário')
                .setRequired(false)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .setDMPermission(false),

    async execute(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const moderator = interaction.member;
        const target = interaction.options.getMember('usuário');
        const reason = interaction.options.getString('motivo') || 'Mute removido pela moderação';
        const silent = interaction.options.getBoolean('silencioso') || false;

        try {
            // Verificações de segurança com embeds premium
            if (!target) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Usuário Não Encontrado',
                    `${embedStyles.format.bold('O usuário especificado não foi encontrado no servidor!')}\n\n${embedStyles.icons.info}**Dica:**Verifique se o usuário ainda está no servidor.`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (target.id === interaction.user.id) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Ação Inválida',
                    `${embedStyles.format.bold('Você não pode desmutar a si mesmo!')}\n\n${embedStyles.format.italic('Use este comando em outros usuários.')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (target.id === client.user.id) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Ação Inválida',
                    `${embedStyles.format.bold('Não posso desmutar a mim mesmo!')}\n\n${embedStyles.format.italic('Eu não estava mutado mesmo!')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Verificar se o usuário está mutado
            if (!target.communicationDisabledUntil || target.communicationDisabledUntil <= new Date()) {
                const errorEmbed = embedStyles.createWarningEmbed(
                    'Usuário Não Mutado',
                    `${embedStyles.format.bold('Este usuário não está mutado!')}\n\n${embedStyles.icons.info}**Status:**Usuário pode enviar mensagens normalmente\n\n${embedStyles.format.italic('Use /mute para aplicar um mute.')}`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            await interaction.deferReply();

            // Calcular tempo restante do mute
            const timeRemaining = target.communicationDisabledUntil.getTime() - Date.now();
            
            let dmSent = false;

            // Tentar enviar DM premium para o usuário
            if (!silent) {
                try {
                    const dmEmbed = {
                        color: parseInt(embedStyles.colors.success.replace('#', ''), 16),
                        title: `${embedStyles.icons.success} ${embedStyles.format.bold('Seu mute foi removido!')}`,
                        description: `**Seu mute foi removido no servidor ${embedStyles.format.bold(guild.name)}**\n\n${embedStyles.format.italic('Você pode voltar a enviar mensagens normalmente.')}`,
                        fields: [
                            {
                                name: `${embedStyles.icons.info}**Motivo da Remoção**`,
                                value: embedStyles.format.code(reason),
                                inline: false
                            },
                            {
                                name: `${embedStyles.icons.shield}**Moderador Responsável**`,
                                value: `${moderator.user.tag}\n${embedStyles.format.code(moderator.user.id)}`,
                                inline: true
                            },
                            {
                                name: `${embedStyles.icons.info}**Tempo Restante**`,
                                value: `${embedStyles.formatDuration(timeRemaining)}\n${embedStyles.format.italic('Tempo que foi poupado')}`,
                                inline: true
                            },
                            {
                                name: `${embedStyles.icons.info}**Informações Importantes**`,
                                value: `• Você pode voltar a participar das conversas\n• Continue respeitando as regras do servidor\n• Evite comportamentos que levaram ao mute\n• Entre em contato com a moderação se tiver dúvidas`,
                                inline: false
                            }
                        ],
                        timestamp: new Date().toISOString(),
                        footer: {
                            text: 'Nodex | Moderação • Sistema de Unmute Premium',
                            icon_url: guild.iconURL()
                        },
                        thumbnail: {
                            url: guild.iconURL({ dynamic: true })
                        }
                    };

                    await target.send({ embeds: [dmEmbed] });
                    dmSent = true;
                } catch (error) {
                    // Usuário não pode receber DM
                }
            }

            // Remover timeout
            await target.timeout(null, reason);

            // Resposta de sucesso premium
            const successFields = [
                {
                    name: `${embedStyles.icons.user}**Usuário Desmutado**`,
                    value: `${target.user.tag}\n${embedStyles.format.code(target.user.id)}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.shield}**Moderador**`,
                    value: `${moderator.user.tag}\n${embedStyles.format.code(moderator.user.id)}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.info}**Status da Notificação**`,
                    value: dmSent ? `${embedStyles.icons.success} DM enviada` : `${embedStyles.icons.error} DM não enviada`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.info}**Tempo Poupado**`,
                    value: `${embedStyles.format.bold('Tempo restante:')} ${embedStyles.formatDuration(timeRemaining)}\n${embedStyles.format.bold('Status:')} ${embedStyles.icons.success} Mute removido com sucesso\n${embedStyles.format.bold('Ação:')} Remoção manual pela moderação`,
                    inline: false
                },
                {
                    name: `${embedStyles.icons.info}**Motivo da Remoção**`,
                    value: embedStyles.format.code(reason),
                    inline: false
                }
            ];

            const successEmbed = {
                color: parseInt(embedStyles.colors.success.replace('#', ''), 16),
                title: `${embedStyles.icons.success} ${embedStyles.format.bold('Mute Removido com Sucesso')}`,
                description: `**${embedStyles.format.bold(target.user.tag)} foi desmutado com sucesso!**\n\n${embedStyles.format.italic('O usuário pode voltar a enviar mensagens normalmente.')}`,
                fields: successFields,
                timestamp: new Date().toISOString(),
                footer: {
                    text: 'Nodex | Moderação • Sistema de Moderação Premium',
                    icon_url: guild.iconURL()
                },
                thumbnail: {
                    url: target.user.displayAvatarURL({ dynamic: true })
                }
            };
            
            await interaction.editReply({ embeds: [successEmbed] });

            // Log no banco de dados
            client.database.logModerationAction(
                guild.id, target.id, moderator.user.id, 'unmute', reason, null, null, null,
                { automatic: false, dmSent: dmSent, timeRemaining: timeRemaining }
            );

            // Log no canal de logs se configurado
            await this.logToModerationChannel(guild, {
                action: 'unmute',
                target: target.user,
                moderator: moderator.user,
                reason: reason,
                timeRemaining: timeRemaining,
                dmSent: dmSent
            });

            client.logger.info(`Usuário ${target.user.tag} desmutado por ${moderator.user.tag}: ${reason}`);

        } catch (error) {
            client.logger.error('Erro no comando unmute:', error);
            
            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                `${embedStyles.format.bold('Ocorreu um erro ao remover o mute.')}\n\n${embedStyles.icons.info}**Detalhes:**\n• Verifique as permissões do bot\n• Tente novamente em alguns segundos\n• Contate o suporte se o problema persistir\n\n${embedStyles.format.italic('Erro técnico: ' + error.message)}`
            );
            
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    // Função para log no canal de moderação
    async logToModerationChannel(guild, data) {
        try {
            const embedStyles = new EmbedStyles();
            const guildConfig = guild.client.database.getGuildConfig(guild.id);
            
            if (!guildConfig?.log_channel) return;
            
            const logChannel = guild.channels.cache.get(guildConfig.log_channel);
            if (!logChannel) return;

            const logEmbed = {
                color: parseInt(embedStyles.colors.success.replace('#', ''), 16),
                title: `${embedStyles.icons.success} ${embedStyles.format.bold('Usuário Desmutado')}`,
                description: `**Ação de moderação executada no servidor**\n${embedStyles.format.italic('Log automático do sistema de moderação')}`,
                fields: [
                    {
                        name: `${embedStyles.icons.user}**Usuário Desmutado**`,
                        value: `${data.target.tag}\n${embedStyles.format.code(data.target.id)}`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.shield}**Moderador**`,
                        value: `${data.moderator.tag}\n${embedStyles.format.code(data.moderator.id)}`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.info}**Status da DM**`,
                        value: data.dmSent ? `${embedStyles.icons.success} Enviada` : `${embedStyles.icons.error} Não enviada`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.info}**Tempo Poupado**`,
                        value: `${embedStyles.formatDuration(data.timeRemaining)}\n${embedStyles.format.italic('Tempo restante do mute original')}`,
                        inline: false
                    },
                    {
                        name: `${embedStyles.icons.info}**Motivo da Remoção**`,
                        value: embedStyles.format.code(data.reason),
                        inline: false
                    }
                ],
                timestamp: new Date().toISOString(),
                footer: {
                    text: 'Nodex | Moderação • Sistema de Logs Premium',
                    icon_url: guild.iconURL()
                },
                thumbnail: {
                    url: data.target.displayAvatarURL({ dynamic: true })
                }
            };

            await logChannel.send({ embeds: [logEmbed] });
        } catch (error) {
            guild.client.logger.error('Erro ao enviar log de unmute:', error);
        }
    },

    cooldown: 3,
    category: 'moderacao',
    examples: [
        '/unmute usuário:@usuário',
        '/unmute usuário:@usuário motivo:Comportamento melhorado',
        '/unmute usuário:@usuário motivo:Pedido de desculpas aceito silencioso:true'
    ],

    // Função para comandos de texto
    executeText: async function(message, args) {
        // Converter para interaction-like object para reutilizar código
        const fakeInteraction = {
            guild: message.guild,
            member: message.member,
            user: message.author,
            channel: message.channel,
            client: message.client,
            options: {
                getUser: () => {
                    const mention = args[0];
                    if (!mention) return null;
                    
                    if (mention.startsWith('<@') && mention.endsWith('>')) {
                        const userId = mention.slice(2, -1).replace('!', '');
                        return message.guild.members.cache.get(userId)?.user;
                    }
                    
                    if (/^\d+$/.test(mention)) {
                        return message.guild.members.cache.get(mention)?.user;
                    }
                    
                    return message.guild.members.cache.find(member =>
                        member.user.username.toLowerCase().includes(mention.toLowerCase()) ||
                        member.displayName.toLowerCase().includes(mention.toLowerCase())
                    )?.user;
                },
                getString: () => {
                    return args.slice(1).join(' ') || 'Nenhum motivo fornecido';
                }
            },
            reply: async (content) => {
                if (typeof content === 'string') {
                    return await message.reply(content);
                }
                return await message.reply(content);
            },
            deferReply: async () => {
                return Promise.resolve();
            },
            editReply: async (content) => {
                return await message.reply(content);
            },
            deferred: false
        };

        // Verificar argumentos básicos
        if (args.length === 0) {
            return await message.reply(`**Uso:** \`!unmute @usuário [motivo]\``);
        }

        // Chamar função execute principal
        return await this.execute(fakeInteraction);
    }
};
