/**
 * ========================================
 * GERENCIADOR DE BANCO DE DADOS SEGURO
 * Wrapper seguro com proteção contra SQL Injection
 * ========================================
 */

const Database = require('better-sqlite3');
const validator = require('validator');
const crypto = require('crypto');
const winston = require('winston');
const path = require('path');

class SecureDatabaseManager {
    constructor(dbPath) {
        this.dbPath = dbPath || process.env.DATABASE_PATH || './database/moderacao.db';
        this.db = null;
        this.isInitialized = false;
        this.setupLogger();
        this.preparedStatements = new Map();
    }

    /**
     * Configurar logger de banco de dados
     */
    setupLogger() {
        this.dbLogger = winston.createLogger({
            level: 'info',
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.errors({ stack: true }),
                winston.format.json()
            ),
            transports: [
                new winston.transports.File({ 
                    filename: path.join(__dirname, '../logs/database.log'),
                    maxsize: 5242880, // 5MB
                    maxFiles: 5
                })
            ]
        });
    }

    /**
     * Validar entrada para prevenir SQL injection
     */
    validateInput(value, type = 'string') {
        if (value === null || value === undefined) {
            return null;
        }

        switch (type) {
            case 'guildId':
                if (!validator.isLength(String(value), { min: 17, max: 19 }) || !validator.isNumeric(String(value))) {
                    throw new Error('Guild ID inválido');
                }
                return String(value);

            case 'userId':
                if (!validator.isLength(String(value), { min: 17, max: 19 }) || !validator.isNumeric(String(value))) {
                    throw new Error('User ID inválido');
                }
                return String(value);

            case 'channelId':
                if (value && (!validator.isLength(String(value), { min: 17, max: 19 }) || !validator.isNumeric(String(value)))) {
                    throw new Error('Channel ID inválido');
                }
                return value ? String(value) : null;

            case 'roleId':
                if (value && (!validator.isLength(String(value), { min: 17, max: 19 }) || !validator.isNumeric(String(value)))) {
                    throw new Error('Role ID inválido');
                }
                return value ? String(value) : null;

            case 'boolean':
                return Boolean(value);

            case 'integer':
                const num = parseInt(value);
                if (isNaN(num)) {
                    throw new Error('Número inválido');
                }
                return num;

            case 'json':
                if (typeof value === 'object') {
                    return JSON.stringify(value);
                }
                if (typeof value === 'string') {
                    try {
                        JSON.parse(value);
                        return value;
                    } catch (e) {
                        throw new Error('JSON inválido');
                    }
                }
                throw new Error('Formato JSON inválido');

            case 'string':
            default:
                const str = String(value);
                if (str.length > 2000) {
                    throw new Error('String muito longa');
                }
                // Remover caracteres perigosos
                return str.replace(/[\x00-\x1F\x7F]/g, '');
        }
    }

    /**
     * Executar query preparada com validação
     */
    async executeQuery(query, params = [], types = []) {
        try {
            // Validar parâmetros
            const validatedParams = params.map((param, index) => {
                const type = types[index] || 'string';
                return this.validateInput(param, type);
            });

            // Log da query (sem parâmetros sensíveis)
            this.dbLogger.info('Database Query', {
                query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
                paramCount: validatedParams.length,
                timestamp: new Date().toISOString()
            });

            const stmt = this.db.prepare(query);
            const rows = stmt.all(...validatedParams);
            return rows || [];
        } catch (err) {
            this.dbLogger.error('Database Error', {
                error: err.message,
                query: query.substring(0, 100),
                timestamp: new Date().toISOString()
            });
            throw new Error('Erro no banco de dados');
        }
    }

    /**
     * Executar query que retorna uma única linha
     */
    async getOne(query, params = [], types = []) {
        const rows = await this.executeQuery(query, params, types);
        return rows.length > 0 ? rows[0] : null;
    }

    /**
     * Executar query de modificação (INSERT, UPDATE, DELETE)
     */
    async executeModification(query, params = [], types = []) {
        try {
            const validatedParams = params.map((param, index) => {
                const type = types[index] || 'string';
                return this.validateInput(param, type);
            });

            this.dbLogger.info('Database Modification', {
                query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
                paramCount: validatedParams.length,
                timestamp: new Date().toISOString()
            });

            const stmt = this.db.prepare(query);
            const result = stmt.run(...validatedParams);

            return {
                lastID: result.lastInsertRowid,
                changes: result.changes
            };
        } catch (err) {
            this.dbLogger.error('Database Modification Error', {
                error: err.message,
                query: query.substring(0, 100),
                timestamp: new Date().toISOString()
            });
            throw new Error('Erro ao modificar banco de dados');
        }
    }

    /**
     * Obter configuração de guild de forma segura
     */
    async getGuildConfig(guildId) {
        const query = `
            SELECT * FROM guild_configs
            WHERE guild_id = ?
            LIMIT 1
        `;

        return await this.getOne(query, [guildId], ['guildId']);
    }

    /**
     * Salvar configuração de guild de forma segura
     */
    async saveGuildConfig(guildId, config) {
        // Validar guild ID
        const validGuildId = this.validateInput(guildId, 'guildId');
        
        // Validar e sanitizar configuração
        const validConfig = this.validateConfigObject(config);

        const query = `
            INSERT OR REPLACE INTO guild_configs
            (guild_id, prefix, language, timezone, auto_mod_enabled, anti_raid_enabled,
             log_channel_id, mod_log_channel_id, welcome_channel_id, mute_role_id,
             quarantine_role_id, mod_roles, admin_roles, ignored_channels, settings, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `;

        const params = [
            validGuildId,
            validConfig.prefix || '!',
            validConfig.language || 'pt-BR',
            validConfig.timezone || 'America/Sao_Paulo',
            validConfig.auto_mod_enabled || false,
            validConfig.anti_raid_enabled || false,
            validConfig.log_channel_id,
            validConfig.mod_log_channel_id,
            validConfig.welcome_channel_id,
            validConfig.mute_role_id,
            validConfig.quarantine_role_id,
            validConfig.mod_roles || '[]',
            validConfig.admin_roles || '[]',
            validConfig.ignored_channels || '[]',
            validConfig.settings || '{}'
        ];

        const types = [
            'guildId', 'string', 'string', 'string', 'boolean', 'boolean',
            'channelId', 'channelId', 'channelId', 'roleId', 'roleId',
            'json', 'json', 'json', 'json'
        ];

        return await this.executeModification(query, params, types);
    }

    /**
     * Validar objeto de configuração
     */
    validateConfigObject(config) {
        if (!config || typeof config !== 'object') {
            throw new Error('Configuração inválida');
        }

        const validConfig = {};

        // Validar campos específicos
        if (config.prefix) {
            validConfig.prefix = this.validateInput(config.prefix, 'string').substring(0, 5);
        }

        if (config.language) {
            const allowedLanguages = ['pt-BR', 'en-US', 'es-ES'];
            validConfig.language = allowedLanguages.includes(config.language) ? config.language : 'pt-BR';
        }

        if (config.timezone) {
            validConfig.timezone = this.validateInput(config.timezone, 'string');
        }

        // Validar booleans
        ['auto_mod_enabled', 'anti_raid_enabled'].forEach(field => {
            if (config[field] !== undefined) {
                validConfig[field] = this.validateInput(config[field], 'boolean');
            }
        });

        // Validar IDs de canais e roles
        ['log_channel_id', 'mod_log_channel_id', 'welcome_channel_id'].forEach(field => {
            if (config[field]) {
                validConfig[field] = this.validateInput(config[field], 'channelId');
            }
        });

        ['mute_role_id', 'quarantine_role_id'].forEach(field => {
            if (config[field]) {
                validConfig[field] = this.validateInput(config[field], 'roleId');
            }
        });

        // Validar arrays JSON
        ['mod_roles', 'admin_roles', 'ignored_channels'].forEach(field => {
            if (config[field]) {
                validConfig[field] = this.validateInput(config[field], 'json');
            }
        });

        // Validar settings
        if (config.settings) {
            validConfig.settings = this.validateInput(config.settings, 'json');
        }

        return validConfig;
    }

    /**
     * Registrar log de moderação de forma segura
     */
    async addModerationLog(guildId, userId, moderatorId, action, reason, duration = null) {
        const query = `
            INSERT INTO moderation_logs
            (guild_id, user_id, moderator_id, action, reason, duration, created_at)
            VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `;

        const params = [guildId, userId, moderatorId, action, reason, duration];
        const types = ['guildId', 'userId', 'userId', 'string', 'string', 'integer'];

        return await this.executeModification(query, params, types);
    }

    /**
     * Obter histórico de moderação de forma segura
     */
    async getModerationHistory(guildId, limit = 50) {
        const query = `
            SELECT * FROM moderation_logs 
            WHERE guild_id = ? 
            ORDER BY created_at DESC 
            LIMIT ?
        `;

        return await this.executeQuery(query, [guildId, limit], ['guildId', 'integer']);
    }

    /**
     * Inicializar conexão segura
     */
    async initialize(originalDb) {
        this.db = originalDb;
        this.isInitialized = true;

        // Configurar pragmas de segurança
        this.db.pragma('foreign_keys = ON');
        this.db.pragma('journal_mode = WAL');
        this.db.pragma('synchronous = FULL');

        this.dbLogger.info('Secure Database Manager initialized');
    }

    /**
     * Fechar conexão
     */
    async close() {
        if (this.db) {
            try {
                this.db.close();
                this.dbLogger.info('Database connection closed');
            } catch (err) {
                this.dbLogger.error('Error closing database', { error: err.message });
            }
        }
    }
}

module.exports = SecureDatabaseManager;
