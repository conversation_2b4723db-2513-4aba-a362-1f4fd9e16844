/**
 * ========================================
 * EVENTO: MESSAGE UPDATE
 * Monitora mensagens editadas
 * ========================================
 */

const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'messageUpdate',
    async execute(oldMessage, newMessage, client) {
        // Ignorar mensagens de bots
        if (newMessage.author?.bot) return;

        // Ignorar mensagens em DM
        if (!newMessage.guild) return;

        // Ignorar mensagens sem autor
        if (!newMessage.author || !oldMessage.author) return;

        // Ignorar se o conteúdo não mudou (pode ser embed update)
        if (oldMessage.content === newMessage.content) return;

        // Ignorar se ambas as mensagens estão vazias
        if (!oldMessage.content && !newMessage.content) return;

        try {
            // Obter configuração do servidor do cache
            const guildConfig = client.guildConfigs?.get(newMessage.guild.id);

            // Verificar se logs estão habilitados
            if (!guildConfig?.log_enabled) {
                console.log(`📝 [MESSAGE UPDATE] Logs desabilitados para ${newMessage.guild.name}`);
                return;
            }

            // Verificar se canal de logs está configurado
            if (!guildConfig?.log_channel_id && !guildConfig?.log_channel) {
                console.log(`📝 [MESSAGE UPDATE] Canal de logs não configurado para ${newMessage.guild.name}`);
                return;
            }

            // Tentar obter canal de logs (priorizar log_channel_id)
            const logChannelId = guildConfig.log_channel_id || guildConfig.log_channel;
            const logChannel = newMessage.guild.channels.cache.get(logChannelId);

            if (!logChannel) {
                console.log(`📝 [MESSAGE UPDATE] Canal de logs não encontrado: ${logChannelId}`);
                return;
            }

            console.log(`📝 [MESSAGE UPDATE] Enviando log para #${logChannel.name} em ${newMessage.guild.name}`);

            // Criar embed de log
            const editEmbed = new EmbedBuilder()
                .setColor('#ffa502')
                .setTitle(`✏️ Mensagem Editada - ${newMessage.author.username}`)
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação - Sistema de Logs' });

            // Informações do usuário
            editEmbed.addFields(
                { name: '👤 Usuário', value: `${newMessage.author.tag} (${newMessage.author.id})`, inline: true },
                { name: '📺 Canal', value: `${newMessage.channel} (${newMessage.channel.name})`, inline: true },
                { name: '📅 Editado em', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
            );

            // Avatar do usuário
            editEmbed.setThumbnail(newMessage.author.displayAvatarURL());

            // Conteúdo original
            if (oldMessage.content) {
                const oldContent = oldMessage.content.length > 1024 
                    ? oldMessage.content.substring(0, 1021) + '...' 
                    : oldMessage.content;
                
                editEmbed.addFields({
                    name: '📝 Conteúdo Original',
                    value: `\`\`\`${oldContent}\`\`\``,
                    inline: false
                });
            } else {
                editEmbed.addFields({
                    name: '📝 Conteúdo Original',
                    value: '*Conteúdo original não disponível*',
                    inline: false
                });
            }

            // Novo conteúdo
            if (newMessage.content) {
                const newContent = newMessage.content.length > 1024 
                    ? newMessage.content.substring(0, 1021) + '...' 
                    : newMessage.content;
                
                editEmbed.addFields({
                    name: '📝 Novo Conteúdo',
                    value: `\`\`\`${newContent}\`\`\``,
                    inline: false
                });
            }

            // Link para a mensagem
            editEmbed.addFields({
                name: '🔗 Link da Mensagem',
                value: `[Ir para mensagem](https://discord.com/channels/${newMessage.guild.id}/${newMessage.channel.id}/${newMessage.id})`,
                inline: true
            });

            // ID da mensagem para referência
            editEmbed.addFields({
                name: '🆔 ID da Mensagem',
                value: `\`${newMessage.id}\``,
                inline: true
            });

            // Diferenças destacadas (se possível)
            if (oldMessage.content && newMessage.content) {
                const oldWords = oldMessage.content.split(' ');
                const newWords = newMessage.content.split(' ');
                
                let changes = [];
                
                // Detectar palavras removidas
                const removedWords = oldWords.filter(word => !newWords.includes(word));
                if (removedWords.length > 0) {
                    changes.push(`**Removido:** ${removedWords.slice(0, 5).join(', ')}${removedWords.length > 5 ? '...' : ''}`);
                }
                
                // Detectar palavras adicionadas
                const addedWords = newWords.filter(word => !oldWords.includes(word));
                if (addedWords.length > 0) {
                    changes.push(`**Adicionado:** ${addedWords.slice(0, 5).join(', ')}${addedWords.length > 5 ? '...' : ''}`);
                }
                
                if (changes.length > 0) {
                    editEmbed.addFields({
                        name: '🔍 Principais Mudanças',
                        value: changes.join('\n'),
                        inline: false
                    });
                }
            }

            await logChannel.send({ embeds: [editEmbed] });

            client.logger.info(`Mensagem editada logada: ${newMessage.author.tag} em #${newMessage.channel.name}`);

        } catch (error) {
            client.logger.error('Erro ao logar mensagem editada:', error);
        }
    }
};
