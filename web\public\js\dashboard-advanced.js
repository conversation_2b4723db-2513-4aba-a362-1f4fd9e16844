/**
 * ========================================
 * DASHBOARD AVANÇADO - JAVASCRIPT SUPREMO
 * Interações fluidas, validações e UX perfeita
 * ========================================
 */

console.log('🚀 [DASHBOARD] Arquivo dashboard-advanced.js carregado!');
console.log('🚀 [DASHBOARD] Timestamp:', new Date().toISOString());

class DashboardManager {
    constructor() {
        this.currentSection = 'geral';
        this.unsavedChanges = false;
        this.commandStates = {};

        // Extrair guildId da URL - pode estar em /dashboard/:guildId ou ser passado via data attribute
        const pathParts = window.location.pathname.split('/');
        this.guildId = pathParts[pathParts.length - 1];

        console.log('🔧 [DASHBOARD] URL atual:', window.location.pathname);
        console.log('🔧 [DASHBOARD] Partes da URL:', pathParts);
        console.log('🔧 [DASHBOARD] Guild ID extraído da URL:', this.guildId);

        // Se não há guildId na URL (página /dashboard), tentar obter do elemento
        if (this.guildId === 'dashboard' || !this.guildId) {
            const guildElement = document.querySelector('[data-guild-id]');
            console.log('🔧 [DASHBOARD] Elemento com data-guild-id:', guildElement);
            this.guildId = guildElement ? guildElement.dataset.guildId : null;
            console.log('🔧 [DASHBOARD] Guild ID do elemento:', this.guildId);
        }

        console.log('🔧 [DASHBOARD] Guild ID FINAL detectado:', this.guildId);
        this.config = {};

        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupFormHandlers();
        this.setupSaveButton();
        this.setupToggles();
        this.setupSliders();
        this.setupColorPickers();
        this.setupDynamicLists();
        this.loadConfiguration();
        this.setupAutoSave();
        this.setupKeyboardShortcuts();
        this.initializeNewModules();
        this.loadCommandStates();
        this.initializeBackupSystem();
    }

    // ===== NAVEGAÇÃO ===== //
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        const sections = document.querySelectorAll('.config-section');

        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const sectionId = item.dataset.section;
                this.switchSection(sectionId);
            });
        });

        // Navegação por hash
        if (window.location.hash) {
            const sectionId = window.location.hash.substring(1);
            this.switchSection(sectionId);
        }
    }

    switchSection(sectionId) {
        // Atualizar navegação
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

        // Atualizar seções
        document.querySelectorAll('.config-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(sectionId).classList.add('active');

        // Atualizar URL
        window.history.pushState(null, null, `#${sectionId}`);
        this.currentSection = sectionId;

        // Animação suave
        this.animateSection(sectionId);
    }

    animateSection(sectionId) {
        const section = document.getElementById(sectionId);
        const cards = section.querySelectorAll('.config-card');
        
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // ===== FORMULÁRIOS ===== //
    setupFormHandlers() {
        const inputs = document.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.markUnsaved();
                this.validateInput(input);
                this.updatePreview(input);
            });

            input.addEventListener('input', () => {
                this.markUnsaved();
                this.updatePreview(input);
            });
        });
    }

    validateInput(input) {
        const value = input.value;
        const name = input.name;
        
        // Validações específicas
        if (name === 'prefix' && value.length > 3) {
            this.showInputError(input, 'Prefixo deve ter no máximo 3 caracteres');
            return false;
        }
        
        if (name === 'join_threshold' && (value < 3 || value > 50)) {
            this.showInputError(input, 'Limite deve estar entre 3 e 50');
            return false;
        }
        
        if (input.type === 'number' && value < 0) {
            this.showInputError(input, 'Valor não pode ser negativo');
            return false;
        }

        this.clearInputError(input);
        return true;
    }

    showInputError(input, message) {
        this.clearInputError(input);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'input-error';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            color: #ff4757;
            font-size: 0.85rem;
            margin-top: 0.5rem;
            animation: fadeInUp 0.3s ease-out;
        `;
        
        input.parentNode.appendChild(errorDiv);
        input.style.borderColor = '#ff4757';
    }

    clearInputError(input) {
        const error = input.parentNode.querySelector('.input-error');
        if (error) {
            error.remove();
        }
        input.style.borderColor = '';
    }

    updatePreview(input) {
        const name = input.name;
        const value = input.value;

        // Preview em tempo real
        if (name === 'bot_color') {
            document.documentElement.style.setProperty('--primary-green', value);
        }
        
        if (name === 'welcome_message') {
            this.updateWelcomePreview(value);
        }
        
        if (name === 'ai_sensitivity') {
            this.updateSensitivityDescription(value);
        }
    }

    updateWelcomePreview(message) {
        // Criar preview da mensagem de boas-vindas
        const preview = document.getElementById('welcomePreview');
        if (preview) {
            const processedMessage = message
                .replace('{user}', '@Usuário')
                .replace('{server}', 'Seu Servidor');
            preview.innerHTML = processedMessage;
        }
    }

    updateSensitivityDescription(value) {
        const descriptions = {
            1: '🟢 Muito suave - Apenas ataques extremamente óbvios',
            2: '🟢 Suave - Ataques claros e diretos',
            3: '🟡 Normal - Balanceada, detecta a maioria dos problemas',
            4: '🔴 Rigorosa - Detecta até insinuações sutis',
            5: '🔴 Máxima - Extremamente sensível a qualquer negatividade'
        };
        
        const desc = document.getElementById('sensitivityDesc');
        if (desc) {
            desc.textContent = descriptions[value] || descriptions[3];
        }
    }

    // ===== TOGGLES E CONTROLES ===== //
    setupToggles() {
        const toggles = document.querySelectorAll('.toggle input');
        
        toggles.forEach(toggle => {
            toggle.addEventListener('change', () => {
                this.handleToggleChange(toggle);
                this.addToggleAnimation(toggle);
            });
        });
    }

    handleToggleChange(toggle) {
        const name = toggle.name;
        const checked = toggle.checked;
        
        // Lógica específica para cada toggle
        if (name === 'ai_moderation_enabled') {
            this.toggleAIFeatures(checked);
        }
        
        if (name === 'economy_enabled') {
            this.toggleEconomyFeatures(checked);
        }
        
        if (name === 'tickets_enabled') {
            this.toggleTicketFeatures(checked);
        }
    }

    addToggleAnimation(toggle) {
        const slider = toggle.nextElementSibling;
        slider.style.animation = 'pulse 0.3s ease-out';
        setTimeout(() => {
            slider.style.animation = '';
        }, 300);
    }

    toggleAIFeatures(enabled) {
        const aiCards = document.querySelectorAll('#ai .config-card:not(:first-child)');
        aiCards.forEach(card => {
            card.style.opacity = enabled ? '1' : '0.5';
            card.style.pointerEvents = enabled ? 'auto' : 'none';
        });
    }

    toggleEconomyFeatures(enabled) {
        const economyInputs = document.querySelectorAll('#economia input:not([name="economy_enabled"])');
        economyInputs.forEach(input => {
            input.disabled = !enabled;
        });
    }



    // ===== SLIDERS ===== //
    setupSliders() {
        const sliders = document.querySelectorAll('input[type="range"]');
        
        sliders.forEach(slider => {
            this.updateSliderBackground(slider);
            
            slider.addEventListener('input', () => {
                this.updateSliderBackground(slider);
                this.updatePreview(slider);
            });
        });
    }

    updateSliderBackground(slider) {
        const value = (slider.value - slider.min) / (slider.max - slider.min) * 100;
        slider.style.background = `linear-gradient(to right, var(--primary-green) 0%, var(--primary-green) ${value}%, var(--medium-gray) ${value}%, var(--medium-gray) 100%)`;
    }

    // ===== COLOR PICKERS ===== //
    setupColorPickers() {
        const colorPresets = document.querySelectorAll('.color-preset');
        
        colorPresets.forEach(preset => {
            preset.addEventListener('click', () => {
                const color = preset.dataset.color;
                const colorInput = document.getElementById('bot_color');
                colorInput.value = color;
                this.updatePreview(colorInput);
                this.markUnsaved();
            });
        });
    }

    // ===== LISTAS DINÂMICAS ===== //
    setupDynamicLists() {
        this.setupShopItems();
        this.setupLevelRewards();
    }

    setupShopItems() {
        const addButton = document.getElementById('addShopItem');
        if (addButton) {
            addButton.addEventListener('click', () => {
                this.addShopItem();
            });
        }

        // Configurar botões de remoção existentes
        document.querySelectorAll('.btn-remove-item').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.target.closest('.shop-item').remove();
                this.markUnsaved();
            });
        });
    }

    addShopItem() {
        const container = document.querySelector('.shop-items');
        const newItem = document.createElement('div');
        newItem.className = 'shop-item';
        newItem.innerHTML = `
            <input type="text" placeholder="Nome do item" name="shop_item_name[]">
            <input type="number" placeholder="Preço" name="shop_item_price[]" min="1">
            <select name="shop_item_type[]">
                <option value="role">Cargo</option>
                <option value="custom">Personalizado</option>
            </select>
            <button type="button" class="btn-remove-item">🗑️</button>
        `;
        
        container.appendChild(newItem);
        
        // Adicionar evento de remoção
        newItem.querySelector('.btn-remove-item').addEventListener('click', () => {
            newItem.remove();
            this.markUnsaved();
        });
        
        // Animação
        newItem.style.opacity = '0';
        newItem.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            newItem.style.transition = 'all 0.3s ease-out';
            newItem.style.opacity = '1';
            newItem.style.transform = 'translateY(0)';
        }, 10);
        
        this.markUnsaved();
    }

    setupLevelRewards() {
        const addButton = document.getElementById('addLevelReward');
        if (addButton) {
            addButton.addEventListener('click', () => {
                this.addLevelReward();
            });
        }

        // Configurar botões de remoção existentes
        document.querySelectorAll('.btn-remove-reward').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.target.closest('.level-reward').remove();
                this.markUnsaved();
            });
        });
    }

    addLevelReward() {
        const container = document.querySelector('.level-rewards');
        const newReward = document.createElement('div');
        newReward.className = 'level-reward';
        newReward.innerHTML = `
            <input type="number" placeholder="Nível" name="reward_level[]" min="1" max="100">
            <select name="reward_role[]">
                <option value="">Selecione um cargo</option>
                <!-- Roles serão carregados dinamicamente -->
            </select>
            <button type="button" class="btn-remove-reward">🗑️</button>
        `;
        
        container.appendChild(newReward);
        
        // Adicionar evento de remoção
        newReward.querySelector('.btn-remove-reward').addEventListener('click', () => {
            newReward.remove();
            this.markUnsaved();
        });
        
        this.markUnsaved();
    }

    // ===== SALVAMENTO INDIVIDUAL POR SEÇÃO ===== //
    setupSaveButton() {
        // Configurar botões de salvamento individual
        const saveSectionButtons = document.querySelectorAll('.btn-save-section');
        console.log(`🔧 [DASHBOARD] Encontrados ${saveSectionButtons.length} botões de salvamento`);

        saveSectionButtons.forEach((button, index) => {
            const section = button.dataset.section;
            console.log(`🔧 [DASHBOARD] Botão ${index + 1}: seção "${section}"`);

            button.addEventListener('click', (e) => {
                e.preventDefault();
                console.log(`🔧 [DASHBOARD] CLIQUE DETECTADO no botão da seção: ${section}`);
                console.log(`🔧 [DASHBOARD] Elemento do botão:`, button);
                console.log(`🔧 [DASHBOARD] Dataset do botão:`, button.dataset);

                try {
                    this.saveSectionConfiguration(section, button);
                } catch (error) {
                    console.error(`❌ [DASHBOARD] Erro ao salvar seção ${section}:`, error);
                }
            });
        });

        // Debug específico para verificação
        const verificationButton = document.querySelector('[data-section="verificacao"]');
        if (verificationButton) {
            console.log('✅ [DASHBOARD] Botão de verificação encontrado:', verificationButton);
        } else {
            console.error('❌ [DASHBOARD] Botão de verificação NÃO encontrado!');
        }
    }

    setupAutoSave() {
        // REMOVIDO: Auto-save automático que causava salvamentos globais indesejados
        // Agora cada módulo tem salvamento individual e manual
        console.log('🔧 [DASHBOARD] Auto-save automático DESABILITADO - usando salvamento manual por módulo');
    }

    async saveSectionConfiguration(sectionName, button) {
        console.log(`🚀 [DASHBOARD] === INÍCIO DO SALVAMENTO DA SEÇÃO ${sectionName.toUpperCase()} ===`);
        console.log(`🚀 [DASHBOARD] Botão recebido:`, button);
        console.log(`🚀 [DASHBOARD] Guild ID atual:`, this.guildId);

        // Verificar se temos um guildId
        if (!this.guildId) {
            console.error('❌ [DASHBOARD] Guild ID não encontrado');
            this.showToast('Erro: Servidor não identificado. Recarregue a página.', 'error');
            return;
        }

        console.log(`🔧 [DASHBOARD] Salvando seção ${sectionName} para guild ${this.guildId}`);

        // Validar apenas inputs da seção atual
        const sectionElement = document.getElementById(sectionName);
        const sectionInputs = sectionElement.querySelectorAll('input, select, textarea');

        let hasErrors = false;
        sectionInputs.forEach(input => {
            if (!this.validateInput(input)) {
                hasErrors = true;
            }
        });

        if (hasErrors) {
            this.showToast('Corrija os erros antes de salvar', 'error');
            return;
        }

        // Atualizar estado do botão
        this.updateSectionButtonState(button, 'saving');

        try {
            // Tratamento especial para seção de comandos
            if (sectionName === 'comandos') {
                this.commandStates = this.collectCommandStates();
                const success = await this.saveCommandStates();

                if (success) {
                    this.updateSectionButtonState(button, 'saved');
                    this.showToast('Estados dos comandos salvos!', 'success');

                    setTimeout(() => {
                        this.updateSectionButtonState(button, 'normal');
                    }, 3000);
                } else {
                    this.updateSectionButtonState(button, 'normal');
                    this.showToast('Erro ao salvar estados dos comandos', 'error');
                }
                return;
            }

            // Tratamento especial para seção de verificação
            if (sectionName === 'verificacao') {
                console.log('🛡️ [VERIFICATION] === PROCESSANDO SEÇÃO DE VERIFICAÇÃO ===');

                const verificationConfig = this.collectVerificationData();
                console.log('🛡️ [VERIFICATION] Dados coletados:', verificationConfig);
                console.log('🛡️ [VERIFICATION] Número de configurações:', Object.keys(verificationConfig.settings || {}).length);

                // Validação específica para verificação
                if (verificationConfig.settings.verification_enabled) {
                    if (!verificationConfig.settings.verification_channel_id) {
                        this.updateSectionButtonState(button, 'normal');
                        this.showToast('❌ Canal de verificação é obrigatório quando o sistema está ativo', 'error');
                        return;
                    }
                    if (!verificationConfig.settings.verified_role_id) {
                        this.updateSectionButtonState(button, 'normal');
                        this.showToast('❌ Cargo de verificado é obrigatório quando o sistema está ativo', 'error');
                        return;
                    }
                }

                // Usar endpoint específico para verificação
                const response = await fetch(`/api/guild/${this.guildId}/config/verificacao`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(verificationConfig)
                });

                const result = await response.json();

                if (result.success) {
                    this.updateSectionButtonState(button, 'saved');
                    this.showToast('✅ Configurações de verificação salvas e aplicadas em tempo real!', 'success');

                    // Atualizar preview
                    if (typeof updateVerificationPreview === 'function') {
                        updateVerificationPreview();
                    }
                    if (typeof updatePreviewTimestamp === 'function') {
                        updatePreviewTimestamp();
                    }

                    setTimeout(() => {
                        this.updateSectionButtonState(button, 'normal');
                    }, 3000);
                } else {
                    this.updateSectionButtonState(button, 'normal');
                    this.showToast('❌ ' + (result.error || 'Erro ao salvar configurações de verificação'), 'error');
                }
                return;
            }

            const sectionConfig = this.collectSectionData(sectionName);
            console.log(`🔧 [DASHBOARD] Dados coletados:`, sectionConfig);

            const url = `/api/guild/${this.guildId}/config/${sectionName}`;
            console.log(`🔧 [DASHBOARD] URL da requisição:`, url);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(sectionConfig)
            });

            console.log(`🔧 [DASHBOARD] Status da resposta:`, response.status);
            console.log(`🔧 [DASHBOARD] Headers da resposta:`, response.headers);

            if (!response.ok) {
                console.error(`❌ [DASHBOARD] Erro HTTP ${response.status}:`, response.statusText);
                this.updateSectionButtonState(button, 'normal');
                this.showToast(`Erro HTTP ${response.status}: ${response.statusText}`, 'error');
                return;
            }

            const result = await response.json();
            console.log(`🔧 [DASHBOARD] Resultado:`, result);

            if (result.success) {
                this.updateSectionButtonState(button, 'saved');
                this.showToast(`Configurações de ${this.getSectionDisplayName(sectionName)} salvas!`, 'success');

                // Mostrar mensagem específica para algumas seções
                this.showSectionSpecificMessage(sectionName, sectionConfig);

                // Resetar botão após 3 segundos
                setTimeout(() => {
                    this.updateSectionButtonState(button, 'normal');
                }, 3000);
            } else {
                console.error(`❌ [DASHBOARD] Falha no salvamento:`, result);
                this.updateSectionButtonState(button, 'normal');
                this.showToast(result.error || 'Erro ao salvar configurações', 'error');
            }
        } catch (error) {
            console.error('❌ [DASHBOARD] Erro ao salvar seção:', error);
            this.updateSectionButtonState(button, 'normal');
            this.showToast('Erro de conexão', 'error');
        }
    }

    collectSectionData(sectionName) {
        const sectionElement = document.getElementById(sectionName);
        if (!sectionElement) {
            console.error(`❌ [DASHBOARD] Seção ${sectionName} não encontrada`);
            return { settings: {} };
        }

        const inputs = sectionElement.querySelectorAll('input, select, textarea');
        const config = { settings: {} };

        console.log(`🔧 [DASHBOARD] === COLETA ISOLADA DA SEÇÃO ${sectionName.toUpperCase()} ===`);
        console.log(`🔧 [DASHBOARD] ${inputs.length} inputs encontrados na seção`);

        // Obter lista de configurações permitidas para esta seção específica
        const allowedSettings = this.getSectionAllowedSettings(sectionName);
        console.log(`🔧 [DASHBOARD] Configurações permitidas para ${sectionName}:`, allowedSettings);

        let processedCount = 0;
        let ignoredCount = 0;

        inputs.forEach(input => {
            if (!input.name) return;

            // VALIDAÇÃO CRÍTICA: Verificar se o input pertence à seção ANTES de processar
            if (!this.isSettingForSection(input.name, sectionName)) {
                console.log(`🚫 [DASHBOARD] IGNORANDO ${input.name} - NÃO PERTENCE À SEÇÃO ${sectionName}`);
                ignoredCount++;
                return;
            }

            let value;
            if (input.type === 'checkbox') {
                value = input.checked;
                console.log(`✅ [DASHBOARD] Checkbox ${input.name}: ${value}`);
            } else if (input.type === 'radio') {
                if (input.checked) {
                    value = input.value;
                    console.log(`📻 [DASHBOARD] Radio ${input.name}: ${value}`);
                } else {
                    return;
                }
            } else if (input.name.endsWith('[]')) {
                const arrayKey = input.name.slice(0, -2);
                if (!config.settings[arrayKey]) config.settings[arrayKey] = [];
                if (input.value) {
                    config.settings[arrayKey].push(input.value);
                }
                console.log(`📋 [DASHBOARD] Array ${arrayKey}: ${input.value}`);
                processedCount++;
                return;
            } else {
                value = input.value;
                console.log(`📝 [DASHBOARD] Input ${input.name}: ${value}`);
            }

            config.settings[input.name] = value;
            processedCount++;
        });

        console.log(`🔧 [DASHBOARD] === RESULTADO DA COLETA ===`);
        console.log(`✅ [DASHBOARD] Processados: ${processedCount} inputs`);
        console.log(`🚫 [DASHBOARD] Ignorados: ${ignoredCount} inputs`);
        console.log(`🔧 [DASHBOARD] Dados finais da seção ${sectionName}:`, config);
        console.log(`🔧 [DASHBOARD] === FIM DA COLETA ===`);

        return config;
    }

    // Obter configurações permitidas para uma seção específica
    getSectionAllowedSettings(sectionName) {
        const sectionMappings = {
            'geral': ['prefix', 'language', 'timezone', 'bot_color', 'embed_color'],
            'moderacao': ['auto_mod_enabled', 'anti_spam_enabled', 'anti_links_enabled', 'anti_caps_enabled', 'anti_mentions_enabled', 'max_mentions', 'max_message_length', 'spam_detection', 'link_filtering', 'caps_detection', 'profanity_filter'],
            'ai': ['ai_moderation_enabled', 'ai_sensitivity', 'second_chance_enabled', 'ai_dm_user', 'ai_public_message', 'ai_timeout_duration', 'toxicity_threshold', 'ai_auto_delete', 'ai_auto_timeout'],
            'antiraid': ['anti_raid_enabled', 'max_joins_per_minute', 'min_account_age', 'quarantine_enabled', 'quarantine_duration', 'join_limit'],
            'logs': ['log_enabled', 'log_channel', 'log_channel_id', 'general_log_channel', 'ai_log_channel', 'raid_log_channel', 'mod_log_enabled', 'mod_log_channel', 'mod_log_channel_id', 'message_log_enabled', 'join_leave_log_enabled'],
            'comandos': ['cmd_', 'mod_role_required', 'admin_only_config'], // cmd_ prefix para comandos
            'cargos': ['admin_role', 'moderator_role', 'helper_role', 'quarantine_role', 'verified_role', 'mute_role_id'],
            'verificacao': [
                'verification_enabled', 'verification_method', 'verification_channel_id', 'verified_role_id', 'unverified_role_id',
                'verification_timeout_minutes', 'verification_rules_text', 'verification_captcha_type', 'verification_dm_enabled',
                'verification_log_enabled', 'verification_auto_kick', 'verification_kick_time', 'combined_captcha', 'combined_rules',
                'verification_ip_check', 'verification_max_attempts', 'verification_anti_bot', 'welcome_message'
            ],
            'analytics': ['analytics_enabled', 'analytics_retention_days', 'analytics_detailed_logs', 'track_messages', 'track_members', 'track_commands'],
            'backup': ['backup_enabled', 'backup_interval', 'backup_retention', 'backup_include_messages', 'backup_include_attachments', 'backup_compression']
        };

        return sectionMappings[sectionName] || [];
    }

    // Verificar se uma configuração pertence à seção específica - VALIDAÇÃO RIGOROSA
    isSettingForSection(settingName, sectionName) {
        const allowedSettings = this.getSectionAllowedSettings(sectionName);

        // Verificação rigorosa: o setting deve estar EXATAMENTE na lista ou começar com um prefixo permitido
        const isAllowed = allowedSettings.some(setting => {
            if (setting.endsWith('_')) {
                // Para prefixos como 'cmd_'
                return settingName.startsWith(setting);
            }
            // Correspondência exata
            return settingName === setting;
        });

        if (!isAllowed) {
            console.warn(`🚫 [DASHBOARD] CONFIGURAÇÃO REJEITADA: ${settingName} não é permitida na seção ${sectionName}`);
            console.warn(`🚫 [DASHBOARD] Configurações permitidas:`, allowedSettings);
        }

        return isAllowed;
    }

    updateSectionButtonState(button, state) {
        button.classList.remove('saving', 'saved');

        const icon = button.querySelector('i');
        const text = button.querySelector('span') || button.childNodes[button.childNodes.length - 1];

        switch (state) {
            case 'saving':
                button.classList.add('saving');
                button.disabled = true;
                icon.className = 'fas fa-spinner fa-spin';
                if (text.textContent) {
                    text.textContent = text.textContent.replace(/Salvar/, 'Salvando');
                }
                break;

            case 'saved':
                button.classList.add('saved');
                button.disabled = false;
                icon.className = 'fas fa-check';
                if (text.textContent) {
                    text.textContent = text.textContent.replace(/Salvando/, 'Salvo');
                }
                break;

            case 'normal':
            default:
                button.disabled = false;
                // Usar sempre o ícone padrão de save para todos os botões
                icon.className = 'fas fa-save';
                if (text.textContent) {
                    text.textContent = text.textContent.replace(/Salvando|Salvo/, 'Salvar');
                }
                break;
        }
    }

    getSectionDisplayName(sectionName) {
        const names = {
            'geral': 'Configurações Gerais',
            'moderacao': 'Sistema de Moderação',
            'ai': 'IA & Auto-Moderação',
            'antiraid': 'Sistema Anti-Raid',
            'logs': 'Logs & Auditoria',
            'comandos': 'Gerenciamento de Comandos',
            'cargos': 'Cargos & Permissões',
            'verificacao': 'Sistema de Verificação',
            'tickets': 'Sistema de Tickets',
            'analytics': 'Analytics',
            'backup': 'Backup & Restore'
        };
        return names[sectionName] || sectionName;
    }

    showSectionSpecificMessage(sectionName, config) {
        setTimeout(() => {
            switch (sectionName) {
                case 'geral':
                    this.showToast('Configurações básicas atualizadas!', 'success');
                    break;
                case 'moderacao':
                    if (config.settings.auto_mod_enabled) {
                        this.showToast('Sistema de moderação ativado! Seu servidor está protegido', 'success');
                    }
                    break;
                case 'ai':
                    if (config.settings.ai_moderation_enabled) {
                        this.showToast('IA de moderação ativada! Protegendo seu servidor 24/7', 'success');
                    }
                    break;
                case 'antiraid':
                    if (config.settings.anti_raid_enabled) {
                        this.showToast('Sistema anti-raid ativado! Fortaleza digital ativa', 'success');
                    }
                    break;
                case 'logs':
                    this.showToast('Sistema de logs configurado! Monitoramento ativo', 'success');
                    break;
                case 'comandos':
                    this.showToast('Configurações de comandos atualizadas!', 'success');
                    break;
                case 'cargos':
                    this.showToast('Sistema de permissões configurado!', 'success');
                    break;
                case 'verificacao':
                    if (config.settings.verification_enabled) {
                        this.showToast('Sistema de verificação ativado! Servidor protegido contra invasões', 'success');
                    } else {
                        this.showToast('Configurações de verificação salvas!', 'success');
                    }
                    break;
                case 'tickets':
                    if (config.settings.tickets_enabled && config.settings.ticket_channel) {
                        this.showToast('Painel de tickets criado no canal selecionado!', 'success');
                    }
                    break;

                case 'analytics':
                    if (config.settings.analytics_enabled) {
                        this.showToast('Analytics ativado! Estatísticas em tempo real', 'success');
                    }
                    break;
                case 'backup':
                    this.showToast('Sistema de backup configurado! Dados protegidos', 'success');
                    break;
                default:
                    this.showToast('Configurações aplicadas em tempo real!', 'success');
            }
        }, 1500);
    }

    // REMOVIDO: autoSave() function que causava salvamentos globais indesejados
    // Agora cada módulo usa saveSectionConfiguration() para salvamento individual

    collectFormData() {
        const config = {
            settings: {}
        };

        // Coletar todos os inputs, selects e textareas
        const inputs = document.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            if (!input.name) return;

            let value;
            if (input.type === 'checkbox') {
                value = input.checked;
            } else if (input.type === 'radio') {
                if (input.checked) {
                    value = input.value;
                } else {
                    return; // Skip unchecked radio buttons
                }
            } else if (input.name.endsWith('[]')) {
                const arrayKey = input.name.slice(0, -2);
                if (!config.settings[arrayKey]) config.settings[arrayKey] = [];
                if (input.value) {
                    config.settings[arrayKey].push(input.value);
                }
                return;
            } else {
                value = input.value;
            }

            // Determinar se é configuração básica ou avançada
            const basicSettings = ['prefix', 'language', 'timezone'];

            if (basicSettings.includes(input.name)) {
                config[input.name] = value;
            } else {
                config.settings[input.name] = value;
            }
        });

        return config;
    }

    validateAllInputs() {
        const inputs = document.querySelectorAll('input, select, textarea');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validateInput(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    async loadConfiguration() {
        if (!this.guildId) {
            console.log('🔧 [DASHBOARD] Sem guildId, pulando carregamento de configurações');
            return;
        }

        try {
            console.log(`🔧 [DASHBOARD] Carregando configurações para guild ${this.guildId}`);
            const response = await fetch(`/api/guild/${this.guildId}/config`);
            const config = await response.json();

            console.log(`🔧 [DASHBOARD] Configurações carregadas:`, config);
            this.config = config;
            this.populateForm(config);
        } catch (error) {
            console.error('❌ [DASHBOARD] Erro ao carregar configurações:', error);
        }
    }

    populateForm(config) {
        Object.keys(config).forEach(key => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = config[key];
                } else {
                    element.value = config[key];
                }
                this.updatePreview(element);
            }
        });
    }

    // ===== UTILITÁRIOS ===== //
    markUnsaved() {
        // MODIFICADO: Não mais usado para auto-save global
        // Apenas para indicação visual de mudanças pendentes
        this.unsavedChanges = true;
        console.log('🔧 [DASHBOARD] Mudanças detectadas - use o botão de salvar do módulo específico');
        // Removido: this.updateSaveButton() - não há mais botão global
    }

    updateSaveButton() {
        const saveButton = document.getElementById('saveConfig');
        if (saveButton) {
            if (this.unsavedChanges) {
                saveButton.innerHTML = '<i class="fas fa-save"></i> Salvar Alterações';
                saveButton.style.animation = 'pulse 2s infinite';
            } else {
                saveButton.innerHTML = '<i class="fas fa-check"></i> Salvo';
                saveButton.style.animation = '';
            }
        }
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = show ? 'flex' : 'none';
        }
    }

    showToast(message, type = 'success') {
        const toast = document.getElementById(`${type}Toast`);
        if (toast) {
            toast.querySelector('span').textContent = message;
            toast.classList.add('show');
            
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+S para salvar
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveConfiguration();
            }
            
            // Ctrl+Z para desfazer (futuro)
            if (e.ctrlKey && e.key === 'z') {
                e.preventDefault();
                // Implementar undo
            }
        });
    }

    /**
     * Inicializar funcionalidades específicas dos novos módulos
     */
    initializeNewModules() {
        // Slider de sensibilidade da IA
        this.initializeSensitivitySlider();

        // Color picker presets
        this.initializeColorPresets();

        // Backup actions
        this.initializeBackupActions();

        // Command toggles
        this.initializeCommandToggles();

        // Inicializar módulo de Verificação
        this.initializeVerification();
    }

    initializeSensitivitySlider() {
        const slider = document.getElementById('ai_sensitivity');
        const description = document.getElementById('sensitivityDesc');

        if (slider && description) {
            const descriptions = {
                1: 'Muito suave - Apenas ataques extremamente óbvios',
                2: 'Suave - Detecta ataques claros mas ignora discussões normais',
                3: 'Moderada - Balanceada, detecta ataques mas permite debates',
                4: 'Rigorosa - Detecta ataques sutis e linguagem agressiva',
                5: 'Máxima - Detecta qualquer sinal de toxicidade'
            };

            slider.addEventListener('input', (e) => {
                const value = e.target.value;
                description.textContent = descriptions[value];
            });
        }
    }

    initializeColorPresets() {
        const colorInput = document.getElementById('bot_color');
        const presets = document.querySelectorAll('.color-preset');

        presets.forEach(preset => {
            preset.addEventListener('click', () => {
                const color = preset.dataset.color;
                if (colorInput) {
                    colorInput.value = color;
                    this.updatePreview(colorInput);
                    this.markUnsaved();
                }
            });
        });
    }

    initializeBackupActions() {
        const createBackupBtn = document.getElementById('createBackup');
        const downloadBackupBtn = document.getElementById('downloadBackup');
        const restoreBackupBtn = document.getElementById('restoreBackup');

        if (createBackupBtn) {
            createBackupBtn.addEventListener('click', () => {
                this.createBackup();
            });
        }

        if (downloadBackupBtn) {
            downloadBackupBtn.addEventListener('click', () => {
                this.downloadBackup();
            });
        }

        if (restoreBackupBtn) {
            restoreBackupBtn.addEventListener('click', () => {
                this.restoreBackup();
            });
        }
    }

    async createBackup() {
        const btn = document.getElementById('createBackup');
        const status = document.getElementById('backupStatus');

        if (btn) btn.classList.add('loading');
        if (status) status.textContent = 'Criando backup...';

        try {
            // Simular criação de backup
            await new Promise(resolve => setTimeout(resolve, 3000));

            if (status) status.textContent = 'Backup criado com sucesso!';
            this.showToast('Backup criado com sucesso!', 'success');

            // Atualizar informações do último backup
            const lastBackupDate = document.getElementById('lastBackupDate');
            const lastBackupSize = document.getElementById('lastBackupSize');

            if (lastBackupDate) lastBackupDate.textContent = new Date().toLocaleString('pt-BR');
            if (lastBackupSize) lastBackupSize.textContent = '2.3 MB';

        } catch (error) {
            if (status) status.textContent = 'Erro ao criar backup';
            this.showToast('Erro ao criar backup', 'error');
        } finally {
            if (btn) btn.classList.remove('loading');
        }
    }

    async downloadBackup() {
        this.showToast('Download do backup iniciado!', 'info');
        // Implementar download real aqui
    }

    async restoreBackup() {
        const fileInput = document.getElementById('backup_file');
        if (!fileInput || !fileInput.files[0]) {
            this.showToast('Selecione um arquivo de backup primeiro', 'warning');
            return;
        }

        const confirmed = confirm('Tem certeza? Isso substituirá todas as configurações atuais!');
        if (!confirmed) return;

        this.showToast('Restaurando backup... Isso pode levar alguns minutos', 'info');
        // Implementar restauração real aqui
    }

    initializeCommandToggles() {
        const commandToggles = document.querySelectorAll('.command-toggle input[type="checkbox"]');

        commandToggles.forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                const commandName = e.target.name.replace('cmd_', '').replace('_enabled', '');
                const enabled = e.target.checked;

                console.log(`Comando ${commandName} ${enabled ? 'ativado' : 'desativado'}`);

                // Atualizar estado local
                this.commandStates[commandName] = enabled;

                // Feedback visual
                const commandToggle = e.target.closest('.command-toggle');
                if (commandToggle) {
                    commandToggle.style.opacity = enabled ? '1' : '0.6';
                }

                // Marcar como não salvo
                this.markUnsaved();
            });
        });
    }

    // ===== GERENCIAMENTO DE ESTADOS DOS COMANDOS ===== //
    async loadCommandStates() {
        if (!this.guildId) {
            console.log('🔧 [DASHBOARD] Sem guildId, pulando carregamento de estados dos comandos');
            return;
        }

        try {
            console.log(`🔧 [DASHBOARD] Carregando estados dos comandos para guild ${this.guildId}`);
            const response = await fetch(`/api/guild/${this.guildId}/commands`);
            const result = await response.json();

            if (result.success) {
                this.commandStates = result.commands;
                this.populateCommandStates();
                console.log(`✅ [DASHBOARD] Estados de ${result.totalCommands} comandos carregados`);
            } else {
                console.error('❌ [DASHBOARD] Erro ao carregar estados dos comandos:', result.error);
            }
        } catch (error) {
            console.error('❌ [DASHBOARD] Erro ao carregar estados dos comandos:', error);
        }
    }

    populateCommandStates() {
        Object.keys(this.commandStates).forEach(commandName => {
            const commandState = this.commandStates[commandName];
            const toggle = document.querySelector(`input[name="cmd_${commandName}_enabled"]`);

            if (toggle) {
                toggle.checked = commandState.enabled;

                // Aplicar feedback visual
                const commandToggle = toggle.closest('.command-toggle');
                if (commandToggle) {
                    commandToggle.style.opacity = commandState.enabled ? '1' : '0.6';
                }
            }
        });
    }

    async saveCommandStates() {
        if (!this.guildId) {
            console.error('❌ [DASHBOARD] Guild ID não encontrado');
            return false;
        }

        try {
            console.log(`🔧 [DASHBOARD] Salvando estados dos comandos para guild ${this.guildId}`);

            const response = await fetch(`/api/guild/${this.guildId}/commands`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    commands: this.commandStates,
                    updatedBy: 'Dashboard'
                })
            });

            const result = await response.json();

            if (result.success) {
                console.log(`✅ [DASHBOARD] Estados dos comandos salvos: ${result.updatedCommands} comandos`);
                return true;
            } else {
                console.error('❌ [DASHBOARD] Erro ao salvar estados dos comandos:', result.error);
                return false;
            }
        } catch (error) {
            console.error('❌ [DASHBOARD] Erro ao salvar estados dos comandos:', error);
            return false;
        }
    }

    collectCommandStates() {
        const commandToggles = document.querySelectorAll('.command-toggle input[type="checkbox"]');
        const states = {};

        commandToggles.forEach(toggle => {
            const commandName = toggle.name.replace('cmd_', '').replace('_enabled', '');
            states[commandName] = toggle.checked;
        });

        return states;
    }



    // ===== VERIFICATION MODULE ===== //
    initializeVerification() {
        console.log('🛡️ [VERIFICATION] Inicializando módulo redesenhado...');

        // Setup verification toggle
        const verificationToggle = document.getElementById('verification_enabled');
        if (verificationToggle) {
            verificationToggle.addEventListener('change', () => {
                this.handleVerificationToggle(verificationToggle.checked);
            });
        }

        // Initialize tab navigation
        this.initializeVerificationTabs();

        // Load verification stats
        this.loadVerificationStats();
        this.loadVerificationConfig();

        // Update timestamp
        this.updatePreviewTimestamp();
    }

    initializeVerificationTabs() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');

                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Add active class to clicked button and corresponding content
                button.classList.add('active');
                const targetContent = document.getElementById(`${targetTab}-tab`);
                if (targetContent) {
                    targetContent.classList.add('active');
                }

                console.log(`🛡️ [VERIFICATION] Aba alterada para: ${targetTab}`);
            });
        });
    }

    updatePreviewTimestamp() {
        const timestampElement = document.getElementById('previewTimestamp');
        if (timestampElement) {
            const now = new Date();
            const timeString = now.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit'
            });
            timestampElement.textContent = timeString;
        }
    }

    async handleVerificationToggle(enabled) {
        console.log('🛡️ [VERIFICATION] Toggle:', enabled);

        // Update visual state
        const statusIndicator = document.querySelector('#verificationIndicator');
        if (statusIndicator) {
            const dot = statusIndicator.querySelector('.status-dot');
            const text = statusIndicator.querySelector('.status-text');

            if (enabled) {
                dot.classList.remove('inactive');
                dot.classList.add('active');
                text.textContent = 'Sistema Ativo';
            } else {
                dot.classList.remove('active');
                dot.classList.add('inactive');
                text.textContent = 'Sistema Inativo';
            }
        }

        // Save state immediately
        await this.saveVerificationConfig({ enabled });
    }

    async loadVerificationStats() {
        try {
            const response = await fetch(`/api/guild/${this.guildId}/verification/stats`);
            const result = await response.json();

            if (result.success) {
                this.updateVerificationStats(result.stats);
            }
        } catch (error) {
            console.error('Erro ao carregar estatísticas de verificação:', error);
        }
    }

    async loadVerificationConfig() {
        try {
            const response = await fetch(`/api/guild/${this.guildId}/verification`);
            const result = await response.json();

            if (result.success && result.config) {
                this.populateVerificationForm(result.config);
            }
        } catch (error) {
            console.error('Erro ao carregar config de verificação:', error);
        }
    }

    updateVerificationStats(stats) {
        if (!stats) return;

        // Update stat numbers with enhanced animation
        const elements = {
            totalMembers: document.getElementById('totalMembers'),
            verifiedMembers: document.getElementById('verifiedMembers'),
            pendingMembers: document.getElementById('pendingMembers'),
            successRate: document.getElementById('successRate')
        };

        Object.entries(elements).forEach(([key, element]) => {
            if (element && stats[key] !== undefined) {
                // Animate number change
                this.animateNumber(element, parseInt(element.textContent) || 0, stats[key]);
            }
        });

        // Calculate and update success rate
        if (stats.totalMembers > 0 && elements.successRate) {
            const rate = Math.round((stats.verifiedMembers / stats.totalMembers) * 100);
            this.animateNumber(elements.successRate, 0, rate, '%');
        }
    }

    animateNumber(element, start, end, suffix = '') {
        const duration = 1000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const current = Math.round(start + (end - start) * easeOut);

            element.textContent = current + suffix;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    populateVerificationForm(config) {
        const fields = {
            // Campos básicos
            verification_enabled: config.enabled,
            verificationChannel: config.channel_id,
            verificationMethod: config.method,
            verifiedRole: config.verified_role_id,
            unverifiedRole: config.unverified_role_id,

            // Configurações avançadas
            verificationTimeout: config.timeout_minutes,
            verificationCaptcha: config.captcha_type,
            verification_auto_kick: config.auto_kick,
            verificationKickTime: config.kick_time,
            verification_max_attempts: config.max_attempts,

            // Mensagens
            verificationRules: config.rules_text,
            welcomeMessage: config.welcome_message,

            // Toggles de notificação
            verification_dm_enabled: config.dm_enabled,
            verification_log_enabled: config.log_enabled,

            // Configurações combinadas
            combined_captcha: config.combined_captcha,
            combined_rules: config.combined_rules,

            // Configurações de segurança
            verification_ip_check: config.ip_check_enabled,
            verification_anti_bot: config.anti_bot_enabled
        };

        Object.entries(fields).forEach(([fieldId, value]) => {
            const field = document.getElementById(fieldId);
            if (field && value !== undefined) {
                if (field.type === 'checkbox') {
                    field.checked = Boolean(value);
                } else {
                    field.value = value;
                }
            }
        });

        // Atualizar preview após carregar configurações
        if (typeof updateVerificationPreview === 'function') {
            updateVerificationPreview();
        }

        // Atualizar timestamp
        if (typeof updatePreviewTimestamp === 'function') {
            updatePreviewTimestamp();
        }

        console.log('✅ [VERIFICATION] Formulário populado com todas as configurações');
    }

    async saveVerificationConfig(config) {
        try {
            console.log('🛡️ [VERIFICATION] Salvando configuração:', config);

            const response = await fetch(`/api/guild/${this.guildId}/config/verificacao`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ settings: config })
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ [VERIFICATION] Configuração salva com sucesso');
            } else {
                console.error('❌ [VERIFICATION] Erro ao salvar:', result.error);
            }

            return result.success;
        } catch (error) {
            console.error('❌ [VERIFICATION] Erro de conexão:', error);
            return false;
        }
    }

    /**
     * Coletar dados específicos da seção de verificação
     */
    collectVerificationData() {
        const settings = {};

        // Coletar todos os campos de verificação
        const verificationFields = [
            'verification_enabled', 'verification_channel_id', 'verification_method', 'verified_role_id', 'unverified_role_id',
            'verification_timeout_minutes', 'verification_rules_text', 'verification_captcha_type', 'verification_dm_enabled',
            'verification_log_enabled', 'verification_auto_kick', 'verification_kick_time', 'combined_captcha', 'combined_rules',
            'verification_ip_check', 'verification_max_attempts', 'verification_anti_bot', 'welcome_message'
        ];

        verificationFields.forEach(fieldName => {
            const element = document.querySelector(`[name="${fieldName}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    settings[fieldName] = element.checked;
                } else if (element.type === 'number') {
                    settings[fieldName] = parseInt(element.value) || 0;
                } else {
                    settings[fieldName] = element.value || '';
                }
            }
        });

        // Log para debug
        console.log('🛡️ [VERIFICATION] Campos coletados:', settings);

        return {
            enabled: settings.verification_enabled || false,
            settings: settings
        };
    }

    /**
     * Coletar todas as configurações do formulário (método legado)
     */
    collectVerificationConfig() {
        return {
            // Aba Básica
            verification_enabled: document.getElementById('verification_enabled')?.checked || false,
            verification_channel_id: document.getElementById('verificationChannel')?.value || '',
            verification_method: document.getElementById('verificationMethod')?.value || 'reaction',
            verified_role_id: document.getElementById('verifiedRole')?.value || '',
            unverified_role_id: document.getElementById('unverifiedRole')?.value || '',

            // Aba Avançada - Tempo
            verification_timeout_minutes: parseInt(document.getElementById('verificationTimeout')?.value) || 30,
            verification_auto_kick: document.getElementById('verification_auto_kick')?.checked || false,
            verification_kick_time: parseInt(document.getElementById('verificationKickTime')?.value) || 60,

            // Aba Avançada - Notificações
            verification_dm_enabled: document.getElementById('verification_dm_enabled')?.checked !== false,
            verification_log_enabled: document.getElementById('verification_log_enabled')?.checked !== false,

            // Aba Avançada - Segurança
            verification_ip_check: document.getElementById('verification_ip_check')?.checked || false,
            verification_max_attempts: parseInt(document.getElementById('verification_max_attempts')?.value) || 3,
            verification_anti_bot: document.getElementById('verification_anti_bot')?.checked !== false,

            // Aba Mensagens
            verification_rules_text: document.getElementById('verificationRules')?.value || 'Leia e aceite as regras do servidor para continuar.',
            welcome_message: document.getElementById('welcomeMessage')?.value || 'Bem-vindo(a) ao servidor! Você foi verificado(a) com sucesso.',

            // Configurações específicas de método
            verification_captcha_type: document.getElementById('verificationCaptcha')?.value || 'math',
            combined_captcha: document.getElementById('combined_captcha')?.checked !== false,
            combined_rules: document.getElementById('combined_rules')?.checked !== false
        };
    }
}

// Função global removida - usando sistema padrão de seções

// Inicializar quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 [DASHBOARD] DOM carregado, inicializando DashboardManager...');
    window.dashboardManager = new DashboardManager();
    console.log('🚀 [DASHBOARD] DashboardManager inicializado:', window.dashboardManager);

    // Controle de volume da música
    const volumeSlider = document.getElementById('default_volume');
    const volumeValue = document.getElementById('volumeValue');

    if (volumeSlider && volumeValue) {
        volumeSlider.addEventListener('input', function() {
            volumeValue.textContent = this.value + '%';
        });
    }
});

// ===== SISTEMA DE VERIFICAÇÃO INTEGRADO ===== //

/**
 * Atualizar preview da verificação em tempo real
 */
function updateVerificationPreview() {
    const method = document.getElementById('verificationMethod')?.value;
    const previewMethod = document.getElementById('previewMethod');
    const methodConfig = document.getElementById('methodSpecificConfig');

    // Limpar configurações específicas
    document.querySelectorAll('.method-section').forEach(section => {
        section.style.display = 'none';
        section.classList.remove('active');
    });

    // Atualizar preview do método com design aprimorado
    const methodTexts = {
        'reaction': { icon: 'fas fa-mouse-pointer', text: 'Clique no botão "Verificar" para continuar' },
        'captcha_math': { icon: 'fas fa-calculator', text: 'Resolva a conta matemática: 7 + 3 = ?' },
        'captcha_text': { icon: 'fas fa-keyboard', text: 'Digite a palavra exibida: NODEX' },
        'captcha_emoji': { icon: 'fas fa-smile', text: 'Selecione o emoji correto: 🛡️' },
        'manual': { icon: 'fas fa-user-shield', text: 'Aguarde aprovação de um moderador' },
        'combined': { icon: 'fas fa-tasks', text: 'Complete todas as etapas de verificação' }
    };

    if (previewMethod) {
        const methodData = methodTexts[method] || { icon: 'fas fa-cog', text: 'Selecione um método de verificação' };

        // Atualizar o preview com o novo design
        const methodDisplay = previewMethod.querySelector('.method-display');
        if (methodDisplay) {
            methodDisplay.innerHTML = `
                <i class="${methodData.icon}"></i>
                <span>${methodData.text}</span>
            `;
        } else {
            // Fallback para o design antigo se o novo não existir
            previewMethod.innerHTML = methodData.text;
        }

        // Adicionar efeito de highlight
        previewMethod.classList.add('highlight');
        setTimeout(() => previewMethod.classList.remove('highlight'), 600);
    }

    // Atualizar tipo de captcha baseado na seleção e mostrar configurações específicas
    const captchaTypeField = document.getElementById('verificationCaptcha');
    if (method && methodConfig) {
        // Atualizar tipo de captcha automaticamente
        if (captchaTypeField) {
            if (method === 'captcha_math') {
                captchaTypeField.value = 'math';
            } else if (method === 'captcha_text') {
                captchaTypeField.value = 'text';
            } else if (method === 'captcha_emoji') {
                captchaTypeField.value = 'emoji';
            } else {
                captchaTypeField.value = 'math'; // padrão
            }
        }

        // Para métodos que precisam de configuração específica
        let configToShow = null;

        if (method === 'manual') {
            configToShow = 'manualConfig';
        } else if (method === 'combined') {
            configToShow = 'combinedConfig';
        }

        if (configToShow) {
            const specificConfig = document.getElementById(configToShow);
            if (specificConfig) {
                specificConfig.style.display = 'block';
                specificConfig.classList.add('active');
            }
        }
    }

    // Atualizar timestamp
    updatePreviewTimestamp();

    // Animar preview
    animatePreviewUpdate();
}

/**
 * Atualizar preview do timeout
 */
function updateTimeoutPreview() {
    const timeout = document.getElementById('verificationTimeout').value;
    const previewTimeout = document.getElementById('previewTimeout');

    if (timeout) {
        previewTimeout.innerHTML = `<small>⏰ Tempo limite: ${timeout} minutos</small>`;
        previewTimeout.style.color = '#00ff7f';

        setTimeout(() => {
            previewTimeout.style.color = '';
        }, 1000);
    }
}

/**
 * Atualizar preview das regras
 */
function updateRulesPreview() {
    const rules = document.getElementById('verificationRules').value;
    const previewRules = document.getElementById('previewRules');

    if (rules.trim()) {
        previewRules.textContent = rules;
    } else {
        previewRules.textContent = 'Leia as regras do servidor antes de se verificar.';
    }

    animatePreviewUpdate();
}

/**
 * Atualizar preview da mensagem de boas-vindas
 */
function updateWelcomePreview() {
    const welcome = document.getElementById('welcomeMessage').value;
    console.log('🛡️ [VERIFICATION] Mensagem de boas-vindas atualizada:', welcome);

    // Aqui você pode adicionar lógica adicional se necessário
    animatePreviewUpdate();
}

/**
 * Toggle configuração de auto-kick
 */
function toggleAutoKick() {
    const autoKick = document.getElementById('verification_auto_kick').checked;
    const autoKickConfig = document.getElementById('autoKickConfig');

    if (autoKick) {
        autoKickConfig.style.display = 'block';
        autoKickConfig.style.animation = 'slideDown 0.3s ease';
    } else {
        autoKickConfig.style.display = 'none';
    }
}

/**
 * Animar atualização do preview
 */
function animatePreviewUpdate() {
    // Tentar encontrar o preview novo primeiro, depois o antigo
    const preview = document.querySelector('.preview-embed-enhanced') || document.querySelector('.preview-embed');
    if (preview) {
        preview.classList.add('updating');

        setTimeout(() => {
            preview.classList.remove('updating');
            preview.classList.add('updated');

            setTimeout(() => {
                preview.classList.remove('updated');
            }, 600);
        }, 150);
    }
}

/**
 * Inicializar sistema de verificação redesenhado
 */
function initializeVerificationSystem() {
    console.log('🛡️ [VERIFICATION] Inicializando sistema de verificação redesenhado');

    // Configurar eventos para os campos existentes
    const verificationMethod = document.getElementById('verificationMethod');
    if (verificationMethod) {
        verificationMethod.addEventListener('change', updateVerificationPreview);
    }

    const verificationTimeout = document.getElementById('verificationTimeout');
    if (verificationTimeout) {
        verificationTimeout.addEventListener('change', updateTimeoutPreview);
    }

    const verificationRules = document.getElementById('verificationRules');
    if (verificationRules) {
        verificationRules.addEventListener('input', updateRulesPreview);
    }

    const welcomeMessage = document.getElementById('welcomeMessage');
    if (welcomeMessage) {
        welcomeMessage.addEventListener('input', updateWelcomePreview);
    }

    const autoKickToggle = document.getElementById('verification_auto_kick');
    if (autoKickToggle) {
        autoKickToggle.addEventListener('change', toggleAutoKick);
    }

    // Inicializar sistema de abas
    initializeVerificationTabs();

    // Inicializar preview
    updateVerificationPreview();
    updateTimeoutPreview();
    updateRulesPreview();
    updatePreviewTimestamp();

    console.log('✅ [VERIFICATION] Sistema de verificação redesenhado inicializado com sucesso');
}

/**
 * Inicializar sistema de abas da verificação
 */
function initializeVerificationTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            button.classList.add('active');
            const targetContent = document.getElementById(`${targetTab}-tab`);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            console.log(`🛡️ [VERIFICATION] Aba alterada para: ${targetTab}`);
        });
    });
}

/**
 * Atualizar timestamp do preview
 */
function updatePreviewTimestamp() {
    const timestampElement = document.getElementById('previewTimestamp');
    if (timestampElement) {
        const now = new Date();
        const timeString = now.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });
        timestampElement.textContent = timeString;
    }

    // ===== SISTEMA DE BACKUP ===== //
    initializeBackupSystem() {
        console.log('💾 [BACKUP] Inicializando sistema de backup...');

        // Botão de criar backup
        const createBackupBtn = document.getElementById('createBackup');
        if (createBackupBtn) {
            createBackupBtn.addEventListener('click', () => {
                this.createBackup();
            });
        }

        // Botão de baixar backup
        const downloadBackupBtn = document.getElementById('downloadBackup');
        if (downloadBackupBtn) {
            downloadBackupBtn.addEventListener('click', () => {
                this.downloadLastBackup();
            });
        }

        // Botão de restaurar backup
        const restoreBackupBtn = document.getElementById('restoreBackup');
        if (restoreBackupBtn) {
            restoreBackupBtn.addEventListener('click', () => {
                this.restoreBackup();
            });
        }

        // Input de arquivo de backup
        const backupFileInput = document.getElementById('backup_file');
        if (backupFileInput) {
            backupFileInput.addEventListener('change', (e) => {
                this.handleBackupFileSelect(e);
            });
        }

        // Carregar informações de backup
        this.loadBackupInfo();

        console.log('✅ [BACKUP] Sistema de backup inicializado');
    }

    async createBackup() {
        console.log('💾 [BACKUP] Criando backup...');

        const createBtn = document.getElementById('createBackup');
        const statusElement = document.getElementById('backupStatus');

        // Atualizar UI
        createBtn.disabled = true;
        createBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Criando...';
        if (statusElement) statusElement.textContent = 'Criando backup...';

        try {
            const response = await fetch(`/api/guild/${this.guildId}/backup/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    options: {
                        includeChannels: false,
                        includeRoles: false,
                        includeUserData: false,
                        includeEconomy: false,
                        includeLevels: false
                    }
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showToast('✅ Backup criado com sucesso!', 'success');
                this.updateBackupInfo(result.backup);
                if (statusElement) statusElement.textContent = 'Backup criado com sucesso';
            } else {
                this.showToast(`❌ Erro ao criar backup: ${result.error}`, 'error');
                if (statusElement) statusElement.textContent = 'Erro ao criar backup';
            }

        } catch (error) {
            console.error('❌ [BACKUP] Erro ao criar backup:', error);
            this.showToast('❌ Erro de conexão ao criar backup', 'error');
            if (statusElement) statusElement.textContent = 'Erro de conexão';
        } finally {
            // Restaurar botão
            createBtn.disabled = false;
            createBtn.innerHTML = '<i class="fas fa-save"></i> Criar Backup Agora';
        }
    }

    async downloadLastBackup() {
        console.log('📥 [BACKUP] Baixando último backup...');

        try {
            const response = await fetch(`/api/guild/${this.guildId}/backup/list`);
            const result = await response.json();

            if (result.success && result.backups.length > 0) {
                const lastBackup = result.backups[0];

                // Simular download (em uma implementação real, você criaria um endpoint para download)
                this.showToast(`📥 Download iniciado: ${lastBackup.id}`, 'info');

                // Aqui você implementaria o download real do arquivo
                console.log('📥 [BACKUP] Backup para download:', lastBackup);

            } else {
                this.showToast('❌ Nenhum backup encontrado para download', 'error');
            }

        } catch (error) {
            console.error('❌ [BACKUP] Erro ao baixar backup:', error);
            this.showToast('❌ Erro ao baixar backup', 'error');
        }
    }

    async restoreBackup() {
        const fileInput = document.getElementById('backup_file');
        const file = fileInput.files[0];

        if (!file) {
            this.showToast('❌ Selecione um arquivo de backup primeiro', 'error');
            return;
        }

        // Confirmação
        if (!confirm('⚠️ ATENÇÃO: Restaurar um backup substituirá TODAS as configurações atuais. Tem certeza?')) {
            return;
        }

        console.log('🔄 [BACKUP] Restaurando backup...');

        const restoreBtn = document.getElementById('restoreBackup');
        const statusElement = document.getElementById('backupStatus');

        // Atualizar UI
        restoreBtn.disabled = true;
        restoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Restaurando...';
        if (statusElement) statusElement.textContent = 'Restaurando backup...';

        try {
            // Ler arquivo
            const fileContent = await this.readBackupFile(file);
            const backupData = JSON.parse(fileContent);

            // Enviar para restauração
            const response = await fetch(`/api/guild/${this.guildId}/backup/restore`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    backupId: backupData.id,
                    options: {
                        restoreConfig: true,
                        restoreChannels: false,
                        restoreRoles: false,
                        restoreUserData: false
                    }
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showToast('✅ Backup restaurado com sucesso! Recarregando página...', 'success');
                if (statusElement) statusElement.textContent = 'Backup restaurado com sucesso';

                // Recarregar página após 2 segundos
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                this.showToast(`❌ Erro ao restaurar backup: ${result.error}`, 'error');
                if (statusElement) statusElement.textContent = 'Erro ao restaurar backup';
            }

        } catch (error) {
            console.error('❌ [BACKUP] Erro ao restaurar backup:', error);
            this.showToast('❌ Erro ao processar arquivo de backup', 'error');
            if (statusElement) statusElement.textContent = 'Erro ao processar arquivo';
        } finally {
            // Restaurar botão
            restoreBtn.disabled = false;
            restoreBtn.innerHTML = '<i class="fas fa-upload"></i> Restaurar Backup';
        }
    }

    readBackupFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    }

    handleBackupFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            console.log('📁 [BACKUP] Arquivo selecionado:', file.name);

            // Validar tipo de arquivo
            if (!file.name.endsWith('.json') && !file.name.endsWith('.zip')) {
                this.showToast('❌ Arquivo deve ser .json ou .zip', 'error');
                event.target.value = '';
                return;
            }

            this.showToast(`📁 Arquivo selecionado: ${file.name}`, 'info');
        }
    }

    async loadBackupInfo() {
        try {
            const response = await fetch(`/api/guild/${this.guildId}/backup/list`);
            const result = await response.json();

            if (result.success && result.backups.length > 0) {
                const lastBackup = result.backups[0];
                this.updateBackupInfo(lastBackup);
            } else {
                this.updateBackupInfo(null);
            }

        } catch (error) {
            console.error('❌ [BACKUP] Erro ao carregar informações de backup:', error);
            this.updateBackupInfo(null);
        }
    }

    updateBackupInfo(backup) {
        const lastBackupDate = document.getElementById('lastBackupDate');
        const lastBackupSize = document.getElementById('lastBackupSize');
        const backupStatus = document.getElementById('backupStatus');

        if (backup) {
            if (lastBackupDate) {
                const date = new Date(backup.created_at);
                lastBackupDate.textContent = date.toLocaleString('pt-BR');
            }
            if (lastBackupSize) {
                lastBackupSize.textContent = backup.size_formatted || 'Desconhecido';
            }
            if (backupStatus) {
                backupStatus.textContent = 'Backup disponível';
                backupStatus.style.color = '#2ecc71';
            }
        } else {
            if (lastBackupDate) lastBackupDate.textContent = 'Nunca';
            if (lastBackupSize) lastBackupSize.textContent = '-';
            if (backupStatus) {
                backupStatus.textContent = 'Nenhum backup';
                backupStatus.style.color = '#e74c3c';
            }
        }
    }
}

// Inicializar quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Aguardar um pouco para garantir que todos os elementos estejam carregados
    setTimeout(() => {
        initializeVerificationSystem();
    }, 500);
});

// Avisar sobre mudanças não salvas
window.addEventListener('beforeunload', (e) => {
    if (window.dashboardManager && window.dashboardManager.unsavedChanges) {
        e.preventDefault();
        e.returnValue = 'Você tem alterações não salvas. Deseja sair mesmo assim?';
    }
});
