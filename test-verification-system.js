/**
 * Script de teste para o sistema de verificação
 * Verifica se todas as funcionalidades estão funcionando corretamente
 */

const DatabaseManager = require('./database/DatabaseManager');

async function testVerificationSystem() {
    console.log('🧪 [TEST] Iniciando testes do sistema de verificação...\n');

    // Inicializar banco de dados
    const dbManager = new DatabaseManager();
    dbManager.initialize();
    const db = dbManager.db;

    const testGuildId = '1381755403326455838';
    const testUserId = '123456789012345678';

    try {
        // Teste 1: Verificar estrutura da tabela
        console.log('📋 [TEST 1] Verificando estrutura da tabela verification_config...');
        const tableInfo = db.prepare(`PRAGMA table_info(verification_config)`).all();
        console.log('Colunas encontradas:', tableInfo.map(col => col.name));
        
        const requiredColumns = [
            'guild_id', 'enabled', 'channel_id', 'method', 'verified_role_id',
            'unverified_role_id', 'timeout_minutes', 'auto_kick', 'kick_time',
            'dm_enabled', 'log_enabled', 'anti_bot_enabled', 'ip_check_enabled',
            'max_attempts', 'rules_text', 'welcome_message'
        ];
        
        const missingColumns = requiredColumns.filter(col => 
            !tableInfo.some(info => info.name === col)
        );
        
        if (missingColumns.length > 0) {
            console.log('❌ Colunas faltando:', missingColumns);
        } else {
            console.log('✅ Todas as colunas necessárias estão presentes');
        }

        // Teste 2: Verificar configuração atual
        console.log('\n📋 [TEST 2] Verificando configuração atual...');
        const currentConfig = db.prepare(`
            SELECT * FROM verification_config WHERE guild_id = ?
        `).get(testGuildId);
        
        if (currentConfig) {
            console.log('✅ Configuração encontrada:');
            console.log(`  - Habilitado: ${currentConfig.enabled ? 'SIM' : 'NÃO'}`);
            console.log(`  - Método: ${currentConfig.method}`);
            console.log(`  - Auto-kick: ${currentConfig.auto_kick ? 'SIM' : 'NÃO'}`);
            console.log(`  - Timeout: ${currentConfig.timeout_minutes} minutos`);
            console.log(`  - Tentativas máximas: ${currentConfig.max_attempts}`);
            console.log(`  - Anti-bot: ${currentConfig.anti_bot_enabled ? 'SIM' : 'NÃO'}`);
            console.log(`  - Verificação IP: ${currentConfig.ip_check_enabled ? 'SIM' : 'NÃO'}`);
        } else {
            console.log('❌ Nenhuma configuração encontrada');
        }

        // Teste 3: Testar inserção de configuração
        console.log('\n📋 [TEST 3] Testando inserção de configuração...');
        const testConfig = {
            guild_id: testGuildId,
            enabled: 1,
            channel_id: '1382429296454537318',
            verified_role_id: '1382425258975563997',
            unverified_role_id: null,
            method: 'captcha_emoji',
            timeout_minutes: 30,
            auto_kick: 0,
            kick_time: 60,
            dm_enabled: 1,
            log_enabled: 1,
            anti_bot_enabled: 1,
            ip_check_enabled: 0,
            max_attempts: 3,
            rules_text: 'Teste de regras',
            welcome_message: 'Teste de mensagem de boas-vindas',
            captcha_type: 'emoji'
        };

        const insertStmt = db.prepare(`
            INSERT OR REPLACE INTO verification_config 
            (guild_id, enabled, channel_id, verified_role_id, unverified_role_id, method,
             timeout_minutes, auto_kick, kick_time, dm_enabled, log_enabled, 
             anti_bot_enabled, ip_check_enabled, max_attempts, rules_text, 
             welcome_message, captcha_type, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `);

        insertStmt.run(
            testConfig.guild_id, testConfig.enabled, testConfig.channel_id,
            testConfig.verified_role_id, testConfig.unverified_role_id, testConfig.method,
            testConfig.timeout_minutes, testConfig.auto_kick, testConfig.kick_time,
            testConfig.dm_enabled, testConfig.log_enabled, testConfig.anti_bot_enabled,
            testConfig.ip_check_enabled, testConfig.max_attempts, testConfig.rules_text,
            testConfig.welcome_message, testConfig.captcha_type
        );

        console.log('✅ Configuração de teste inserida com sucesso');

        // Teste 4: Verificar member_verification
        console.log('\n📋 [TEST 4] Testando tabela member_verification...');
        
        // Inserir membro de teste
        db.prepare(`
            INSERT OR REPLACE INTO member_verification
            (guild_id, user_id, verified, attempts, last_attempt)
            VALUES (?, ?, 0, 0, CURRENT_TIMESTAMP)
        `).run(testGuildId, testUserId);

        const memberVerification = db.prepare(`
            SELECT * FROM member_verification WHERE guild_id = ? AND user_id = ?
        `).get(testGuildId, testUserId);

        if (memberVerification) {
            console.log('✅ Registro de membro criado:');
            console.log(`  - Verificado: ${memberVerification.verified ? 'SIM' : 'NÃO'}`);
            console.log(`  - Tentativas: ${memberVerification.attempts}`);
        } else {
            console.log('❌ Erro ao criar registro de membro');
        }

        // Teste 5: Testar logs de verificação
        console.log('\n📋 [TEST 5] Testando logs de verificação...');
        
        db.prepare(`
            INSERT INTO verification_logs
            (guild_id, user_id, action, method, details, timestamp)
            VALUES (?, ?, 'test', 'captcha_emoji', ?, CURRENT_TIMESTAMP)
        `).run(testGuildId, testUserId, JSON.stringify({ test: true }));

        const logCount = db.prepare(`
            SELECT COUNT(*) as count FROM verification_logs 
            WHERE guild_id = ? AND user_id = ?
        `).get(testGuildId, testUserId);

        console.log(`✅ Logs de verificação: ${logCount.count} registros encontrados`);

        // Teste 6: Verificar valores problemáticos
        console.log('\n📋 [TEST 6] Verificando valores problemáticos...');
        
        const problematicValues = db.prepare(`
            SELECT guild_id, verified_role_id, channel_id 
            FROM verification_config 
            WHERE verified_role_id IN ('undefined', 'null') 
               OR channel_id IN ('undefined', 'null')
        `).all();

        if (problematicValues.length > 0) {
            console.log('❌ Valores problemáticos encontrados:', problematicValues);
        } else {
            console.log('✅ Nenhum valor problemático encontrado');
        }

        console.log('\n🎉 [TEST] Todos os testes concluídos!');

    } catch (error) {
        console.error('❌ [TEST] Erro durante os testes:', error);
    } finally {
        db.close();
    }
}

// Executar testes se chamado diretamente
if (require.main === module) {
    testVerificationSystem();
}

module.exports = { testVerificationSystem };
