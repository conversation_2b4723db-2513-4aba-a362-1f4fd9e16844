/**
 * ========================================
 * VERIFICATION HANDLER
 * Sistema de processamento de verificação
 * ========================================
 */

const { ActionRowBuilder, ButtonBuilder, ButtonStyle, EmbedBuilder } = require('discord.js');
const EmbedStyles = require('../utils/EmbedStyles');

module.exports = {
    name: 'interactionCreate',
    once: false,

    async execute(interaction) {
        if (!interaction.isButton() && !interaction.isModalSubmit()) return;

        // Processar botões de verificação
        if (interaction.customId === 'verify_start') {
            await this.handleVerificationStart(interaction);
        } else if (interaction.customId === 'verify_confirm') {
            await this.handleVerificationConfirm(interaction);
        } else if (interaction.customId === 'verify_captcha_submit') {
            await this.handleCaptchaSubmit(interaction);
        } else if (interaction.customId === 'verify_manual_approve') {
            await this.handleManualApproval(interaction);
        }
    },

    // Exportar funções para testes
    handleVerificationStart: async function(interaction) {
        return await this.handleVerificationStart(interaction);
    },

    createVerificationEmbed: async function(method, guild) {
        return await this.createVerificationEmbed(method, guild);
    },

    completeVerification: async function(member, config, method) {
        return await this.completeVerification(member, config, method);
    },

    /**
     * Iniciar processo de verificação
     */
    async handleVerificationStart(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const member = interaction.member;

        try {
            // Buscar configuração de verificação
            const config = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ?
            `).get(guild.id);

            // Atualizar status em tempo real
            if (client.realTimeIntegration) {
                client.realTimeIntegration.updateVerificationStatus(guild.id, interaction.user.id, 'pending');
            }

            if (!config || !config.enabled) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Sistema Inativo',
                    'O sistema de verificação não está ativo neste servidor!'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Verificar se já está verificado
            const verification = client.database.db.prepare(`
                SELECT verified FROM member_verification WHERE guild_id = ? AND user_id = ?
            `).get(guild.id, member.id);

            if (verification?.verified) {
                const alreadyEmbed = embedStyles.createWarningEmbed(
                    'Já Verificado',
                    'Você já está verificado neste servidor!'
                );
                return await interaction.reply({ embeds: [alreadyEmbed], ephemeral: true });
            }

            // Registrar tentativa
            client.database.db.prepare(`
                INSERT OR REPLACE INTO member_verification 
                (guild_id, user_id, verified, attempts, last_attempt)
                VALUES (?, ?, 0, COALESCE((SELECT attempts FROM member_verification WHERE guild_id = ? AND user_id = ?), 0) + 1, CURRENT_TIMESTAMP)
            `).run(guild.id, member.id, guild.id, member.id);

            // Log da tentativa
            client.database.db.prepare(`
                INSERT INTO verification_logs 
                (guild_id, user_id, action, method, details, timestamp)
                VALUES (?, ?, 'started', ?, ?, CURRENT_TIMESTAMP)
            `).run(guild.id, member.id, config.method, JSON.stringify({ ip: interaction.user.id }));

            // Processar baseado no método
            switch (config.method) {
                case 'reaction':
                    await this.handleReactionVerification(interaction, config);
                    break;
                case 'captcha_math':
                case 'captcha_text':
                case 'captcha_emoji':
                    await this.handleCaptchaVerification(interaction, config);
                    break;
                case 'combined':
                    await this.handleCombinedVerification(interaction, config);
                    break;
                case 'manual':
                    await this.handleManualVerification(interaction, config);
                    break;
                default:
                    console.log(`🛡️ [VERIFICATION] Método não reconhecido: ${config.method}`);
                    const errorEmbed = embedStyles.createErrorEmbed(
                        'Método Inválido',
                        `Método de verificação não reconhecido: ${config.method}`
                    );
                    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

        } catch (error) {
            client.logger.error('Erro no início da verificação:', error);
            
            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                'Ocorreu um erro ao iniciar a verificação.'
            );
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },

    /**
     * Verificação por reação
     */
    async handleReactionVerification(interaction, config) {
        const embedStyles = new EmbedStyles();
        
        const verifyEmbed = {
            color: parseInt(embedStyles.colors.info.replace('#', ''), 16),
            title: `${embedStyles.icons.shield} ${embedStyles.format.bold('Confirmação de Verificação')}`,
            description: `**Confirme que você leu e aceita as regras do servidor**\n\n${embedStyles.format.italic('Clique no botão abaixo para confirmar')}`,
            fields: [
                {
                    name: `${embedStyles.icons.rules} **Regras do Servidor**`,
                    value: config.rules_text || 'Leia e aceite as regras do servidor para continuar.',
                    inline: false
                },
                {
                    name: `${embedStyles.icons.info} **Importante**`,
                    value: `• Ao confirmar, você declara ter lido as regras\n• Você concorda em seguir todas as diretrizes\n• Comportamentos inadequados resultarão em punições\n• Este processo é obrigatório para acessar o servidor`,
                    inline: false
                }
            ],
            timestamp: new Date().toISOString(),
            footer: {
                text: 'Nodex | Moderação • Verificação por Reação',
                icon_url: interaction.guild.iconURL()
            }
        };

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('verify_confirm')
                    .setLabel('Aceito as Regras')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅')
            );

        await interaction.reply({ embeds: [verifyEmbed], components: [row], ephemeral: true });
    },

    /**
     * Verificação por captcha
     */
    async handleCaptchaVerification(interaction, config) {
        const embedStyles = new EmbedStyles();

        // Determinar tipo de captcha baseado no método
        let captchaType = 'math'; // padrão
        if (config.method === 'captcha_text') captchaType = 'text';
        else if (config.method === 'captcha_image') captchaType = 'image';
        else if (config.method === 'captcha_math') captchaType = 'math';

        console.log(`🛡️ [VERIFICATION] Gerando captcha tipo: ${captchaType} para método: ${config.method}`);

        // Gerar captcha
        const captcha = this.generateCaptcha(captchaType);

        // Salvar resposta esperada temporariamente
        const tempData = {
            answer: captcha.answer,
            expires: Date.now() + (config.timeout_minutes * 60 * 1000)
        };

        // Armazenar temporariamente (em produção, usar Redis ou similar)
        if (!interaction.client.tempVerificationData) {
            interaction.client.tempVerificationData = new Map();
        }
        interaction.client.tempVerificationData.set(`${interaction.guild.id}_${interaction.user.id}`, tempData);

        const captchaEmbed = {
            color: parseInt(embedStyles.colors.warning.replace('#', ''), 16),
            title: `${embedStyles.icons.captcha} ${embedStyles.format.bold('Verificação Captcha')}`,
            description: `**Resolva o captcha abaixo para continuar**\n\n${embedStyles.format.italic('Você tem ' + config.timeout_minutes + ' minutos para responder')}`,
            fields: [
                {
                    name: `${embedStyles.icons.question} **Pergunta**`,
                    value: embedStyles.format.code(captcha.question),
                    inline: false
                },
                {
                    name: `${embedStyles.icons.info} **Instruções**`,
                    value: `• Digite apenas o número da resposta\n• Não use espaços ou caracteres especiais\n• Exemplo: se a resposta for 15, digite apenas: 15`,
                    inline: false
                }
            ],
            timestamp: new Date().toISOString(),
            footer: {
                text: 'Nodex | Moderação • Verificação Captcha',
                icon_url: interaction.guild.iconURL()
            }
        };

        await interaction.reply({ embeds: [captchaEmbed], ephemeral: true });

        // Aguardar resposta
        const filter = (msg) => msg.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({ 
            filter, 
            time: config.timeout_minutes * 60 * 1000,
            max: 1 
        });

        collector.on('collect', async (message) => {
            await this.processCaptchaAnswer(interaction, message, captcha.answer);
        });

        collector.on('end', async (collected) => {
            if (collected.size === 0) {
                await this.handleVerificationTimeout(interaction);
            }
        });
    },

    /**
     * Verificação combinada
     */
    async handleCombinedVerification(interaction, config) {
        // Primeiro fazer captcha, depois reação
        await this.handleCaptchaVerification(interaction, config);
    },

    /**
     * Verificação manual
     */
    async handleManualVerification(interaction, config) {
        const embedStyles = new EmbedStyles();
        
        const manualEmbed = embedStyles.createInfoEmbed(
            'Verificação Manual Solicitada',
            `Sua solicitação de verificação foi enviada para a equipe de moderação.\n\n**Tempo estimado:** 1-24 horas\n**Status:** Aguardando revisão\n\nVocê será notificado quando a verificação for aprovada.`
        );

        await interaction.reply({ embeds: [manualEmbed], ephemeral: true });

        // Notificar moderadores
        await this.notifyModerators(interaction, config);
    },

    /**
     * Confirmar verificação por reação
     */
    async handleVerificationConfirm(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const member = interaction.member;

        try {
            // Buscar configuração
            const config = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ?
            `).get(guild.id);

            if (!config) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Configuração Não Encontrada',
                    'Sistema de verificação não configurado!'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Completar verificação
            await this.completeVerification(member, config, 'reaction');

            const successEmbed = embedStyles.createSuccessEmbed(
                'Verificação Concluída!',
                `Parabéns! Você foi verificado com sucesso no servidor.\n\n✅ Cargo de membro atribuído\n✅ Acesso completo liberado\n✅ Bem-vindo(a) à comunidade!`
            );

            await interaction.update({ embeds: [successEmbed], components: [] });

        } catch (error) {
            client.logger.error('Erro na confirmação de verificação:', error);
            
            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                'Ocorreu um erro ao completar a verificação.'
            );
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },

    /**
     * Processar resposta do captcha
     */
    async processCaptchaAnswer(interaction, message, correctAnswer) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const userAnswer = message.content.trim();

        try {
            // Deletar mensagem do usuário
            await message.delete().catch(() => {});

            if (userAnswer === correctAnswer.toString()) {
                // Resposta correta
                const config = client.database.db.prepare(`
                    SELECT * FROM verification_config WHERE guild_id = ?
                `).get(interaction.guild.id);

                await this.completeVerification(interaction.member, config, 'captcha');

                const successEmbed = embedStyles.createSuccessEmbed(
                    'Captcha Correto!',
                    `Parabéns! Você resolveu o captcha corretamente e foi verificado.\n\n✅ Resposta correta\n✅ Cargo de membro atribuído\n✅ Acesso completo liberado`
                );

                await interaction.followUp({ embeds: [successEmbed], ephemeral: true });

            } else {
                // Resposta incorreta
                await this.handleVerificationFailure(interaction, 'Resposta incorreta do captcha');
            }

        } catch (error) {
            client.logger.error('Erro ao processar resposta do captcha:', error);
        }
    },

    /**
     * Completar verificação
     */
    async completeVerification(member, config, method) {
        const guild = member.guild;
        const client = member.client;

        // Atribuir cargos
        const verifiedRole = guild.roles.cache.get(config.verified_role_id);
        const unverifiedRole = config.unverified_role_id ? guild.roles.cache.get(config.unverified_role_id) : null;

        if (verifiedRole) {
            await member.roles.add(verifiedRole);
        }

        if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
            await member.roles.remove(unverifiedRole);
        }

        // Atualizar banco
        client.database.db.prepare(`
            UPDATE member_verification 
            SET verified = 1, verification_method = ?, verified_at = CURRENT_TIMESTAMP
            WHERE guild_id = ? AND user_id = ?
        `).run(method, guild.id, member.id);

        // Log da verificação
        client.database.db.prepare(`
            INSERT INTO verification_logs 
            (guild_id, user_id, action, method, details, timestamp)
            VALUES (?, ?, 'completed', ?, ?, CURRENT_TIMESTAMP)
        `).run(guild.id, member.id, method, JSON.stringify({ success: true }));

        // Enviar notificação de sucesso no canal de logs
        const guildConfig = await client.configManager.getGuildConfig(guild.id);
        if (guildConfig?.log_channel_id) {
            const logChannel = await guild.channels.fetch(guildConfig.log_channel_id);
            if (logChannel) {
                const successEmbed = new EmbedBuilder()
                    .setColor('#00ff7f')
                    .setTitle('✅ Membro Verificado')
                    .setDescription(`${member.user.tag} completou a verificação com sucesso!`)
                    .addFields(
                        { name: 'Método', value: method, inline: true },
                        { name: 'ID do Usuário', value: member.id, inline: true },
                        { name: 'Tempo de Verificação', value: 'Agora mesmo', inline: true }
                    )
                    .setThumbnail(member.user.displayAvatarURL())
                    .setTimestamp();

                await logChannel.send({ embeds: [successEmbed] });
            }
        }

        client.logger.info(`Membro verificado: ${member.user.tag} no servidor ${guild.name} via ${method}`);
    },

    /**
     * Gerar captcha
     */
    generateCaptcha(type) {
        console.log(`🛡️ [VERIFICATION] Gerando captcha do tipo: ${type}`);

        if (type === 'math') {
            const num1 = Math.floor(Math.random() * 20) + 1;
            const num2 = Math.floor(Math.random() * 20) + 1;
            const operations = ['+', '-', '*'];
            const operation = operations[Math.floor(Math.random() * operations.length)];

            let answer;
            let question;

            switch (operation) {
                case '+':
                    answer = num1 + num2;
                    question = `${num1} + ${num2} = ?`;
                    break;
                case '-':
                    answer = Math.max(num1, num2) - Math.min(num1, num2);
                    question = `${Math.max(num1, num2)} - ${Math.min(num1, num2)} = ?`;
                    break;
                case '*':
                    const smallNum1 = Math.floor(Math.random() * 10) + 1;
                    const smallNum2 = Math.floor(Math.random() * 10) + 1;
                    answer = smallNum1 * smallNum2;
                    question = `${smallNum1} × ${smallNum2} = ?`;
                    break;
            }

            console.log(`🧮 [CAPTCHA] Pergunta: ${question}, Resposta: ${answer}`);
            return { question, answer };

        } else if (type === 'text') {
            // Captcha de texto simples
            const words = ['DISCORD', 'SERVIDOR', 'MODERACAO', 'VERIFICAR', 'MEMBRO', 'NODEX', 'COMUNIDADE'];
            const word = words[Math.floor(Math.random() * words.length)];
            console.log(`📝 [CAPTCHA] Palavra: ${word}`);
            return {
                question: `Digite exatamente esta palavra: ${word}`,
                answer: word
            };

        } else if (type === 'image') {
            // Captcha de imagem
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let code = '';
            for (let i = 0; i < 6; i++) {
                code += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            console.log(`🖼️ [CAPTCHA] Código gerado: ${code}`);
            return {
                question: `Digite o código mostrado na imagem:`,
                answer: code
            };

        } else {
            // Fallback para math
            console.log(`⚠️ [CAPTCHA] Tipo desconhecido: ${type}, usando math como fallback`);
            return this.generateCaptcha('math');
        }
    },

    /**
     * Lidar com falha na verificação
     */
    async handleVerificationFailure(interaction, reason) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;

        // Log da falha
        client.database.db.prepare(`
            INSERT INTO verification_logs 
            (guild_id, user_id, action, method, details, timestamp)
            VALUES (?, ?, 'failed', 'captcha', ?, CURRENT_TIMESTAMP)
        `).run(interaction.guild.id, interaction.user.id, JSON.stringify({ reason }));

        const failEmbed = embedStyles.createErrorEmbed(
            'Verificação Falhada',
            `A verificação falhou: ${reason}\n\nVocê pode tentar novamente clicando no botão de verificação.`
        );

        await interaction.followUp({ embeds: [failEmbed], ephemeral: true });
    },

    /**
     * Lidar com timeout da verificação
     */
    async handleVerificationTimeout(interaction) {
        const embedStyles = new EmbedStyles();
        
        const timeoutEmbed = embedStyles.createWarningEmbed(
            'Tempo Esgotado',
            'O tempo para completar a verificação esgotou.\n\nVocê pode tentar novamente clicando no botão de verificação.'
        );

        await interaction.followUp({ embeds: [timeoutEmbed], ephemeral: true });
    },

    /**
     * Notificar moderadores sobre verificação manual
     */
    async notifyModerators(interaction, config) {
        try {
            const guild = interaction.guild;
            const guildConfig = interaction.client.database.getGuildConfig(guild.id);
            
            if (guildConfig?.mod_log_channel_id) {
                const modChannel = guild.channels.cache.get(guildConfig.mod_log_channel_id);
                if (modChannel) {
                    const embedStyles = new EmbedStyles();
                    
                    const notifyEmbed = embedStyles.createInfoEmbed(
                        'Verificação Manual Solicitada',
                        `**Usuário:** ${interaction.user.tag}\n**ID:** ${interaction.user.id}\n**Conta criada:** <t:${Math.floor(interaction.user.createdTimestamp / 1000)}:R>\n**Entrou no servidor:** <t:${Math.floor(interaction.member.joinedTimestamp / 1000)}:R>\n\nUse \`/verificacao verificar @${interaction.user.tag}\` para aprovar.`
                    );

                    await modChannel.send({ embeds: [notifyEmbed] });
                }
            }
        } catch (error) {
            interaction.client.logger.error('Erro ao notificar moderadores:', error);
        }
    }
};