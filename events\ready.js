/**
 * ========================================
 * EVENTO: READY
 * Executado quando o bot está pronto
 * ========================================
 */

const { Events, ActivityType } = require('discord.js');
const chalk = require('chalk');
const figlet = require('figlet');

module.exports = {
    name: Events.ClientReady,
    once: true,
    async execute(client) {
        try {
            // Banner de inicialização
            console.log(chalk.green('='.repeat(60)));
            console.log(chalk.cyan(figlet.textSync('BOT ONLINE', {
                font: 'Small',
                horizontalLayout: 'default',
                verticalLayout: 'default'
            })));
            console.log(chalk.green('='.repeat(60)));

            // Informações do bot
            console.log(chalk.blue('📊 INFORMAÇÕES DO BOT:'));
            console.log(chalk.white(`   👤 Nome: ${client.user.tag}`));
            console.log(chalk.white(`   🆔 ID: ${client.user.id}`));
            console.log(chalk.white(`   🌐 Servidores: ${client.guilds.cache.size}`));
            console.log(chalk.white(`   👥 Usuários: ${client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0)}`));
            console.log(chalk.white(`   📝 Comandos: ${client.commands.size}`));
            console.log(chalk.white(`   🕐 Iniciado em: ${new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' })}`));
            console.log(chalk.green('='.repeat(60)));

            // Configurar presença do bot
            await setRandomActivity(client);

            // Configurar mudança de atividade a cada 5 minutos
            setInterval(() => {
                setRandomActivity(client);
            }, 300000); // 5 minutos

            // Registrar comandos slash se necessário
            await registerSlashCommands(client);

            // Verificar configurações dos servidores
            await checkGuildConfigurations(client);

            // Inicializar estados dos comandos
            if (client.commandStateManager) {
                await client.commandStateManager.initialize();
            }

            // Inicializar tarefas periódicas
            initializePeriodicTasks(client);

            // Log de inicialização
            client.logger.startup('Bot', 'success', {
                guilds: client.guilds.cache.size,
                users: client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0),
                commands: client.commands.size,
                uptime: process.uptime()
            });

            console.log(chalk.green('✅ Nova Moderação Bot está online e funcionando!'));
            console.log(chalk.yellow('🔗 Dashboard: http://localhost:' + (process.env.WEB_PORT || 3000)));
            console.log(chalk.green('='.repeat(60)));

        } catch (error) {
            console.error(chalk.red('❌ Erro durante a inicialização:'), error);
            client.logger.error('Erro na inicialização do bot:', error);
        }
    }
};

/**
 * Define uma atividade aleatória para o bot
 */
async function setRandomActivity(client) {
    const activities = [
        {
            name: `${client.guilds.cache.size} servidores`,
            type: ActivityType.Watching
        },
        {
            name: 'moderando automaticamente',
            type: ActivityType.Playing
        },
        {
            name: 'protegendo contra raids',
            type: ActivityType.Competing
        },
        {
            name: '/help para comandos',
            type: ActivityType.Listening
        },
        {
            name: 'Nova Moderação Bot',
            type: ActivityType.Playing
        },
        {
            name: 'mensagens suspeitas',
            type: ActivityType.Watching
        },
        {
            name: 'a comunidade brasileira',
            type: ActivityType.Watching
        },
        {
            name: 'IA integrada funcionando',
            type: ActivityType.Playing
        }
    ];

    const randomActivity = activities[Math.floor(Math.random() * activities.length)];
    
    try {
        await client.user.setPresence({
            activities: [randomActivity],
            status: 'online'
        });
        
        client.logger.debug(`Atividade alterada para: ${randomActivity.name}`);
    } catch (error) {
        client.logger.error('Erro ao definir atividade:', error);
    }
}

/**
 * Registra comandos slash globalmente
 */
async function registerSlashCommands(client) {
    try {
        client.logger.info('🔄 Registrando comandos slash...');

        const commands = [];
        for (const command of client.commands.values()) {
            if (command.data) {
                commands.push(command.data.toJSON());
            }
        }

        // Registrar comandos globalmente
        const { REST } = require('@discordjs/rest');
        const { Routes } = require('discord-api-types/v10');

        const restClient = new REST({ version: '10' }).setToken(process.env.DISCORD_TOKEN);

        await restClient.put(
            Routes.applicationCommands(client.user.id),
            { body: commands }
        );

        client.logger.info(`✅ ${commands.length} comandos slash registrados globalmente`);

    } catch (error) {
        client.logger.error('Erro ao registrar comandos slash:', error);
    }
}

/**
 * Verifica e inicializa configurações dos servidores
 */
async function checkGuildConfigurations(client) {
    try {
        client.logger.info('🔍 Verificando configurações dos servidores...');

        // Inicializar cache de configurações
        if (!client.guildConfigs) {
            client.guildConfigs = new Map();
        }

        let configuredGuilds = 0;
        let newGuilds = 0;

        for (const guild of client.guilds.cache.values()) {
            let guildConfig = client.database.getGuildConfig(guild.id);

            if (!guildConfig) {
                // Criar configuração padrão para novos servidores
                const defaultConfig = {
                    prefix: '!',
                    language: 'pt-BR',
                    timezone: 'America/Sao_Paulo',
                    auto_mod_enabled: true,
                    anti_raid_enabled: true,
                    log_enabled: false,
                    log_channel_id: null,
                    log_channel: null,
                    ai_moderation_enabled: false,
                    tickets_enabled: false,
                    music_enabled: false,
                    economy_enabled: false,
                    levels_enabled: false,
                    settings: {}
                };

                client.database.saveGuildConfig(guild.id, defaultConfig);
                guildConfig = defaultConfig;
                newGuilds++;

                client.logger.info(`Configuração padrão criada para: ${guild.name} (${guild.id})`);
            } else {
                configuredGuilds++;

                // Garantir que todas as propriedades necessárias existam
                if (!guildConfig.hasOwnProperty('log_enabled')) guildConfig.log_enabled = false;
                if (!guildConfig.hasOwnProperty('ai_moderation_enabled')) guildConfig.ai_moderation_enabled = false;
                if (!guildConfig.hasOwnProperty('tickets_enabled')) guildConfig.tickets_enabled = false;
                if (!guildConfig.hasOwnProperty('music_enabled')) guildConfig.music_enabled = false;
                if (!guildConfig.hasOwnProperty('economy_enabled')) guildConfig.economy_enabled = false;
                if (!guildConfig.hasOwnProperty('levels_enabled')) guildConfig.levels_enabled = false;
            }

            // Carregar configurações no cache
            client.guildConfigs.set(guild.id, guildConfig);

            console.log(`🔧 [CONFIG CACHE] Carregado para ${guild.name}:`, {
                log_enabled: guildConfig.log_enabled,
                log_channel: guildConfig.log_channel,
                log_channel_id: guildConfig.log_channel_id,
                ai_moderation_enabled: guildConfig.ai_moderation_enabled
            });

            // Verificar e criar cargos necessários se não existirem
            await ensureRequiredRoles(guild, client);
        }

        client.logger.info(`✅ Verificação concluída: ${configuredGuilds} configurados, ${newGuilds} novos`);
        client.logger.info(`📋 Cache de configurações carregado para ${client.guildConfigs.size} servidores`);

    } catch (error) {
        client.logger.error('Erro ao verificar configurações dos servidores:', error);
    }
}

/**
 * Garante que os cargos necessários existam
 */
async function ensureRequiredRoles(guild, client) {
    try {
        // Verificar se o bot tem permissões para gerenciar cargos
        if (!guild.members.me.permissions.has('ManageRoles')) {
            return;
        }

        const requiredRoles = [
            {
                name: '🔇 Silenciado',
                color: '#95a5a6',
                permissions: []
            },
            {
                name: '🚨 Quarentena',
                color: '#e74c3c',
                permissions: []
            }
        ];

        for (const roleData of requiredRoles) {
            let role = guild.roles.cache.find(r => r.name === roleData.name);
            
            if (!role) {
                try {
                    role = await guild.roles.create({
                        name: roleData.name,
                        color: roleData.color,
                        permissions: roleData.permissions,
                        reason: 'Cargo criado automaticamente pelo Nova Moderação Bot'
                    });

                    client.logger.info(`Cargo "${roleData.name}" criado no servidor ${guild.name}`);

                    // Configurar permissões do cargo em todos os canais
                    for (const channel of guild.channels.cache.values()) {
                        if (channel.isTextBased() || channel.isVoiceBased()) {
                            await channel.permissionOverwrites.create(role, {
                                SendMessages: false,
                                Speak: false,
                                Connect: false,
                                AddReactions: false,
                                UseApplicationCommands: false
                            }).catch(() => {}); // Ignorar erros de permissão
                        }
                    }

                } catch (error) {
                    client.logger.error(`Erro ao criar cargo "${roleData.name}" no servidor ${guild.name}:`, error);
                }
            }
        }

    } catch (error) {
        client.logger.error(`Erro ao verificar cargos no servidor ${guild.name}:`, error);
    }
}

/**
 * Inicializa tarefas periódicas
 */
function initializePeriodicTasks(client) {
    try {
        // Limpeza de dados antigos a cada hora
        setInterval(() => {
            cleanupOldData(client);
        }, 3600000); // 1 hora

        // Backup automático a cada 6 horas
        setInterval(() => {
            performAutoBackup(client);
        }, 21600000); // 6 horas

        // Relatório de estatísticas a cada 24 horas
        setInterval(() => {
            generateDailyReport(client);
        }, 86400000); // 24 horas

        // Verificação de saúde do sistema a cada 30 minutos
        setInterval(() => {
            performHealthCheck(client);
        }, 1800000); // 30 minutos

        client.logger.info('✅ Tarefas periódicas inicializadas');

    } catch (error) {
        client.logger.error('Erro ao inicializar tarefas periódicas:', error);
    }
}

/**
 * Limpeza de dados antigos
 */
async function cleanupOldData(client) {
    try {
        client.logger.debug('🧹 Iniciando limpeza de dados antigos...');

        // Limpar logs antigos
        const deletedLogs = client.logger.cleanup(30); // 30 dias

        // Limpar cache de IA
        if (client.autoMod) {
            client.autoMod.cleanupOldData();
        }

        // Limpar dados do anti-raid
        if (client.antiRaid) {
            client.antiRaid.cleanupOldData();
        }

        client.logger.debug(`✅ Limpeza concluída: ${deletedLogs} logs removidos`);

    } catch (error) {
        client.logger.error('Erro na limpeza de dados:', error);
    }
}

/**
 * Backup automático
 */
async function performAutoBackup(client) {
    try {
        if (!process.env.AUTO_BACKUP || process.env.AUTO_BACKUP !== 'true') {
            return;
        }

        client.logger.info('💾 Iniciando backup automático...');

        const backupResult = await client.database.backup();
        
        if (backupResult) {
            client.logger.info(`✅ Backup criado: ${backupResult.path} (${(backupResult.size / 1024 / 1024).toFixed(2)} MB)`);
        } else {
            client.logger.warn('⚠️ Falha no backup automático');
        }

    } catch (error) {
        client.logger.error('Erro no backup automático:', error);
    }
}

/**
 * Relatório diário
 */
async function generateDailyReport(client) {
    try {
        client.logger.info('📊 Gerando relatório diário...');

        const stats = {
            guilds: client.guilds.cache.size,
            users: client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0),
            commands: client.commands.size,
            uptime: process.uptime(),
            memoryUsage: process.memoryUsage(),
            antiRaidStats: client.antiRaid ? client.antiRaid.getStats() : null,
            autoModStats: client.autoMod ? client.autoMod.getStats() : null
        };

        client.logger.report('daily_stats', stats);

    } catch (error) {
        client.logger.error('Erro ao gerar relatório diário:', error);
    }
}

/**
 * Verificação de saúde do sistema
 */
async function performHealthCheck(client) {
    try {
        const memUsage = process.memoryUsage();
        const memUsageMB = memUsage.heapUsed / 1024 / 1024;

        // Verificar uso de memória
        if (memUsageMB > 500) { // 500 MB
            client.logger.warn(`⚠️ Alto uso de memória: ${memUsageMB.toFixed(2)} MB`);
        }

        // Verificar conectividade
        if (!client.isReady()) {
            client.logger.error('❌ Bot não está pronto/conectado');
        }

        // Verificar banco de dados
        if (!client.database.isInitialized) {
            client.logger.error('❌ Banco de dados não está inicializado');
        }

        client.logger.debug(`💚 Health check OK - Memória: ${memUsageMB.toFixed(2)} MB, Uptime: ${(process.uptime() / 3600).toFixed(2)}h`);

    } catch (error) {
        client.logger.error('Erro na verificação de saúde:', error);
    }
}
