[12/06/2025 17:54:24] INFO: 📝 Sistema de logs inicializado
[12/06/2025 17:54:24] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 17:54:24] INFO: 💾 Banco de dados inicializado
[12/06/2025 17:54:24] INFO: 📋 4 comandos carregados
[12/06/2025 17:54:24] INFO: 🎯 Eventos carregados
[12/06/2025 17:54:24] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 17:54:24] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 17:54:24] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 17:54:26] INFO: 🔄 Registrando comandos slash...
[12/06/2025 17:54:27] INFO: ✅ 4 comandos slash registrados globalmente
[12/06/2025 17:54:27] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 17:54:27] INFO: Cargo "🔇 Silenciado" criado no servidor Servidor de EMP
[12/06/2025 17:54:43] INFO: Cargo "🚨 Quarentena" criado no servidor Servidor de EMP
[12/06/2025 17:54:57] INFO: Cargo "🔇 Silenciado" criado no servidor biblioteca dos canudos
[12/06/2025 17:55:01] INFO: Cargo "🚨 Quarentena" criado no servidor biblioteca dos canudos
[12/06/2025 17:55:05] INFO: Cargo "🔇 Silenciado" criado no servidor Nodex | Community
[12/06/2025 17:55:11] INFO: Cargo "🚨 Quarentena" criado no servidor Nodex | Community
[12/06/2025 17:55:17] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 17:55:17] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 17:55:17] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":4,"uptime":55.9263476}
[12/06/2025 17:55:24] ERROR: Uncaught Exception: | Meta: {"error":"this.checkQuarantines is not a function","stack":"TypeError: this.checkQuarantines is not a function\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AntiRaidSystem.js:44:18)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)"}
[12/06/2025 17:59:56] INFO: 📝 Sistema de logs inicializado
[12/06/2025 17:59:56] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 17:59:56] INFO: 💾 Banco de dados inicializado
[12/06/2025 17:59:56] INFO: 📋 4 comandos carregados
[12/06/2025 17:59:56] INFO: 🎯 Eventos carregados
[12/06/2025 17:59:56] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 17:59:56] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 17:59:56] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 17:59:58] INFO: 🔄 Registrando comandos slash...
[12/06/2025 17:59:59] INFO: ✅ 4 comandos slash registrados globalmente
[12/06/2025 17:59:59] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 17:59:59] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 17:59:59] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 17:59:59] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":4,"uptime":5.014189}
[12/06/2025 18:00:48] INFO: 🔄 Iniciando encerramento gracioso...
[12/06/2025 18:01:39] INFO: 📝 Sistema de logs inicializado
[12/06/2025 18:01:39] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 18:01:39] INFO: 💾 Banco de dados inicializado
[12/06/2025 18:01:39] INFO: 📋 4 comandos carregados
[12/06/2025 18:01:39] INFO: 🎯 Eventos carregados
[12/06/2025 18:01:39] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 18:01:39] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 18:01:39] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 18:01:41] INFO: 🔄 Registrando comandos slash...
[12/06/2025 18:01:42] INFO: ✅ 4 comandos slash registrados globalmente
[12/06/2025 18:01:42] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 18:01:42] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 18:01:42] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 18:01:42] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":4,"uptime":4.3649267}
[12/06/2025 18:02:50] INFO: COMANDO: stats executado | Meta: {"command":"stats","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 18:03:24] INFO: Anti-raid ativado no servidor Nodex | Community
[12/06/2025 18:03:24] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 18:03:52] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 18:06:40] ERROR: Uncaught Exception: | Meta: {"error":"this.cleanupOldData is not a function","stack":"TypeError: this.cleanupOldData is not a function\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AntiRaidSystem.js:39:18)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)"}
[12/06/2025 18:15:40] INFO: 📝 Sistema de logs inicializado
[12/06/2025 18:15:40] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 18:15:40] INFO: 💾 Banco de dados inicializado
[12/06/2025 18:15:40] INFO: 📋 4 comandos carregados
[12/06/2025 18:15:41] INFO: 🎯 Eventos carregados
[12/06/2025 18:15:41] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 18:15:41] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 18:15:41] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 18:15:41] INFO: 🌐 Servidor web inicializado
[12/06/2025 18:15:42] INFO: 🔄 Registrando comandos slash...
[12/06/2025 18:15:44] INFO: ✅ 4 comandos slash registrados globalmente
[12/06/2025 18:15:44] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 18:15:44] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 18:15:44] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 18:15:44] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":4,"uptime":16.7407657}
[12/06/2025 18:22:14] INFO: Canal de logs definido: 📋・logs (1382429285788418110) no servidor Nodex | Community
[12/06/2025 18:22:14] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 18:31:34] INFO: 📝 Sistema de logs inicializado
[12/06/2025 18:31:34] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 18:31:34] INFO: 💾 Banco de dados inicializado
[12/06/2025 18:31:34] INFO: 📋 8 comandos carregados
[12/06/2025 18:31:34] INFO: 🎯 Eventos carregados
[12/06/2025 18:31:34] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 18:31:34] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 18:31:34] INFO: 🧠 Sistema de IA para moderação inicializado
[12/06/2025 18:31:34] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 18:31:34] INFO: 🌐 Servidor web inicializado
[12/06/2025 18:31:36] INFO: 🔄 Registrando comandos slash...
[12/06/2025 18:31:37] INFO: ✅ 8 comandos slash registrados globalmente
[12/06/2025 18:31:37] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 18:31:37] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 18:31:37] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 18:31:37] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":8,"uptime":4.5672182}
[12/06/2025 18:33:15] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 18:40:13] INFO: 📝 Sistema de logs inicializado
[12/06/2025 18:40:13] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 18:40:13] INFO: 💾 Banco de dados inicializado
[12/06/2025 18:40:13] INFO: 📋 10 comandos carregados
[12/06/2025 18:40:13] INFO: 🎯 Eventos carregados
[12/06/2025 18:40:13] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 18:40:13] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 18:40:13] INFO: 🧠 Sistema de IA para moderação inicializado
[12/06/2025 18:40:13] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 18:40:13] INFO: 🌐 Servidor web inicializado
[12/06/2025 18:40:15] INFO: 🔄 Registrando comandos slash...
[12/06/2025 18:40:15] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 18:40:15] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 18:40:15] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 18:40:15] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 18:40:15] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":3.9157895}
[12/06/2025 18:40:30] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 18:49:14] INFO: 📝 Sistema de logs inicializado
[12/06/2025 18:49:14] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 18:49:14] INFO: 💾 Banco de dados inicializado
[12/06/2025 18:49:15] INFO: 📋 10 comandos carregados
[12/06/2025 18:49:15] INFO: 🎯 Eventos carregados
[12/06/2025 18:49:15] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 18:49:15] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 18:49:15] INFO: 🧠 Sistema de IA para moderação inicializado
[12/06/2025 18:49:15] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 18:49:15] INFO: 🌐 Servidor web inicializado
[12/06/2025 18:49:17] INFO: 🔄 Registrando comandos slash...
[12/06/2025 18:49:17] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 18:49:17] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 18:49:17] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 18:49:17] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 18:49:17] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":4.389327}
[12/06/2025 18:50:52] INFO: Moderação por IA ativada no servidor Nodex | Community
[12/06/2025 18:50:52] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 19:49:17] INFO: Limpeza de logs concluída: 0 arquivos removidos
[12/06/2025 20:46:48] INFO: 📝 Sistema de logs inicializado
[12/06/2025 20:46:48] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 20:46:48] INFO: 💾 Banco de dados inicializado
[12/06/2025 20:46:48] INFO: 📋 10 comandos carregados
[12/06/2025 20:46:48] INFO: 🎯 Eventos carregados
[12/06/2025 20:46:48] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 20:46:48] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 20:46:48] INFO: 🧠 Sistema de IA para moderação inicializado
[12/06/2025 20:46:48] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 20:46:48] INFO: 🌐 Servidor web inicializado
[12/06/2025 20:46:50] INFO: 🔄 Registrando comandos slash...
[12/06/2025 20:46:51] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 20:46:51] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 20:46:51] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 20:46:51] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 20:46:51] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":4.6845482}
[12/06/2025 20:47:47] INFO: Moderação por IA ativada no servidor Nodex | Community
[12/06/2025 20:47:47] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 20:50:07] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 20:55:05] INFO: 📝 Sistema de logs inicializado
[12/06/2025 20:55:05] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 20:55:05] INFO: 💾 Banco de dados inicializado
[12/06/2025 20:55:05] INFO: 📋 10 comandos carregados
[12/06/2025 20:55:05] INFO: 🎯 Eventos carregados
[12/06/2025 20:55:05] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 20:55:05] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 20:55:05] INFO: 🧠 Sistema de IA para moderação inicializado
[12/06/2025 20:55:05] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 20:55:05] INFO: 🌐 Servidor web inicializado
[12/06/2025 20:55:08] INFO: 🔄 Registrando comandos slash...
[12/06/2025 20:55:09] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 20:55:09] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 20:55:09] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 20:55:09] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 20:55:09] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":4.9799373}
[12/06/2025 20:57:44] INFO: 📝 Sistema de logs inicializado
[12/06/2025 20:57:44] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 20:57:44] INFO: 💾 Banco de dados inicializado
[12/06/2025 20:57:44] INFO: 📋 10 comandos carregados
[12/06/2025 20:57:44] INFO: 🎯 Eventos carregados
[12/06/2025 20:57:44] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 20:57:44] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 20:57:44] INFO: 🧠 Sistema de IA para moderação inicializado
[12/06/2025 20:57:44] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 20:57:44] INFO: 🌐 Servidor web inicializado
[12/06/2025 20:57:47] INFO: 🔄 Registrando comandos slash...
[12/06/2025 20:57:47] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 20:57:47] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 20:57:47] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 20:57:47] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 20:57:47] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":4.3580931}
[12/06/2025 21:00:26] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:401:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:30:17)"}
[12/06/2025 21:00:35] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:401:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:30:17)"}
[12/06/2025 21:00:42] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:401:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:30:17)"}
[12/06/2025 21:00:45] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:401:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:30:17)"}
[12/06/2025 21:00:48] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:401:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:30:17)"}
[12/06/2025 21:00:54] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:401:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:30:17)"}
[12/06/2025 21:01:24] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:401:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:30:17)"}
[12/06/2025 21:01:30] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:401:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:30:17)"}
[12/06/2025 21:01:33] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:401:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:30:17)"}
[12/06/2025 21:01:50] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:401:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:30:17)"}
[12/06/2025 21:02:15] INFO: 🔄 Iniciando encerramento gracioso...
[12/06/2025 21:03:29] INFO: 📝 Sistema de logs inicializado
[12/06/2025 21:03:29] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 21:03:29] INFO: 💾 Banco de dados inicializado
[12/06/2025 21:03:29] INFO: 📋 10 comandos carregados
[12/06/2025 21:03:29] INFO: 🎯 Eventos carregados
[12/06/2025 21:03:29] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 21:03:29] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 21:03:29] INFO: 🧠 Sistema de IA para moderação inicializado
[12/06/2025 21:03:29] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 21:03:29] INFO: 🌐 Servidor web inicializado
[12/06/2025 21:03:31] INFO: 🔄 Registrando comandos slash...
[12/06/2025 21:03:32] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 21:03:32] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 21:03:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 21:03:32] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 21:03:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":4.6869069}
[12/06/2025 21:03:36] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:03:38] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:03:39] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:03:43] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:03:51] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:04:38] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:04:42] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:04:45] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:05:21] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:05:38] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:05:46] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:05:47] ERROR: Erro na análise de IA: | Meta: {"error":"Request failed with status code 401","stack":"AxiosError: Request failed with status code 401\n    at settle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIModeration.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\moderation\\AIModeration.js:69:30)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:394:26)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:07:30] INFO: 🔄 Iniciando encerramento gracioso...
[12/06/2025 21:08:07] INFO: 📝 Sistema de logs inicializado
[12/06/2025 21:08:07] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 21:08:07] INFO: 💾 Banco de dados inicializado
[12/06/2025 21:08:07] INFO: 📋 10 comandos carregados
[12/06/2025 21:08:07] INFO: 🎯 Eventos carregados
[12/06/2025 21:08:07] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 21:08:07] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 21:08:07] WARN: ⚠️ Sistema de IA desabilitado - DEEPSEEK_API_KEY não encontrada
[12/06/2025 21:08:07] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 21:08:07] INFO: 🌐 Servidor web inicializado
[12/06/2025 21:08:11] INFO: 🔄 Registrando comandos slash...
[12/06/2025 21:08:11] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 21:08:11] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 21:08:11] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 21:08:11] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 21:08:11] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":6.4566625}
[12/06/2025 21:12:35] INFO: 📝 Sistema de logs inicializado
[12/06/2025 21:12:35] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 21:12:35] INFO: 💾 Banco de dados inicializado
[12/06/2025 21:12:35] INFO: 📋 10 comandos carregados
[12/06/2025 21:12:35] INFO: 🎯 Eventos carregados
[12/06/2025 21:12:35] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 21:12:35] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 21:12:35] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 21:12:35] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 21:12:35] INFO: 🌐 Servidor web inicializado
[12/06/2025 21:12:37] INFO: 🔄 Registrando comandos slash...
[12/06/2025 21:12:37] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 21:12:37] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 21:12:37] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 21:12:37] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 21:12:37] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":4.1836082}
[12/06/2025 21:13:26] INFO: IA aplicou timeout em nodex_dev: Comportamento tóxico ou assédio detectado pela IA
[12/06/2025 21:13:26] ERROR: Erro ao executar ação da IA: | Meta: {"error":"guildConfig is not defined","stack":"ReferenceError: guildConfig is not defined\n    at executeAIAction (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:564:61)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:425:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:13:36] INFO: IA aplicou timeout em nodex_dev: Comportamento tóxico ou assédio detectado pela IA
[12/06/2025 21:13:36] ERROR: Erro ao executar ação da IA: | Meta: {"error":"guildConfig is not defined","stack":"ReferenceError: guildConfig is not defined\n    at executeAIAction (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:564:61)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:425:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:20:34] INFO: 📝 Sistema de logs inicializado
[12/06/2025 21:20:34] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 21:20:34] INFO: 💾 Banco de dados inicializado
[12/06/2025 21:20:34] INFO: 📋 10 comandos carregados
[12/06/2025 21:20:34] INFO: 🎯 Eventos carregados
[12/06/2025 21:20:34] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 21:20:34] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 21:20:34] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 21:20:34] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 21:20:34] INFO: 🌐 Servidor web inicializado
[12/06/2025 21:20:40] INFO: 🔄 Registrando comandos slash...
[12/06/2025 21:20:41] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 21:20:41] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 21:20:41] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 21:20:41] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 21:20:41] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":8.8102447}
[12/06/2025 21:25:01] INFO: Canal de logs definido: 📋・logs (1382429285788418110) no servidor Nodex | Community
[12/06/2025 21:25:01] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 21:32:06] INFO: 📝 Sistema de logs inicializado
[12/06/2025 21:32:06] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 21:32:06] INFO: 💾 Banco de dados inicializado
[12/06/2025 21:32:06] INFO: 📋 10 comandos carregados
[12/06/2025 21:32:06] INFO: 🎯 Eventos carregados
[12/06/2025 21:32:06] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 21:32:06] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 21:32:06] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 21:32:06] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 21:32:06] INFO: 🌐 Servidor web inicializado
[12/06/2025 21:32:09] INFO: 🔄 Registrando comandos slash...
[12/06/2025 21:32:09] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 21:32:09] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 21:32:09] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 21:32:09] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 21:32:09] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":4.7391832}
[12/06/2025 21:38:16] INFO: 📝 Sistema de logs inicializado
[12/06/2025 21:38:16] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 21:38:16] INFO: 💾 Banco de dados inicializado
[12/06/2025 21:38:16] INFO: 📋 10 comandos carregados
[12/06/2025 21:38:16] INFO: 🎯 Eventos carregados
[12/06/2025 21:38:16] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 21:38:16] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 21:38:16] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 21:38:16] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 21:38:16] INFO: 🌐 Servidor web inicializado
[12/06/2025 21:38:18] INFO: 🔄 Registrando comandos slash...
[12/06/2025 21:38:19] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 21:38:19] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 21:38:19] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 21:38:19] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 21:38:19] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":5.1006254}
[12/06/2025 21:41:55] INFO: 📝 Sistema de logs inicializado
[12/06/2025 21:41:55] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 21:41:55] INFO: 💾 Banco de dados inicializado
[12/06/2025 21:41:55] INFO: 📋 10 comandos carregados
[12/06/2025 21:41:55] INFO: 🎯 Eventos carregados
[12/06/2025 21:41:55] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 21:41:55] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 21:41:55] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 21:41:55] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 21:41:55] INFO: 🌐 Servidor web inicializado
[12/06/2025 21:41:57] INFO: 🔄 Registrando comandos slash...
[12/06/2025 21:41:58] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 21:41:58] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 21:41:58] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 21:41:58] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 21:41:58] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":4.7166288}
[12/06/2025 21:43:36] INFO: Canal de logs definido: 📋・logs (1382429285788418110) no servidor Nodex | Community
[12/06/2025 21:43:36] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 21:43:46] INFO: Moderação por IA ativada no servidor Nodex | Community
[12/06/2025 21:43:46] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 21:45:23] WARN: IA baniu .anaogamer: Discurso de ódio ou ameaças graves detectadas pela IA
[12/06/2025 21:45:23] ERROR: Erro ao executar ação da IA: | Meta: {"error":"guildConfig is not defined","stack":"ReferenceError: guildConfig is not defined\n    at executeAIAction (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:564:61)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:425:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[12/06/2025 21:47:21] INFO: 📝 Sistema de logs inicializado
[12/06/2025 21:47:21] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 21:47:21] INFO: 💾 Banco de dados inicializado
[12/06/2025 21:47:21] INFO: 📋 10 comandos carregados
[12/06/2025 21:47:21] INFO: 🎯 Eventos carregados
[12/06/2025 21:47:21] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 21:47:21] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 21:47:21] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 21:47:21] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 21:47:21] INFO: 🌐 Servidor web inicializado
[12/06/2025 21:47:23] INFO: 🔄 Registrando comandos slash...
[12/06/2025 21:47:24] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 21:47:24] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 21:47:24] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 21:47:24] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 21:47:24] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":4.4330872}
[12/06/2025 21:50:12] INFO: 📝 Sistema de logs inicializado
[12/06/2025 21:50:12] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 21:50:12] INFO: 💾 Banco de dados inicializado
[12/06/2025 21:50:12] INFO: 📋 10 comandos carregados
[12/06/2025 21:50:12] INFO: 🎯 Eventos carregados
[12/06/2025 21:50:12] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 21:50:12] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 21:50:12] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 21:50:12] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 21:50:12] INFO: 🌐 Servidor web inicializado
[12/06/2025 21:50:14] INFO: 🔄 Registrando comandos slash...
[12/06/2025 21:50:15] INFO: ✅ 10 comandos slash registrados globalmente
[12/06/2025 21:50:15] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 21:50:15] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 21:50:15] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 21:50:15] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":10,"uptime":4.4184448}
[12/06/2025 21:50:36] INFO: Moderação por IA ativada no servidor Nodex | Community
[12/06/2025 21:50:36] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 21:50:46] INFO: Canal de logs definido: 📋・logs (1382429285788418110) no servidor Nodex | Community
[12/06/2025 21:50:46] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 21:59:38] INFO: 📝 Sistema de logs inicializado
[12/06/2025 21:59:38] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 21:59:38] INFO: 💾 Banco de dados inicializado
[12/06/2025 21:59:38] INFO: 📋 11 comandos carregados
[12/06/2025 21:59:39] INFO: 🎯 Eventos carregados
[12/06/2025 21:59:39] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 21:59:39] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 21:59:39] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 21:59:39] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 21:59:39] INFO: 🌐 Servidor web inicializado
[12/06/2025 21:59:41] INFO: 🔄 Registrando comandos slash...
[12/06/2025 21:59:42] INFO: ✅ 11 comandos slash registrados globalmente
[12/06/2025 21:59:42] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 21:59:42] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 21:59:42] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 21:59:42] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":5.0886247}
[12/06/2025 22:00:10] INFO: Mensagem deletada logada: undefined em #🔐・privado-staff
[12/06/2025 22:00:11] INFO: Mensagem deletada logada: undefined em #🔐・privado-staff
[12/06/2025 22:00:13] INFO: Mensagem deletada logada: undefined em #🔐・privado-staff
[12/06/2025 22:00:15] INFO: Mensagem deletada logada: undefined em #🔐・privado-staff
[12/06/2025 22:00:35] INFO: Mensagem deletada logada: undefined em #📋・logs
[12/06/2025 22:00:37] INFO: Mensagem deletada logada: undefined em #📋・logs
[12/06/2025 22:05:04] INFO: 📝 Sistema de logs inicializado
[12/06/2025 22:05:04] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 22:05:04] INFO: 💾 Banco de dados inicializado
[12/06/2025 22:05:04] INFO: 📋 11 comandos carregados
[12/06/2025 22:05:04] INFO: 🎯 Eventos carregados
[12/06/2025 22:05:04] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 22:05:04] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 22:05:04] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 22:05:04] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 22:05:04] INFO: 🌐 Servidor web inicializado
[12/06/2025 22:05:07] INFO: 🔄 Registrando comandos slash...
[12/06/2025 22:05:08] INFO: ✅ 11 comandos slash registrados globalmente
[12/06/2025 22:05:08] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 22:05:08] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 22:05:08] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 22:05:08] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":5.3473845}
[12/06/2025 22:10:43] INFO: 📝 Sistema de logs inicializado
[12/06/2025 22:10:43] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 22:10:43] INFO: 💾 Banco de dados inicializado
[12/06/2025 22:10:43] INFO: 📋 11 comandos carregados
[12/06/2025 22:10:43] INFO: 🎯 Eventos carregados
[12/06/2025 22:10:43] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 22:10:43] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 22:10:43] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 22:10:43] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 22:10:43] INFO: 🌐 Servidor web inicializado
[12/06/2025 22:10:45] INFO: 🔄 Registrando comandos slash...
[12/06/2025 22:10:46] INFO: ✅ 11 comandos slash registrados globalmente
[12/06/2025 22:10:46] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 22:10:46] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 22:10:46] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 22:10:46] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":5.095839}
[12/06/2025 22:13:07] INFO: Canal de logs definido: 📋・logs (1382429285788418110) no servidor Nodex | Community
[12/06/2025 22:13:07] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 22:13:35] INFO: Mensagem deletada logada: emp2866 em #📋・logs
[12/06/2025 22:14:27] INFO: Mensagem editada logada: emp2866 em #📋・logs
[12/06/2025 22:15:26] INFO: Moderação por IA ativada no servidor Nodex | Community
[12/06/2025 22:15:26] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 22:17:07] ERROR: Uncaught Exception: | Meta: {"error":"Cannot read properties of null (reading 'metadata')","stack":"TypeError: Cannot read properties of null (reading 'metadata')\n    at C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\database\\DatabaseManager.js:342:31\n    at replacement (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\sqlite3\\lib\\trace.js:25:27)\n    at Statement.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\sqlite3\\lib\\sqlite3.js:142:9)"}
[12/06/2025 22:19:26] INFO: 📝 Sistema de logs inicializado
[12/06/2025 22:19:26] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 22:19:26] INFO: 💾 Banco de dados inicializado
[12/06/2025 22:19:26] INFO: 📋 11 comandos carregados
[12/06/2025 22:19:26] INFO: 🎯 Eventos carregados
[12/06/2025 22:19:26] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 22:19:26] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 22:19:26] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 22:19:26] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 22:19:26] INFO: 🌐 Servidor web inicializado
[12/06/2025 22:19:28] INFO: 🔄 Registrando comandos slash...
[12/06/2025 22:19:28] INFO: ✅ 11 comandos slash registrados globalmente
[12/06/2025 22:19:28] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 22:19:28] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 22:19:28] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 22:19:28] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":4.4227903}
[12/06/2025 22:20:23] INFO: Moderação por IA ativada no servidor Nodex | Community
[12/06/2025 22:20:23] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 22:20:34] INFO: Canal de logs definido: 📋・logs (1382429285788418110) no servidor Nodex | Community
[12/06/2025 22:20:34] INFO: COMANDO: config executado | Meta: {"command":"config","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[12/06/2025 22:29:25] INFO: 📝 Sistema de logs inicializado
[12/06/2025 22:29:25] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 22:29:25] INFO: 💾 Banco de dados inicializado
[12/06/2025 22:29:25] INFO: 📋 11 comandos carregados
[12/06/2025 22:29:25] INFO: 🎯 Eventos carregados
[12/06/2025 22:29:25] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 22:29:25] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 22:29:25] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 22:29:25] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 22:29:25] INFO: 🌐 Servidor web inicializado
[12/06/2025 22:29:28] INFO: 🔄 Registrando comandos slash...
[12/06/2025 22:29:28] INFO: ✅ 11 comandos slash registrados globalmente
[12/06/2025 22:29:28] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 22:29:28] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 22:29:28] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 22:29:28] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":4.8046765}
[12/06/2025 23:29:28] INFO: Limpeza de logs concluída: 0 arquivos removidos
[12/06/2025 23:56:40] INFO: 📝 Sistema de logs inicializado
[12/06/2025 23:56:40] INFO: ⚙️ Gerenciador de configurações inicializado
[12/06/2025 23:56:41] INFO: 💾 Banco de dados inicializado
[12/06/2025 23:56:41] INFO: 📋 11 comandos carregados
[12/06/2025 23:56:41] INFO: 🎯 Eventos carregados
[12/06/2025 23:56:41] INFO: 🛡️ Sistema Anti-Raid inicializado
[12/06/2025 23:56:41] INFO: 🤖 Sistema de Auto-Moderação inicializado
[12/06/2025 23:56:41] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[12/06/2025 23:56:41] INFO: 🛡️ Sistemas de moderação inicializados
[12/06/2025 23:56:41] INFO: 🌐 Servidor web inicializado
[12/06/2025 23:56:42] INFO: 🔄 Registrando comandos slash...
[12/06/2025 23:56:43] INFO: ✅ 11 comandos slash registrados globalmente
[12/06/2025 23:56:43] INFO: 🔍 Verificando configurações dos servidores...
[12/06/2025 23:56:43] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[12/06/2025 23:56:43] INFO: ✅ Tarefas periódicas inicializadas
[12/06/2025 23:56:43] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":4.3762898}
