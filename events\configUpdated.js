/**
 * ========================================
 * EVENT: CONFIG UPDATED
 * Aplica configurações em tempo real quando alteradas no dashboard
 * ========================================
 */

module.exports = {
    name: 'configUpdated',
    once: false,
    async execute(guildId, config, client) {
        try {
            console.log(`🔄 [CONFIG UPDATE] Aplicando configurações para servidor ${guildId}`);

            // Verificar se o cliente está disponível
            if (!client || !client.guilds) {
                console.log(`❌ [CONFIG UPDATE] Cliente não disponível`);
                return;
            }

            // Obter o servidor
            const guild = client.guilds.cache.get(guildId);
            if (!guild) {
                console.log(`❌ [CONFIG UPDATE] Servidor ${guildId} não encontrado`);
                return;
            }

            // Inicializar cache de configurações se não existir
            if (!client.guildConfigs) {
                client.guildConfigs = new Map();
            }

            // Obter configurações atuais do cache ou criar novas
            let currentCache = client.guildConfigs.get(guildId) || {};

            // Aplicar configurações de settings
            if (config.settings) {
                let settings;
                try {
                    settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
                } catch (e) {
                    settings = {};
                }

                console.log(`🔧 [CONFIG UPDATE] Aplicando settings:`, settings);

                // Atualizar cache com todas as configurações
                Object.assign(currentCache, settings);

                // Aplicar configurações específicas
                if (settings.ai_moderation_enabled !== undefined) {
                    console.log(`🧠 [CONFIG UPDATE] IA Moderação: ${settings.ai_moderation_enabled ? 'ATIVADA' : 'DESATIVADA'}`);
                }

                if (settings.log_enabled !== undefined) {
                    console.log(`📝 [CONFIG UPDATE] Logs Gerais: ${settings.log_enabled ? 'ATIVADOS' : 'DESATIVADOS'}`);
                }

                if (settings.log_channel) {
                    console.log(`📝 [CONFIG UPDATE] Canal de Logs: ${settings.log_channel}`);
                }

                if (settings.tickets_enabled !== undefined) {
                    console.log(`🎫 [CONFIG UPDATE] Tickets: ${settings.tickets_enabled ? 'ATIVADOS' : 'DESATIVADOS'}`);
                }

                if (settings.music_enabled !== undefined) {
                    console.log(`🎵 [CONFIG UPDATE] Música: ${settings.music_enabled ? 'ATIVADA' : 'DESATIVADA'}`);
                }

                if (settings.economy_enabled !== undefined) {
                    console.log(`💰 [CONFIG UPDATE] Economia: ${settings.economy_enabled ? 'ATIVADA' : 'DESATIVADA'}`);
                }

                if (settings.levels_enabled !== undefined) {
                    console.log(`🏆 [CONFIG UPDATE] Níveis: ${settings.levels_enabled ? 'ATIVADOS' : 'DESATIVADOS'}`);
                }

                if (settings.analytics_enabled !== undefined) {
                    console.log(`📊 [CONFIG UPDATE] Analytics: ${settings.analytics_enabled ? 'ATIVADO' : 'DESATIVADO'}`);
                }
            }

            // Aplicar configurações básicas do servidor
            if (config.log_channel_id) {
                currentCache.log_channel_id = config.log_channel_id;
                const logChannel = guild.channels.cache.get(config.log_channel_id);
                if (logChannel) {
                    console.log(`📝 [CONFIG UPDATE] Canal de logs definido: #${logChannel.name}`);
                }
            }

            if (config.mute_role_id) {
                currentCache.mute_role_id = config.mute_role_id;
                const muteRole = guild.roles.cache.get(config.mute_role_id);
                if (muteRole) {
                    console.log(`🔇 [CONFIG UPDATE] Cargo de timeout definido: @${muteRole.name}`);
                }
            }

            // Verificar prefixo tanto na raiz quanto em settings
            const prefix = config.prefix || settings.prefix;
            if (prefix) {
                currentCache.prefix = prefix;
                console.log(`🔧 [CONFIG UPDATE] Prefixo atualizado: ${prefix}`);
            }

            // Verificar idioma tanto na raiz quanto em settings
            const language = config.language || settings.language;
            if (language) {
                currentCache.language = language;
                console.log(`🌐 [CONFIG UPDATE] Idioma atualizado: ${language}`);
            }

            // Verificar timezone tanto na raiz quanto em settings
            const timezone = config.timezone || settings.timezone;
            if (timezone) {
                currentCache.timezone = timezone;
                console.log(`🕐 [CONFIG UPDATE] Fuso horário atualizado: ${timezone}`);
            }

            if (config.auto_mod_enabled !== undefined) {
                currentCache.auto_mod_enabled = config.auto_mod_enabled;
                console.log(`🛡️ [CONFIG UPDATE] Auto-moderação: ${config.auto_mod_enabled ? 'ATIVADA' : 'DESATIVADA'}`);
            }

            // Salvar configurações no cache
            client.guildConfigs.set(guildId, currentCache);

            // Log das configurações finais aplicadas
            console.log(`✅ [CONFIG UPDATE] Cache atualizado para ${guild.name}:`, {
                log_enabled: currentCache.log_enabled,
                log_channel: currentCache.log_channel,
                log_channel_id: currentCache.log_channel_id,
                ai_moderation_enabled: currentCache.ai_moderation_enabled,
                tickets_enabled: currentCache.tickets_enabled,
                music_enabled: currentCache.music_enabled,
                economy_enabled: currentCache.economy_enabled,
                levels_enabled: currentCache.levels_enabled,
                analytics_enabled: currentCache.analytics_enabled
            });

            // APLICAR CONFIGURAÇÕES NOS SISTEMAS
            await applySystemConfigurations(client, guild, currentCache);

            // Emitir evento para outros módulos que possam precisar reagir
            client.emit('guildConfigChanged', guildId, currentCache);

            console.log(`✅ [CONFIG UPDATE] Configurações aplicadas com sucesso para ${guild.name}`);

        } catch (error) {
            console.error(`❌ [CONFIG UPDATE] Erro ao aplicar configurações:`, error);
        }
    }
};

/**
 * Aplica as configurações nos sistemas do bot
 */
async function applySystemConfigurations(client, guild, config) {
    try {
        console.log(`🔧 [SYSTEM CONFIG] Aplicando configurações nos sistemas para ${guild.name}...`);

        // 🧠 SISTEMA DE IA MODERAÇÃO
        if (config.ai_moderation_enabled !== undefined) {
            if (config.ai_moderation_enabled && client.aiMod) {
                console.log(`🧠 [SYSTEM CONFIG] ✅ Ativando IA Moderação`);
                // IA já funciona através do cache, apenas log
            } else {
                console.log(`🧠 [SYSTEM CONFIG] ❌ Desativando IA Moderação`);
            }
        }



        // 📊 SISTEMA DE ANALYTICS
        if (config.analytics_enabled !== undefined) {
            if (config.analytics_enabled && client.analytics) {
                console.log(`📊 [SYSTEM CONFIG] ✅ Ativando Sistema de Analytics`);
                try {
                    const result = await client.analytics.enableForGuild(guild.id);
                    if (result && result.success) {
                        console.log(`📊 [SYSTEM CONFIG] Analytics ativado para guild ${guild.id}`);
                    }
                } catch (error) {
                    console.error(`📊 [SYSTEM CONFIG] Erro ao ativar analytics:`, error.message);
                }
            } else {
                console.log(`📊 [SYSTEM CONFIG] ❌ Sistema de Analytics desativado`);
            }
        }

        // 🏆 SISTEMA DE NÍVEIS
        if (config.levels_enabled !== undefined) {
            if (config.levels_enabled && client.levels) {
                console.log(`🏆 [SYSTEM CONFIG] ✅ Ativando Sistema de Níveis`);
                // Sistema de níveis funciona automaticamente através do cache
            } else {
                console.log(`🏆 [SYSTEM CONFIG] ❌ Sistema de Níveis desativado`);
            }
        }

        // 📊 SISTEMA DE ANALYTICS
        if (config.analytics_enabled !== undefined) {
            if (config.analytics_enabled && client.analytics) {
                console.log(`📊 [SYSTEM CONFIG] ✅ Ativando Sistema de Analytics`);
                try {
                    // Ativar coleta de dados
                    await client.analytics.enableForGuild(guild.id);
                    console.log(`📊 [SYSTEM CONFIG] Analytics ativado para ${guild.name}`);
                } catch (error) {
                    console.error(`📊 [SYSTEM CONFIG] Erro ao ativar analytics:`, error.message);
                }
            } else {
                console.log(`📊 [SYSTEM CONFIG] ❌ Sistema de Analytics desativado`);
                if (client.analytics) {
                    try {
                        await client.analytics.disableForGuild(guild.id);
                    } catch (error) {
                        console.error(`📊 [SYSTEM CONFIG] Erro ao desativar analytics:`, error.message);
                    }
                }
            }
        }

        // 🎉 SISTEMA DE EVENTOS
        if (config.events_enabled !== undefined) {
            if (config.events_enabled && client.events) {
                console.log(`🎉 [SYSTEM CONFIG] ✅ Ativando Sistema de Eventos`);
                // Sistema de eventos funciona automaticamente através do cache
            } else {
                console.log(`🎉 [SYSTEM CONFIG] ❌ Sistema de Eventos desativado`);
            }
        }

        console.log(`✅ [SYSTEM CONFIG] Configurações aplicadas nos sistemas para ${guild.name}`);

    } catch (error) {
        console.error(`❌ [SYSTEM CONFIG] Erro ao aplicar configurações nos sistemas:`, error);
    }
}