[13/06/2025 00:21:41] INFO: 📝 Sistema de logs inicializado
[13/06/2025 00:21:41] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 00:21:41] INFO: 💾 Banco de dados inicializado
[13/06/2025 00:21:42] INFO: 📋 11 comandos carregados
[13/06/2025 00:21:42] INFO: 🎯 Eventos carregados
[13/06/2025 00:21:42] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 00:21:42] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 00:21:42] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 00:21:42] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 00:21:42] INFO: 🌐 Servidor web inicializado
[13/06/2025 00:21:44] INFO: 🔄 Registrando comandos slash...
[13/06/2025 00:21:44] INFO: ✅ 11 comandos slash registrados globalmente
[13/06/2025 00:21:44] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 00:21:44] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 00:21:44] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 00:21:44] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":4.5922589}
[13/06/2025 00:32:08] INFO: 📝 Sistema de logs inicializado
[13/06/2025 00:32:08] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 00:32:08] INFO: 💾 Banco de dados inicializado
[13/06/2025 00:32:08] INFO: 📋 11 comandos carregados
[13/06/2025 00:32:08] INFO: 🎯 Eventos carregados
[13/06/2025 00:32:08] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 00:32:08] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 00:32:08] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 00:32:08] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 00:32:08] INFO: 🌐 Servidor web inicializado
[13/06/2025 00:32:10] INFO: 🔄 Registrando comandos slash...
[13/06/2025 00:32:11] INFO: ✅ 11 comandos slash registrados globalmente
[13/06/2025 00:32:11] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 00:32:11] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 00:32:11] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 00:32:11] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":4.8389338}
[13/06/2025 00:38:09] INFO: 📝 Sistema de logs inicializado
[13/06/2025 00:38:09] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 00:38:09] INFO: 💾 Banco de dados inicializado
[13/06/2025 00:38:09] INFO: 📋 11 comandos carregados
[13/06/2025 00:38:09] INFO: 🎯 Eventos carregados
[13/06/2025 00:38:09] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 00:38:09] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 00:38:09] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 00:38:09] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 00:38:09] INFO: 🌐 Servidor web inicializado
[13/06/2025 00:38:11] INFO: 🔄 Registrando comandos slash...
[13/06/2025 00:38:11] INFO: ✅ 11 comandos slash registrados globalmente
[13/06/2025 00:38:11] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 00:38:11] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 00:38:11] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 00:38:11] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":3.5715372}
[13/06/2025 00:39:55] INFO: 📝 Sistema de logs inicializado
[13/06/2025 00:39:55] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 00:39:55] INFO: 💾 Banco de dados inicializado
[13/06/2025 00:39:55] INFO: 📋 11 comandos carregados
[13/06/2025 00:39:55] INFO: 🎯 Eventos carregados
[13/06/2025 00:39:55] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 00:39:55] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 00:39:55] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 00:39:55] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 00:39:55] INFO: 🌐 Servidor web inicializado
[13/06/2025 00:39:56] INFO: 🔄 Registrando comandos slash...
[13/06/2025 00:39:57] INFO: ✅ 11 comandos slash registrados globalmente
[13/06/2025 00:39:57] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 00:39:57] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 00:39:57] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 00:39:57] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":3.7264407}
[13/06/2025 00:41:21] INFO: 📝 Sistema de logs inicializado
[13/06/2025 00:41:21] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 00:41:21] INFO: 💾 Banco de dados inicializado
[13/06/2025 00:41:21] INFO: 📋 11 comandos carregados
[13/06/2025 00:41:21] INFO: 🎯 Eventos carregados
[13/06/2025 00:41:21] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 00:41:21] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 00:41:21] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 00:41:21] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 00:41:21] INFO: 🌐 Servidor web inicializado
[13/06/2025 00:41:22] INFO: 🔄 Registrando comandos slash...
[13/06/2025 00:41:23] INFO: ✅ 11 comandos slash registrados globalmente
[13/06/2025 00:41:23] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 00:41:23] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 00:41:23] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 00:41:23] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":3.9083763}
[13/06/2025 01:04:06] INFO: 📝 Sistema de logs inicializado
[13/06/2025 01:04:06] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 01:04:06] INFO: 💾 Banco de dados inicializado
[13/06/2025 01:04:07] INFO: 📋 11 comandos carregados
[13/06/2025 01:04:07] INFO: 🎯 Eventos carregados
[13/06/2025 01:04:07] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 01:04:07] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 01:04:07] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 01:04:07] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 01:04:07] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 01:04:07] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 01:04:07] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 01:04:07] INFO: 🌐 Servidor web inicializado
[13/06/2025 01:04:08] INFO: 🔄 Registrando comandos slash...
[13/06/2025 01:04:08] INFO: ✅ 11 comandos slash registrados globalmente
[13/06/2025 01:04:08] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 01:04:08] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 01:04:08] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 01:04:08] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":3.7678827}
[13/06/2025 01:12:11] INFO: 📝 Sistema de logs inicializado
[13/06/2025 01:12:11] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 01:12:11] INFO: 💾 Banco de dados inicializado
[13/06/2025 01:12:11] INFO: 📋 11 comandos carregados
[13/06/2025 01:12:11] INFO: 🎯 Eventos carregados
[13/06/2025 01:12:11] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 01:12:11] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 01:12:11] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 01:12:11] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 01:12:11] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 01:12:11] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 01:12:11] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 01:12:11] INFO: 🌐 Servidor web inicializado
[13/06/2025 01:12:12] INFO: 🔄 Registrando comandos slash...
[13/06/2025 01:12:13] INFO: ✅ 11 comandos slash registrados globalmente
[13/06/2025 01:12:13] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 01:12:13] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 01:12:13] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 01:12:13] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":11,"uptime":3.8962951}
[13/06/2025 01:20:38] INFO: 📝 Sistema de logs inicializado
[13/06/2025 01:20:38] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 01:20:38] INFO: 💾 Banco de dados inicializado
[13/06/2025 01:20:38] INFO: 📋 12 comandos carregados
[13/06/2025 01:20:38] INFO: 🎯 Eventos carregados
[13/06/2025 01:20:38] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 01:20:38] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 01:20:38] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 01:20:38] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 01:20:38] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 01:20:38] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 01:20:38] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 01:20:38] INFO: 🌐 Servidor web inicializado
[13/06/2025 01:20:40] INFO: 🔄 Registrando comandos slash...
[13/06/2025 01:20:40] INFO: ✅ 12 comandos slash registrados globalmente
[13/06/2025 01:20:40] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 01:20:40] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 01:20:40] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 01:20:40] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":12,"uptime":3.9642849}
[13/06/2025 01:22:28] ERROR: Uncaught Exception: | Meta: {"error":"The reply to this interaction has not been sent or deferred.","stack":"Error [InteractionNotReplied]: The reply to this interaction has not been sent or deferred.\n    at ButtonInteraction.editReply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:247:48)\n    at TicketSystem.handleCreateTicket (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\TicketSystem.js:133:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\TicketSystem.js:30:17)"}
[13/06/2025 01:40:42] INFO: 📝 Sistema de logs inicializado
[13/06/2025 01:40:42] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 01:40:42] INFO: 💾 Banco de dados inicializado
[13/06/2025 01:40:42] INFO: 📋 16 comandos carregados
[13/06/2025 01:40:42] INFO: 🎯 Eventos carregados
[13/06/2025 01:40:42] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 01:40:42] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 01:40:42] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 01:40:42] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 01:40:42] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 01:40:42] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 01:40:42] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 01:40:42] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 01:40:42] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 01:40:42] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 01:40:42] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 01:40:42] INFO: 🌐 Servidor web inicializado
[13/06/2025 01:40:45] INFO: 🔄 Registrando comandos slash...
[13/06/2025 01:40:46] INFO: ✅ 16 comandos slash registrados globalmente
[13/06/2025 01:40:46] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 01:40:46] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 01:40:46] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 01:40:46] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":16,"uptime":5.7191234}
[13/06/2025 01:44:02] ERROR: Uncaught Exception: | Meta: {"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ButtonInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async TicketSystem.handleCreateTicket (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\TicketSystem.js:141:17)\n    at async Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\systems\\TicketSystem.js:30:17)"}
[13/06/2025 01:49:31] INFO: 📝 Sistema de logs inicializado
[13/06/2025 01:49:31] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 01:49:31] INFO: 💾 Banco de dados inicializado
[13/06/2025 01:49:31] INFO: 📋 16 comandos carregados
[13/06/2025 01:49:31] INFO: 🎯 Eventos carregados
[13/06/2025 01:49:31] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 01:49:31] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 01:49:31] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 01:49:31] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 01:49:31] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 01:49:31] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 01:49:31] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 01:49:31] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 01:49:31] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 01:49:31] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 01:49:31] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 01:49:31] INFO: 🌐 Servidor web inicializado
[13/06/2025 01:49:33] INFO: 🔄 Registrando comandos slash...
[13/06/2025 01:49:33] INFO: ✅ 16 comandos slash registrados globalmente
[13/06/2025 01:49:33] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 01:49:33] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 01:49:33] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 01:49:33] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":16,"uptime":3.8271693}
[13/06/2025 01:52:23] INFO: 📝 Sistema de logs inicializado
[13/06/2025 01:52:23] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 01:52:23] INFO: 💾 Banco de dados inicializado
[13/06/2025 01:52:23] INFO: 📋 16 comandos carregados
[13/06/2025 01:52:23] INFO: 🎯 Eventos carregados
[13/06/2025 01:52:23] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 01:52:23] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 01:52:23] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 01:52:23] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 01:52:23] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 01:52:23] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 01:52:23] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 01:52:23] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 01:52:23] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 01:52:23] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 01:52:23] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 01:52:23] INFO: 🌐 Servidor web inicializado
[13/06/2025 01:52:24] INFO: 🔄 Registrando comandos slash...
[13/06/2025 01:52:25] INFO: ✅ 16 comandos slash registrados globalmente
[13/06/2025 01:52:25] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 01:52:25] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 01:52:25] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 01:52:25] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":16,"uptime":3.6625275}
[13/06/2025 02:04:02] INFO: 📝 Sistema de logs inicializado
[13/06/2025 02:04:02] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 02:04:02] INFO: 💾 Banco de dados inicializado
[13/06/2025 02:04:02] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\eventos\evento.js: | Meta: {"error":"Received one or more errors","stack":"Error: Received one or more errors\n    at _UnionValidator.handle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@sapphire\\shapeshift\\dist\\cjs\\index.cjs:1965:23)\n    at _UnionValidator.parse (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@sapphire\\shapeshift\\dist\\cjs\\index.cjs:972:90)\n    at validateDefaultMemberPermissions (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\builders\\dist\\index.js:2479:36)\n    at MixedClass.setDefaultMemberPermissions (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\builders\\dist\\index.js:2673:29)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\eventos\\evento.js:17:10)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)"}
[13/06/2025 02:04:02] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\clear.js: | Meta: {"error":"Received one or more errors","stack":"Error: Received one or more errors\n    at _UnionValidator.handle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@sapphire\\shapeshift\\dist\\cjs\\index.cjs:1965:23)\n    at _UnionValidator.parse (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@sapphire\\shapeshift\\dist\\cjs\\index.cjs:972:90)\n    at validateDefaultMemberPermissions (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\builders\\dist\\index.js:2479:36)\n    at MixedClass.setDefaultMemberPermissions (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\builders\\dist\\index.js:2673:29)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\clear.js:20:10)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)"}
[13/06/2025 02:04:02] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\moderacao\unban.js: | Meta: {"error":"Received one or more errors","stack":"Error: Received one or more errors\n    at _UnionValidator.handle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@sapphire\\shapeshift\\dist\\cjs\\index.cjs:1965:23)\n    at _UnionValidator.parse (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@sapphire\\shapeshift\\dist\\cjs\\index.cjs:972:90)\n    at validateDefaultMemberPermissions (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\builders\\dist\\index.js:2479:36)\n    at MixedClass.setDefaultMemberPermissions (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\builders\\dist\\index.js:2673:29)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\moderacao\\unban.js:20:10)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)"}
[13/06/2025 02:04:02] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\utilidades\say.js: | Meta: {"error":"Received one or more errors","stack":"Error: Received one or more errors\n    at _UnionValidator.handle (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@sapphire\\shapeshift\\dist\\cjs\\index.cjs:1965:23)\n    at _UnionValidator.parse (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@sapphire\\shapeshift\\dist\\cjs\\index.cjs:972:90)\n    at validateDefaultMemberPermissions (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\builders\\dist\\index.js:2479:36)\n    at MixedClass.setDefaultMemberPermissions (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\builders\\dist\\index.js:2673:29)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\utilidades\\say.js:20:10)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)"}
[13/06/2025 02:04:02] INFO: 📋 24 comandos carregados
[13/06/2025 02:04:02] INFO: 🎯 Eventos carregados
[13/06/2025 02:04:02] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 02:04:02] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 02:04:02] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 02:04:02] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 02:04:02] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 02:04:02] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 02:04:02] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 02:04:02] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 02:04:02] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 02:04:02] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 02:04:02] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 02:04:02] INFO: 🌐 Servidor web inicializado
[13/06/2025 02:04:03] INFO: 🔄 Registrando comandos slash...
[13/06/2025 02:04:04] INFO: ✅ 24 comandos slash registrados globalmente
[13/06/2025 02:04:04] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 02:04:04] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 02:04:04] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 02:04:04] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":24,"uptime":4.3897454}
[13/06/2025 02:06:06] INFO: 📝 Sistema de logs inicializado
[13/06/2025 02:06:06] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 02:06:06] INFO: 💾 Banco de dados inicializado
[13/06/2025 02:06:06] INFO: 📋 28 comandos carregados
[13/06/2025 02:06:06] INFO: 🎯 Eventos carregados
[13/06/2025 02:06:06] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 02:06:06] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 02:06:06] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 02:06:06] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 02:06:06] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 02:06:06] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 02:06:07] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 02:06:07] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 02:06:07] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 02:06:07] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 02:06:07] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 02:06:07] INFO: 🌐 Servidor web inicializado
[13/06/2025 02:06:08] INFO: 🔄 Registrando comandos slash...
[13/06/2025 02:06:09] INFO: ✅ 28 comandos slash registrados globalmente
[13/06/2025 02:06:09] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 02:06:09] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 02:06:09] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 02:06:09] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.9770364}
[13/06/2025 02:12:27] INFO: 📝 Sistema de logs inicializado
[13/06/2025 02:12:27] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 02:12:27] INFO: 💾 Banco de dados inicializado
[13/06/2025 02:12:27] INFO: 📋 28 comandos carregados
[13/06/2025 02:12:27] INFO: 🎯 Eventos carregados
[13/06/2025 02:12:27] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 02:12:27] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 02:12:27] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 02:12:27] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 02:12:27] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 02:12:27] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 02:12:27] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 02:12:27] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 02:12:27] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 02:12:27] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 02:12:27] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 02:12:27] INFO: 🌐 Servidor web inicializado
[13/06/2025 02:12:28] INFO: 🔄 Registrando comandos slash...
[13/06/2025 02:12:29] INFO: ✅ 28 comandos slash registrados globalmente
[13/06/2025 02:12:29] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 02:12:29] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 02:12:29] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 02:12:29] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.600856}
[13/06/2025 02:22:29] INFO: 📝 Sistema de logs inicializado
[13/06/2025 02:22:29] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 02:22:29] INFO: 💾 Banco de dados inicializado
[13/06/2025 02:22:29] INFO: 📋 28 comandos carregados
[13/06/2025 02:22:29] INFO: 🎯 Eventos carregados
[13/06/2025 02:22:29] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 02:22:29] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 02:22:29] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 02:22:29] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 02:22:29] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 02:22:29] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 02:22:29] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 02:22:29] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 02:22:29] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 02:22:29] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 02:22:29] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 02:22:29] INFO: 🌐 Servidor web inicializado
[13/06/2025 02:22:30] INFO: 🔄 Registrando comandos slash...
[13/06/2025 02:22:31] INFO: ✅ 28 comandos slash registrados globalmente
[13/06/2025 02:22:31] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 02:22:31] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 02:22:31] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 02:22:31] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.1324418}
[13/06/2025 02:32:30] INFO: 📝 Sistema de logs inicializado
[13/06/2025 02:32:30] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 02:32:31] INFO: 💾 Banco de dados inicializado
[13/06/2025 02:32:31] INFO: 📋 28 comandos carregados
[13/06/2025 02:32:31] INFO: 🎯 Eventos carregados
[13/06/2025 02:32:31] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 02:32:31] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 02:32:31] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 02:32:31] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 02:32:31] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 02:32:31] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 02:32:31] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 02:32:31] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 02:32:31] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 02:32:31] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 02:32:31] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 02:32:31] INFO: 🌐 Servidor web inicializado
[13/06/2025 02:32:32] INFO: 🔄 Registrando comandos slash...
[13/06/2025 02:32:33] INFO: ✅ 28 comandos slash registrados globalmente
[13/06/2025 02:32:33] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 02:32:33] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 02:32:33] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 02:32:33] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.4347655}
[13/06/2025 02:40:11] INFO: 📝 Sistema de logs inicializado
[13/06/2025 02:40:11] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 02:40:11] INFO: 💾 Banco de dados inicializado
[13/06/2025 02:40:11] INFO: 📋 28 comandos carregados
[13/06/2025 02:40:11] INFO: 🎯 Eventos carregados
[13/06/2025 02:40:11] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 02:40:11] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 02:40:11] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 02:40:11] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 02:40:11] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 02:40:11] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 02:40:11] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 02:40:11] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 02:40:11] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 02:40:11] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 02:40:11] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 02:40:11] INFO: 🌐 Servidor web inicializado
[13/06/2025 02:40:12] INFO: 🔄 Registrando comandos slash...
[13/06/2025 02:40:13] INFO: ✅ 28 comandos slash registrados globalmente
[13/06/2025 02:40:13] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 02:40:13] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 02:40:13] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 02:40:13] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":4.2083007}
[13/06/2025 02:48:39] INFO: 📝 Sistema de logs inicializado
[13/06/2025 02:48:39] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 02:48:39] INFO: 💾 Banco de dados inicializado
[13/06/2025 02:48:39] INFO: 📋 28 comandos carregados
[13/06/2025 02:48:39] INFO: 🎯 Eventos carregados
[13/06/2025 02:48:39] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 02:48:39] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 02:48:39] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 02:48:39] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 02:48:39] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 02:48:39] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 02:48:39] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 02:48:39] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 02:48:39] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 02:48:39] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 02:48:39] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 02:48:39] INFO: 🌐 Servidor web inicializado
[13/06/2025 02:48:41] INFO: 🔄 Registrando comandos slash...
[13/06/2025 02:48:41] INFO: ✅ 28 comandos slash registrados globalmente
[13/06/2025 02:48:41] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 02:48:41] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 02:48:41] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 02:48:41] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":3.968884}
[13/06/2025 02:50:27] INFO: 📝 Sistema de logs inicializado
[13/06/2025 02:50:27] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 02:50:27] INFO: 💾 Banco de dados inicializado
[13/06/2025 02:50:27] INFO: 📋 28 comandos carregados
[13/06/2025 02:50:27] INFO: 🎯 Eventos carregados
[13/06/2025 02:50:27] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 02:50:27] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 02:50:27] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 02:50:27] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 02:50:27] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 02:50:27] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 02:50:27] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 02:50:27] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 02:50:27] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 02:50:27] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 02:50:27] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 02:50:27] INFO: 🌐 Servidor web inicializado
[13/06/2025 02:50:28] INFO: 🔄 Registrando comandos slash...
[13/06/2025 02:50:29] INFO: ✅ 28 comandos slash registrados globalmente
[13/06/2025 02:50:29] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 02:50:29] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 02:50:29] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 02:50:29] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":28,"uptime":3.9262848}
[13/06/2025 02:51:57] INFO: 📝 Sistema de logs inicializado
[13/06/2025 02:51:57] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 02:51:57] INFO: 💾 Banco de dados inicializado
[13/06/2025 02:51:57] INFO: 📋 29 comandos carregados
[13/06/2025 02:51:57] INFO: 🎯 Eventos carregados
[13/06/2025 02:51:57] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 02:51:57] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 02:51:57] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 02:51:57] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 02:51:57] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 02:51:57] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 02:51:57] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 02:51:57] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 02:51:57] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 02:51:57] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 02:51:57] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 02:51:57] INFO: 🌐 Servidor web inicializado
[13/06/2025 02:51:59] INFO: 🔄 Registrando comandos slash...
[13/06/2025 02:52:00] INFO: ✅ 29 comandos slash registrados globalmente
[13/06/2025 02:52:00] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 02:52:00] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 02:52:00] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 02:52:00] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":29,"uptime":4.494606}
[13/06/2025 02:59:15] INFO: 📝 Sistema de logs inicializado
[13/06/2025 02:59:15] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 02:59:15] INFO: 💾 Banco de dados inicializado
[13/06/2025 02:59:15] INFO: 📋 29 comandos carregados
[13/06/2025 02:59:15] INFO: 🎯 Eventos carregados
[13/06/2025 02:59:15] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 02:59:15] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 02:59:15] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 02:59:15] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 02:59:15] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 02:59:15] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 02:59:15] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 02:59:15] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 02:59:16] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 02:59:16] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 02:59:16] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 02:59:16] INFO: 🌐 Servidor web inicializado
[13/06/2025 02:59:17] INFO: 🔄 Registrando comandos slash...
[13/06/2025 02:59:18] INFO: ✅ 29 comandos slash registrados globalmente
[13/06/2025 02:59:18] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 02:59:18] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 02:59:18] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 02:59:18] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":29,"uptime":4.4282077}
[13/06/2025 03:02:21] INFO: 📝 Sistema de logs inicializado
[13/06/2025 03:02:21] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 03:02:21] INFO: 💾 Banco de dados inicializado
[13/06/2025 03:02:21] INFO: 📋 31 comandos carregados
[13/06/2025 03:02:21] INFO: 🎯 Eventos carregados
[13/06/2025 03:02:21] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 03:02:21] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 03:02:21] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 03:02:21] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 03:02:21] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 03:02:21] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 03:02:21] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 03:02:21] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 03:02:21] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 03:02:21] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 03:02:21] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 03:02:21] INFO: 🌐 Servidor web inicializado
[13/06/2025 03:02:23] INFO: 🔄 Registrando comandos slash...
[13/06/2025 03:02:24] INFO: ✅ 31 comandos slash registrados globalmente
[13/06/2025 03:02:24] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 03:02:24] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 03:02:24] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 03:02:24] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":31,"uptime":4.4829676}
[13/06/2025 03:08:06] INFO: 📝 Sistema de logs inicializado
[13/06/2025 03:08:06] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 03:08:06] INFO: 💾 Banco de dados inicializado
[13/06/2025 03:08:07] INFO: 📋 32 comandos carregados
[13/06/2025 03:08:07] INFO: 🎯 Eventos carregados
[13/06/2025 03:08:07] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 03:08:07] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 03:08:07] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 03:08:07] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 03:08:07] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 03:08:07] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 03:08:07] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 03:08:07] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 03:08:07] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 03:08:07] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 03:08:07] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 03:08:07] INFO: 🌐 Servidor web inicializado
[13/06/2025 03:08:08] INFO: 🔄 Registrando comandos slash...
[13/06/2025 03:08:09] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 03:08:09] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 03:08:09] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 03:08:09] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 03:08:09] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":4.7482185}
[13/06/2025 03:13:16] INFO: COMANDO: ticket executado | Meta: {"command":"ticket","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 03:13:41] INFO: COMANDO: play executado | Meta: {"command":"play","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 03:15:42] INFO: COMANDO: stats executado | Meta: {"command":"stats","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 03:20:48] INFO: 📝 Sistema de logs inicializado
[13/06/2025 03:20:48] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 03:20:48] INFO: 💾 Banco de dados inicializado
[13/06/2025 03:20:48] INFO: 📋 32 comandos carregados
[13/06/2025 03:20:48] INFO: 🎯 Eventos carregados
[13/06/2025 03:20:48] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 03:20:48] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 03:20:48] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 03:20:48] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 03:20:48] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 03:20:48] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 03:20:48] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 03:20:49] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 03:20:49] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 03:20:49] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 03:20:49] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 03:20:49] INFO: 🌐 Servidor web inicializado
[13/06/2025 03:20:51] INFO: 🔄 Registrando comandos slash...
[13/06/2025 03:20:52] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 03:20:52] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 03:20:52] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 03:20:52] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 03:20:52] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":5.8314337}
[13/06/2025 03:23:32] INFO: COMANDO: play executado | Meta: {"command":"play","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 03:23:55] INFO: COMANDO: play executado | Meta: {"command":"play","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 11:48:21] INFO: 📝 Sistema de logs inicializado
[13/06/2025 11:48:21] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 11:48:21] INFO: 💾 Banco de dados inicializado
[13/06/2025 11:48:21] INFO: 📋 32 comandos carregados
[13/06/2025 11:48:21] INFO: 🎯 Eventos carregados
[13/06/2025 11:48:21] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 11:48:21] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 11:48:21] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 11:48:21] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 11:48:21] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 11:48:21] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 11:48:21] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 11:48:22] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 11:48:22] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 11:48:22] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 11:48:22] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 11:48:23] INFO: 🌐 Servidor web inicializado
[13/06/2025 11:48:25] INFO: 🔄 Registrando comandos slash...
[13/06/2025 11:48:25] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 11:48:25] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 11:48:25] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 11:48:25] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 11:48:25] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":16.6251835}
[13/06/2025 11:51:08] INFO: COMANDO: play executado | Meta: {"command":"play","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 11:51:08] WARN: PERFORMANCE: Comando play levou 3069ms | Meta: {"operation":"Comando play","duration":3069,"user":"558672715243061269","guild":"1381755403326455838"}
[13/06/2025 11:53:33] INFO: COMANDO: play executado | Meta: {"command":"play","user":"1202721227999944777","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 11:53:56] INFO: COMANDO: play executado | Meta: {"command":"play","user":"1202721227999944777","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 11:53:56] WARN: PERFORMANCE: Comando play levou 5530ms | Meta: {"operation":"Comando play","duration":5530,"user":"1202721227999944777","guild":"1381755403326455838"}
[13/06/2025 11:55:36] INFO: 📝 Sistema de logs inicializado
[13/06/2025 11:55:36] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 11:55:36] INFO: 💾 Banco de dados inicializado
[13/06/2025 11:55:36] INFO: 📋 32 comandos carregados
[13/06/2025 11:55:36] INFO: 🎯 Eventos carregados
[13/06/2025 11:55:36] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 11:55:36] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 11:55:36] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 11:55:36] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 11:55:36] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 11:55:36] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 11:55:36] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 11:55:36] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 11:55:36] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 11:55:36] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 11:55:36] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 11:55:36] INFO: 🌐 Servidor web inicializado
[13/06/2025 11:55:38] INFO: 🔄 Registrando comandos slash...
[13/06/2025 11:55:38] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 11:55:38] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 11:55:38] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 11:55:38] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 11:55:38] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":4.3653911}
[13/06/2025 12:04:24] INFO: 📝 Sistema de logs inicializado
[13/06/2025 12:04:24] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 12:04:24] INFO: 💾 Banco de dados inicializado
[13/06/2025 12:04:24] INFO: 📋 33 comandos carregados
[13/06/2025 12:04:25] INFO: 🎯 Eventos carregados
[13/06/2025 12:04:25] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 12:04:25] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 12:04:25] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 12:04:25] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 12:04:25] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 12:04:25] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 12:04:25] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 12:04:25] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 12:04:25] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 12:04:25] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 12:04:25] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 12:04:25] INFO: 🌐 Servidor web inicializado
[13/06/2025 12:04:26] INFO: 🔄 Registrando comandos slash...
[13/06/2025 12:04:28] INFO: ✅ 33 comandos slash registrados globalmente
[13/06/2025 12:04:28] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 12:04:28] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 12:04:28] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 12:04:28] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":33,"uptime":6.0581215}
[13/06/2025 12:41:03] INFO: COMANDO: play executado | Meta: {"command":"play","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 12:41:03] WARN: PERFORMANCE: Comando play levou 4680ms | Meta: {"operation":"Comando play","duration":4680,"user":"558672715243061269","guild":"1381755403326455838"}
[13/06/2025 12:54:45] INFO: 📝 Sistema de logs inicializado
[13/06/2025 12:54:45] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 12:54:45] INFO: 💾 Banco de dados inicializado
[13/06/2025 12:54:45] INFO: 📋 33 comandos carregados
[13/06/2025 12:54:46] INFO: 🎯 Eventos carregados
[13/06/2025 12:54:46] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 12:54:46] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 12:54:46] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 12:54:46] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 12:54:46] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 12:54:46] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 12:54:46] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 12:54:48] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 12:54:48] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 12:54:48] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 12:54:48] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 12:54:48] INFO: 🌐 Servidor web inicializado
[13/06/2025 12:54:50] INFO: 🔄 Registrando comandos slash...
[13/06/2025 12:54:51] INFO: ✅ 33 comandos slash registrados globalmente
[13/06/2025 12:54:51] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 12:54:51] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 12:54:51] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 12:54:51] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":33,"uptime":23.3392969}
[13/06/2025 13:12:38] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:12:38] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:12:38] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:12:39] INFO: 📋 34 comandos carregados
[13/06/2025 13:12:39] INFO: 🎯 Eventos carregados
[13/06/2025 13:12:39] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:12:39] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:12:39] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:12:39] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:12:39] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:12:39] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:12:39] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:12:39] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:12:39] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:12:39] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:12:39] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:12:39] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:12:40] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:12:41] INFO: ✅ 34 comandos slash registrados globalmente
[13/06/2025 13:12:41] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:12:41] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:12:41] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:12:41] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":34,"uptime":3.9087169}
[13/06/2025 13:21:04] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:21:04] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:21:04] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:21:04] INFO: 📋 34 comandos carregados
[13/06/2025 13:21:04] INFO: 🎯 Eventos carregados
[13/06/2025 13:21:04] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:21:04] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:21:04] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:21:04] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:21:04] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:21:04] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:21:04] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:21:04] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:21:04] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:21:04] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:21:04] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:24:23] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:24:23] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:24:23] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:24:23] INFO: 📋 34 comandos carregados
[13/06/2025 13:24:23] INFO: 🎯 Eventos carregados
[13/06/2025 13:24:23] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:24:23] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:24:23] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:24:23] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:24:23] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:24:23] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:24:23] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:24:24] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:24:24] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:24:24] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:24:24] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:24:24] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:24:25] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:24:26] INFO: ✅ 34 comandos slash registrados globalmente
[13/06/2025 13:24:26] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:24:26] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:24:26] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:24:26] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":34,"uptime":5.1920191}
[13/06/2025 13:26:49] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 13:27:52] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:27:52] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:27:52] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:27:53] INFO: 📋 35 comandos carregados
[13/06/2025 13:27:53] INFO: 🎯 Eventos carregados
[13/06/2025 13:27:53] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:27:53] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:27:53] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:27:53] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:27:53] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:27:53] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:27:53] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:27:53] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:27:53] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:27:53] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:27:53] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:27:53] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:27:54] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:27:55] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 13:27:55] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:27:55] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:27:55] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:27:55] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":3.8856154}
[13/06/2025 13:31:37] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:31:37] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:31:37] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:31:37] INFO: 📋 35 comandos carregados
[13/06/2025 13:31:37] INFO: 🎯 Eventos carregados
[13/06/2025 13:31:37] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:31:37] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:31:37] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:31:37] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:31:37] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:31:37] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:31:37] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:31:37] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:31:37] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:31:37] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:31:37] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:31:37] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:31:39] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:31:39] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 13:31:39] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:31:39] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:31:39] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:31:39] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.0295128}
[13/06/2025 13:35:18] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:35:18] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:35:18] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:35:18] INFO: 📋 35 comandos carregados
[13/06/2025 13:35:18] INFO: 🎯 Eventos carregados
[13/06/2025 13:35:18] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:35:18] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:35:18] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:35:18] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:35:18] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:35:18] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:35:18] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:35:18] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:35:18] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:35:18] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:35:18] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:35:18] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:35:19] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:35:20] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 13:35:20] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:35:20] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:35:20] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:35:20] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.1745658}
[13/06/2025 13:35:57] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:35:57] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:35:57] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:35:57] INFO: 📋 35 comandos carregados
[13/06/2025 13:35:57] INFO: 🎯 Eventos carregados
[13/06/2025 13:35:57] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:35:57] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:35:57] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:35:57] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:35:57] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:35:57] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:35:57] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:35:58] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:35:58] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:35:58] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:35:58] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:35:58] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:35:59] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:35:59] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 13:35:59] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:35:59] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:35:59] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:35:59] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.0586033}
[13/06/2025 13:36:29] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 13:37:57] INFO: 🔄 Iniciando encerramento gracioso...
[13/06/2025 13:38:06] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:38:06] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:38:06] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:38:06] INFO: 📋 35 comandos carregados
[13/06/2025 13:38:06] INFO: 🎯 Eventos carregados
[13/06/2025 13:38:06] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:38:06] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:38:06] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:38:06] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:38:06] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:38:06] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:38:06] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:38:06] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:38:06] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:38:06] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:38:06] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:38:06] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:38:07] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:38:08] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 13:38:08] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:38:08] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:38:08] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:38:08] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.0050366}
[13/06/2025 13:39:54] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:39:54] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:39:54] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:39:54] INFO: 📋 35 comandos carregados
[13/06/2025 13:39:54] INFO: 🎯 Eventos carregados
[13/06/2025 13:39:54] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:39:54] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:39:54] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:39:54] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:39:54] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:39:54] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:39:54] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:39:55] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:39:55] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:39:55] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:39:55] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:39:55] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:39:56] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:39:56] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 13:39:56] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:39:56] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:39:56] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:39:56] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.0670014}
[13/06/2025 13:40:55] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 13:44:48] INFO: 🔄 Iniciando encerramento gracioso...
[13/06/2025 13:44:58] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:44:58] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:44:58] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:44:58] INFO: 📋 35 comandos carregados
[13/06/2025 13:44:58] INFO: 🎯 Eventos carregados
[13/06/2025 13:44:58] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:44:58] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:44:58] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:44:58] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:44:58] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:44:58] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:44:58] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:44:58] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:44:58] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:44:58] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:44:58] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:44:58] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:45:00] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:45:00] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 13:45:00] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:45:00] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:45:00] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:45:00] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.4230749}
[13/06/2025 13:45:35] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:45:35] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:45:35] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:45:35] INFO: 📋 35 comandos carregados
[13/06/2025 13:45:35] INFO: 🎯 Eventos carregados
[13/06/2025 13:45:35] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:45:35] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:45:35] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:45:35] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:45:35] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:45:35] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:45:35] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:45:36] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:45:36] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:45:36] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:45:36] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:45:36] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:45:37] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:45:37] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 13:45:37] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:45:37] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:45:37] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:45:37] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.0707287}
[13/06/2025 13:47:30] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 13:52:26] INFO: 🔄 Iniciando encerramento gracioso...
[13/06/2025 13:57:24] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:57:24] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:57:24] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:57:24] INFO: 📋 35 comandos carregados
[13/06/2025 13:57:24] INFO: 🎯 Eventos carregados
[13/06/2025 13:57:24] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:57:24] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:57:24] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:57:24] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:57:24] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:57:24] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:57:24] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:57:24] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:57:24] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:57:24] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:57:24] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:57:24] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:57:26] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:57:26] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 13:57:26] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:57:26] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:57:26] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:57:26] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.0977953}
[13/06/2025 13:58:08] INFO: 📝 Sistema de logs inicializado
[13/06/2025 13:58:08] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 13:58:08] INFO: 💾 Banco de dados inicializado
[13/06/2025 13:58:08] INFO: 📋 35 comandos carregados
[13/06/2025 13:58:08] INFO: 🎯 Eventos carregados
[13/06/2025 13:58:08] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 13:58:08] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 13:58:08] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 13:58:08] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 13:58:08] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 13:58:08] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 13:58:08] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 13:58:08] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 13:58:08] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 13:58:08] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 13:58:08] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 13:58:08] INFO: 🌐 Servidor web inicializado
[13/06/2025 13:58:09] INFO: 🔄 Registrando comandos slash...
[13/06/2025 13:58:10] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 13:58:10] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 13:58:10] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 13:58:10] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 13:58:10] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":3.9257137}
[13/06/2025 13:59:03] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 14:01:43] INFO: 🔄 Iniciando encerramento gracioso...
[13/06/2025 14:01:54] INFO: 📝 Sistema de logs inicializado
[13/06/2025 14:01:54] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 14:01:54] INFO: 💾 Banco de dados inicializado
[13/06/2025 14:01:54] INFO: 📋 35 comandos carregados
[13/06/2025 14:01:54] INFO: 🎯 Eventos carregados
[13/06/2025 14:01:54] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 14:01:54] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 14:01:54] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 14:01:54] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 14:01:54] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 14:01:54] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 14:01:54] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 14:01:54] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 14:01:54] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 14:01:54] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 14:01:54] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 14:01:54] INFO: 🌐 Servidor web inicializado
[13/06/2025 14:01:56] INFO: 🔄 Registrando comandos slash...
[13/06/2025 14:01:56] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 14:01:56] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 14:01:56] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 14:01:56] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 14:01:56] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.1715836}
[13/06/2025 14:02:43] INFO: 📝 Sistema de logs inicializado
[13/06/2025 14:02:43] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 14:02:43] INFO: 💾 Banco de dados inicializado
[13/06/2025 14:02:43] INFO: 📋 35 comandos carregados
[13/06/2025 14:02:43] INFO: 🎯 Eventos carregados
[13/06/2025 14:02:43] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 14:02:43] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 14:02:43] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 14:02:43] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 14:02:43] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 14:02:43] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 14:02:43] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 14:02:43] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 14:02:43] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 14:02:43] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 14:02:43] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 14:02:43] INFO: 🌐 Servidor web inicializado
[13/06/2025 14:02:44] INFO: 🔄 Registrando comandos slash...
[13/06/2025 14:02:45] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 14:02:45] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 14:02:45] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 14:02:45] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 14:02:45] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.1547441}
[13/06/2025 14:22:32] INFO: 📝 Sistema de logs inicializado
[13/06/2025 14:22:33] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 14:22:33] INFO: 💾 Banco de dados inicializado
[13/06/2025 14:22:33] INFO: 📋 35 comandos carregados
[13/06/2025 14:22:33] INFO: 🎯 Eventos carregados
[13/06/2025 14:22:33] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 14:22:33] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 14:22:33] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 14:22:33] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 14:22:33] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 14:22:33] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 14:22:33] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 14:22:33] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 14:22:33] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 14:22:33] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 14:22:33] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 14:22:33] INFO: 🌐 Servidor web inicializado
[13/06/2025 14:22:34] INFO: 🔄 Registrando comandos slash...
[13/06/2025 14:22:35] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 14:22:35] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 14:22:35] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 14:22:35] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 14:22:35] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.6340884}
[13/06/2025 14:30:24] INFO: 📝 Sistema de logs inicializado
[13/06/2025 14:30:24] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 14:30:25] INFO: 💾 Banco de dados inicializado
[13/06/2025 14:30:25] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token ':'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:77\n                        value: [\n                             ^\n\nSyntaxError: Unexpected token ':'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 14:30:25] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:90\n        )\n        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 14:30:25] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\musica\tocar.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\musica\\tocar.js:99\n                    )\n                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 14:30:25] INFO: 📋 32 comandos carregados
[13/06/2025 14:30:25] INFO: 🎯 Eventos carregados
[13/06/2025 14:30:25] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 14:30:25] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 14:30:25] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 14:30:25] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 14:30:25] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 14:30:25] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 14:30:25] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 14:30:25] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 14:30:25] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 14:30:25] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 14:30:25] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 14:30:25] INFO: 🌐 Servidor web inicializado
[13/06/2025 14:30:26] INFO: 🔄 Registrando comandos slash...
[13/06/2025 14:30:27] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 14:30:27] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 14:30:27] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 14:30:27] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 14:30:27] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":4.7564571}
[13/06/2025 14:43:29] INFO: 📝 Sistema de logs inicializado
[13/06/2025 14:43:29] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 14:43:29] INFO: 💾 Banco de dados inicializado
[13/06/2025 14:43:29] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token ':'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:77\n                        value: [\n                             ^\n\nSyntaxError: Unexpected token ':'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 14:43:29] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:90\n        )\n        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 14:43:29] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\musica\tocar.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\musica\\tocar.js:99\n                    )\n                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 14:43:29] INFO: 📋 32 comandos carregados
[13/06/2025 14:43:29] INFO: 🎯 Eventos carregados
[13/06/2025 14:43:29] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 14:43:29] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 14:43:29] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 14:43:29] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 14:43:29] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 14:43:29] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 14:43:29] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 14:43:30] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 14:43:30] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 14:43:30] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 14:43:30] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 14:43:30] INFO: 🌐 Servidor web inicializado
[13/06/2025 14:43:31] INFO: 🔄 Registrando comandos slash...
[13/06/2025 14:43:32] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 14:43:32] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 14:43:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 14:43:32] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 14:43:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":5.0390109}
[13/06/2025 14:45:51] INFO: 📝 Sistema de logs inicializado
[13/06/2025 14:45:51] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 14:45:51] INFO: 💾 Banco de dados inicializado
[13/06/2025 14:45:52] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token ':'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:77\n                        value: [\n                             ^\n\nSyntaxError: Unexpected token ':'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 14:45:52] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:90\n        )\n        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 14:45:52] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\musica\tocar.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\musica\\tocar.js:99\n                    )\n                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 14:45:52] INFO: 📋 32 comandos carregados
[13/06/2025 14:45:52] INFO: 🎯 Eventos carregados
[13/06/2025 14:45:52] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 14:45:52] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 14:45:52] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 14:45:52] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 14:45:52] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 14:45:52] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 14:45:52] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 14:45:52] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 14:45:52] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 14:45:52] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 14:45:52] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 14:45:52] INFO: 🌐 Servidor web inicializado
[13/06/2025 14:45:53] INFO: 🔄 Registrando comandos slash...
[13/06/2025 14:45:54] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 14:45:54] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 14:45:54] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 14:45:54] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 14:45:54] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":4.4173496}
[13/06/2025 15:45:54] INFO: Limpeza de logs concluída: 0 arquivos removidos
[13/06/2025 15:51:04] INFO: COMANDO: play executado | Meta: {"command":"play","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 15:55:04] INFO: 📝 Sistema de logs inicializado
[13/06/2025 15:55:04] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 15:55:05] INFO: 💾 Banco de dados inicializado
[13/06/2025 15:55:05] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token ':'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:77\n                        value: [\n                             ^\n\nSyntaxError: Unexpected token ':'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 15:55:05] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:90\n        )\n        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 15:55:05] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\musica\tocar.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\musica\\tocar.js:99\n                    )\n                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 15:55:05] INFO: 📋 32 comandos carregados
[13/06/2025 15:55:05] INFO: 🎯 Eventos carregados
[13/06/2025 15:55:05] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 15:55:05] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 15:55:05] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 15:55:05] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 15:55:05] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 15:55:05] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 15:55:05] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 15:55:05] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 15:55:05] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 15:55:05] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 15:55:05] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 15:55:05] INFO: 🌐 Servidor web inicializado
[13/06/2025 15:55:08] INFO: 🔄 Registrando comandos slash...
[13/06/2025 15:55:27] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 15:55:27] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 15:55:27] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 15:55:27] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 15:55:27] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":25.1088489}
[13/06/2025 15:56:33] INFO: 📝 Sistema de logs inicializado
[13/06/2025 15:56:33] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 15:56:34] INFO: 💾 Banco de dados inicializado
[13/06/2025 15:56:34] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token ':'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:77\n                        value: [\n                             ^\n\nSyntaxError: Unexpected token ':'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 15:56:34] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:90\n        )\n        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 15:56:34] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\musica\tocar.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\musica\\tocar.js:99\n                    )\n                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 15:56:34] INFO: 📋 32 comandos carregados
[13/06/2025 15:56:34] INFO: 🎯 Eventos carregados
[13/06/2025 15:56:34] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 15:56:34] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 15:56:34] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 15:56:34] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 15:56:34] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 15:56:34] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 15:56:34] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 15:56:34] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 15:56:34] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 15:56:34] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 15:56:34] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 15:56:34] INFO: 🌐 Servidor web inicializado
[13/06/2025 15:56:36] INFO: 🔄 Registrando comandos slash...
[13/06/2025 15:56:37] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 15:56:37] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 15:56:37] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 15:56:37] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 15:56:37] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":5.2106189}
[13/06/2025 15:58:54] INFO: 📝 Sistema de logs inicializado
[13/06/2025 15:58:54] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 15:58:54] INFO: 💾 Banco de dados inicializado
[13/06/2025 15:58:55] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token ':'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:77\n                        value: [\n                             ^\n\nSyntaxError: Unexpected token ':'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 15:58:55] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:90\n        )\n        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 15:58:55] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\musica\tocar.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\musica\\tocar.js:99\n                    )\n                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 15:58:55] INFO: 📋 32 comandos carregados
[13/06/2025 15:58:55] INFO: 🎯 Eventos carregados
[13/06/2025 15:58:55] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 15:58:55] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 15:58:55] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 15:58:55] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 15:58:55] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 15:58:55] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 15:58:55] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 15:58:55] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 15:58:55] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 15:58:55] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 15:58:55] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 15:58:55] INFO: 🌐 Servidor web inicializado
[13/06/2025 15:58:57] INFO: 🔄 Registrando comandos slash...
[13/06/2025 15:58:57] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 15:58:57] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 15:58:57] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 15:58:57] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 15:58:57] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":5.2286939}
[13/06/2025 16:01:20] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:01:20] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:01:21] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:01:21] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token ':'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:77\n                        value: [\n                             ^\n\nSyntaxError: Unexpected token ':'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 16:01:21] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:90\n        )\n        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 16:01:21] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\musica\tocar.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\musica\\tocar.js:99\n                    )\n                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 16:01:21] INFO: 📋 32 comandos carregados
[13/06/2025 16:01:21] INFO: 🎯 Eventos carregados
[13/06/2025 16:01:21] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:01:21] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:01:21] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:01:21] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:01:21] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:01:21] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:01:21] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:01:21] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:01:21] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:01:21] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:01:21] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:01:21] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:01:22] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:01:23] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 16:01:23] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:01:23] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:01:23] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:01:23] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":4.6522355}
[13/06/2025 16:02:41] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:02:41] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:02:42] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:02:42] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token ':'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:77\n                        value: [\n                             ^\n\nSyntaxError: Unexpected token ':'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 16:02:42] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:90\n        )\n        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 16:02:42] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\musica\tocar.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\musica\\tocar.js:99\n                    )\n                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 16:02:42] INFO: 📋 32 comandos carregados
[13/06/2025 16:02:42] INFO: 🎯 Eventos carregados
[13/06/2025 16:02:42] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:02:42] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:02:42] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:02:42] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:02:42] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:02:42] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:02:42] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:02:42] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:02:42] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:02:42] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:02:42] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:02:42] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:02:43] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:02:44] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 16:02:44] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:02:44] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:02:44] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:02:44] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":5.0985595}
[13/06/2025 16:06:04] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:06:04] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:06:05] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:06:05] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\config\dashboard.js: | Meta: {"error":"Unexpected token ':'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:77\n                        value: [\n                             ^\n\nSyntaxError: Unexpected token ':'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 16:06:05] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\geral\help.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\geral\\help.js:90\n        )\n        ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 16:06:05] ERROR: Erro ao carregar comando C:\Users\<USER>\Desktop\Nova pasta (4)\commands\musica\tocar.js: | Meta: {"error":"Unexpected token ')'","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\musica\\tocar.js:99\n                    )\n                    ^\n\nSyntaxError: Unexpected token ')'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadCommands (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:163:33)"}
[13/06/2025 16:06:05] INFO: 📋 32 comandos carregados
[13/06/2025 16:06:05] INFO: 🎯 Eventos carregados
[13/06/2025 16:06:05] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:06:05] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:06:05] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:06:05] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:06:05] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:06:05] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:06:05] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:06:05] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:06:05] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:06:05] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:06:05] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:06:05] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:06:07] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:06:07] INFO: ✅ 32 comandos slash registrados globalmente
[13/06/2025 16:06:07] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:06:07] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:06:07] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:06:07] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":32,"uptime":4.6484802}
[13/06/2025 16:08:16] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:08:16] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:08:17] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:08:17] INFO: 📋 35 comandos carregados
[13/06/2025 16:08:17] INFO: 🎯 Eventos carregados
[13/06/2025 16:08:17] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:08:17] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:08:17] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:08:17] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:08:17] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:08:17] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:08:17] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:08:17] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:08:17] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:08:17] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:08:17] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:08:17] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:08:18] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:08:19] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:08:19] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:08:19] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:08:19] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:08:19] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":5.1748028}
[13/06/2025 16:10:12] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:10:12] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:10:13] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:10:13] INFO: 📋 35 comandos carregados
[13/06/2025 16:10:13] INFO: 🎯 Eventos carregados
[13/06/2025 16:10:13] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:10:13] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:10:13] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:10:13] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:10:13] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:10:13] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:10:13] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:10:14] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:10:14] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:10:14] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:10:14] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:10:14] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:10:15] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:10:16] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:10:16] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:10:16] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:10:16] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:10:16] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":5.4185935}
[13/06/2025 16:15:19] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:15:19] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:15:20] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:15:20] INFO: 📋 35 comandos carregados
[13/06/2025 16:15:20] INFO: 🎯 Eventos carregados
[13/06/2025 16:15:20] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:15:20] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:15:20] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:15:20] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:15:20] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:15:20] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:15:20] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:15:20] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:15:20] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:15:20] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:15:20] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:15:20] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:15:22] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:15:22] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:15:22] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:15:22] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:15:22] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:15:22] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":5.9943382}
[13/06/2025 16:17:07] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:17:07] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:17:11] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:17:11] INFO: 📋 35 comandos carregados
[13/06/2025 16:17:11] INFO: 🎯 Eventos carregados
[13/06/2025 16:17:11] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:17:11] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:17:11] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:17:11] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:17:11] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:17:11] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:17:11] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:17:11] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:17:11] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:17:11] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:17:11] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:17:11] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:17:13] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:17:13] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:17:13] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:17:13] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:17:13] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:17:13] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":10.5846392}
[13/06/2025 16:23:52] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:23:52] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:23:53] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:23:53] INFO: 📋 35 comandos carregados
[13/06/2025 16:23:53] INFO: 🎯 Eventos carregados
[13/06/2025 16:23:53] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:23:53] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:23:53] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:23:53] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:23:53] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:23:53] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:23:53] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:23:53] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:23:53] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:23:53] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:23:53] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:23:53] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:23:55] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:24:01] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:24:01] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:24:01] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:24:01] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:24:01] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":10.4877604}
[13/06/2025 16:26:09] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:26:09] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:26:10] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:26:10] INFO: 📋 35 comandos carregados
[13/06/2025 16:26:10] INFO: 🎯 Eventos carregados
[13/06/2025 16:26:10] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:26:10] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:26:10] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:26:10] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:26:10] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:26:10] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:26:10] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:26:10] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:26:10] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:26:10] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:26:10] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:26:10] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:26:12] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:26:12] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:26:12] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:26:12] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:26:12] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:26:12] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":5.176114}
[13/06/2025 16:29:49] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:29:49] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:29:50] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:29:50] INFO: 📋 35 comandos carregados
[13/06/2025 16:29:50] INFO: 🎯 Eventos carregados
[13/06/2025 16:29:50] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:29:50] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:29:50] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:29:50] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:29:50] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:29:50] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:29:50] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:29:50] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:29:50] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:29:50] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:29:50] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:29:50] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:29:51] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:29:52] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:29:52] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:29:52] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:29:52] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:29:52] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.8635306}
[13/06/2025 16:31:47] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:31:47] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:31:48] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:31:48] INFO: 📋 35 comandos carregados
[13/06/2025 16:31:48] INFO: 🎯 Eventos carregados
[13/06/2025 16:31:48] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:31:48] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:31:48] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:31:48] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:31:48] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:31:48] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:31:48] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:31:48] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:31:48] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:31:48] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:31:48] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:31:48] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:31:50] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:31:50] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:31:50] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:31:50] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:31:50] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:31:50] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.9502037}
[13/06/2025 16:36:32] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:36:32] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:36:33] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:36:33] INFO: 📋 35 comandos carregados
[13/06/2025 16:36:33] INFO: 🎯 Eventos carregados
[13/06/2025 16:36:33] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:36:33] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:36:33] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:36:33] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:36:33] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:36:33] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:36:33] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:36:33] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:36:33] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:36:33] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:36:33] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:36:33] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:36:35] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:36:35] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:36:35] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:36:35] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:36:35] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:36:35] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":5.0989379}
[13/06/2025 16:42:19] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:42:19] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:42:20] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:42:20] INFO: 📋 35 comandos carregados
[13/06/2025 16:42:20] INFO: 🎯 Eventos carregados
[13/06/2025 16:42:20] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:42:20] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:42:20] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:42:20] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:42:20] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:42:20] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:42:20] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:42:20] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:42:20] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:42:20] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:42:20] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:42:20] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:42:21] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:42:22] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:42:22] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:42:22] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:42:22] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:42:22] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":5.4388713}
[13/06/2025 16:50:34] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:50:34] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:50:35] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:50:35] INFO: 📋 35 comandos carregados
[13/06/2025 16:50:35] INFO: 🎯 Eventos carregados
[13/06/2025 16:50:35] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:50:35] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:50:35] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:50:35] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:50:35] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:50:35] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:50:35] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:50:35] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:50:35] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:50:35] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:50:35] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:50:35] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:50:37] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:50:37] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:50:37] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:50:37] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:50:37] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:50:37] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.840518}
[13/06/2025 16:52:01] INFO: 📝 Sistema de logs inicializado
[13/06/2025 16:52:01] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 16:52:02] INFO: 💾 Banco de dados inicializado
[13/06/2025 16:52:02] INFO: 📋 35 comandos carregados
[13/06/2025 16:52:02] INFO: 🎯 Eventos carregados
[13/06/2025 16:52:02] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 16:52:02] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 16:52:02] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 16:52:02] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 16:52:02] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 16:52:02] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 16:52:02] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 16:52:02] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 16:52:02] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 16:52:02] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 16:52:02] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 16:52:02] INFO: 🌐 Servidor web inicializado
[13/06/2025 16:52:03] INFO: 🔄 Registrando comandos slash...
[13/06/2025 16:52:04] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 16:52:04] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 16:52:04] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 16:52:04] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 16:52:04] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":5.0338929}
[13/06/2025 17:07:12] INFO: 📝 Sistema de logs inicializado
[13/06/2025 17:07:12] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 17:07:12] INFO: 💾 Banco de dados inicializado
[13/06/2025 17:07:12] INFO: 📋 35 comandos carregados
[13/06/2025 17:07:12] INFO: 🎯 Eventos carregados
[13/06/2025 17:07:12] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 17:07:12] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 17:07:12] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 17:07:12] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 17:07:12] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 17:07:12] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 17:07:12] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 17:07:13] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 17:07:13] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 17:07:13] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 17:07:13] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 17:07:13] INFO: 🌐 Servidor web inicializado
[13/06/2025 17:07:13] ERROR: Uncaught Exception: | Meta: {"error":"listen EADDRINUSE: address already in use :::3000","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\express\\lib\\application.js:635:24)\n    at WebServer.start (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\server.js:838:18)\n    at initializeBot (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:127:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[13/06/2025 17:07:43] INFO: 📝 Sistema de logs inicializado
[13/06/2025 17:07:43] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 17:07:43] INFO: 💾 Banco de dados inicializado
[13/06/2025 17:07:43] INFO: 📋 35 comandos carregados
[13/06/2025 17:07:43] INFO: 🎯 Eventos carregados
[13/06/2025 17:07:43] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 17:07:43] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 17:07:43] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 17:07:43] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 17:07:43] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 17:07:43] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 17:07:43] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 17:07:44] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 17:07:44] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 17:07:44] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 17:07:44] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 17:07:44] INFO: 🌐 Servidor web inicializado
[13/06/2025 17:07:45] INFO: 🔄 Registrando comandos slash...
[13/06/2025 17:07:45] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 17:07:45] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 17:07:45] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 17:07:45] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 17:07:45] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 17:07:45] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.0824105}
[13/06/2025 17:12:07] INFO: 📝 Sistema de logs inicializado
[13/06/2025 17:12:07] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 17:12:07] INFO: 💾 Banco de dados inicializado
[13/06/2025 17:12:07] INFO: 📋 35 comandos carregados
[13/06/2025 17:12:07] INFO: 🎯 Eventos carregados
[13/06/2025 17:12:07] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 17:12:07] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 17:12:07] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 17:12:07] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 17:12:07] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 17:12:07] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 17:12:07] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 17:12:07] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 17:12:07] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 17:12:07] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 17:12:07] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 17:12:07] INFO: 🌐 Servidor web inicializado
[13/06/2025 17:12:09] INFO: 🔄 Registrando comandos slash...
[13/06/2025 17:12:09] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 17:12:09] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 17:12:09] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 17:12:09] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 17:12:09] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 17:12:09] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.2077576}
[13/06/2025 17:13:48] INFO: 📝 Sistema de logs inicializado
[13/06/2025 17:13:48] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 17:13:48] INFO: 💾 Banco de dados inicializado
[13/06/2025 17:13:48] INFO: 📋 35 comandos carregados
[13/06/2025 17:13:48] INFO: 🎯 Eventos carregados
[13/06/2025 17:13:48] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 17:13:48] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 17:13:48] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 17:13:48] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 17:13:48] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 17:13:48] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 17:13:48] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 17:13:49] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 17:13:49] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 17:13:49] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 17:13:49] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 17:13:49] INFO: 🌐 Servidor web inicializado
[13/06/2025 17:13:50] INFO: 🔄 Registrando comandos slash...
[13/06/2025 17:13:51] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 17:13:51] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 17:13:51] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 17:13:51] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 17:13:51] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 17:13:51] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.1942278}
[13/06/2025 17:18:54] INFO: 📝 Sistema de logs inicializado
[13/06/2025 17:18:54] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 17:18:54] INFO: 💾 Banco de dados inicializado
[13/06/2025 17:18:54] INFO: 📋 35 comandos carregados
[13/06/2025 17:18:54] INFO: 🎯 Eventos carregados
[13/06/2025 17:18:54] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 17:18:54] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 17:18:54] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 17:18:54] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 17:18:54] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 17:18:54] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 17:18:54] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 17:18:54] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 17:18:54] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 17:18:54] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 17:18:54] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 17:18:54] INFO: 🌐 Servidor web inicializado
[13/06/2025 17:18:55] INFO: 🔄 Registrando comandos slash...
[13/06/2025 17:18:56] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 17:18:56] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 17:18:56] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 17:18:56] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 17:18:56] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 17:18:56] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.2338536}
[13/06/2025 17:20:05] INFO: 📝 Sistema de logs inicializado
[13/06/2025 17:20:05] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 17:20:05] INFO: 💾 Banco de dados inicializado
[13/06/2025 17:20:06] INFO: 📋 35 comandos carregados
[13/06/2025 17:20:06] INFO: 🎯 Eventos carregados
[13/06/2025 17:20:06] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 17:20:06] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 17:20:06] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 17:20:06] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 17:20:06] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 17:20:06] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 17:20:06] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 17:20:06] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 17:20:06] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 17:20:06] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 17:20:06] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 17:20:06] INFO: 🌐 Servidor web inicializado
[13/06/2025 17:20:07] INFO: 🔄 Registrando comandos slash...
[13/06/2025 17:20:08] INFO: ✅ 35 comandos slash registrados globalmente
[13/06/2025 17:20:08] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 17:20:08] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 17:20:08] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 17:20:08] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 17:20:08] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":35,"uptime":4.1166301}
[13/06/2025 17:21:39] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 17:24:42] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 17:28:48] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 17:29:24] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 17:29:44] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 17:35:08] INFO: 📝 Sistema de logs inicializado
[13/06/2025 17:35:08] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 17:35:08] INFO: 💾 Banco de dados inicializado
[13/06/2025 17:35:08] INFO: 📋 36 comandos carregados
[13/06/2025 17:35:08] ERROR: Erro ao carregar evento C:\Users\<USER>\Desktop\Nova pasta (4)\events\configUpdated.js: | Meta: {"error":"await is only valid in async functions and the top level bodies of modules","stack":"C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\configUpdated.js:164\n            await applySystemConfigurations(client, guild, currentCache);\n            ^^^^^\n\nSyntaxError: await is only valid in async functions and the top level bodies of modules\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at loadEvents (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\index.js:195:27)"}
[13/06/2025 17:35:08] INFO: 🎯 Eventos carregados
[13/06/2025 17:35:08] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 17:35:08] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 17:35:08] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 17:35:08] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 17:35:08] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 17:35:08] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 17:35:08] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 17:35:08] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 17:35:08] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 17:35:08] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 17:35:08] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 17:35:08] INFO: 🌐 Servidor web inicializado
[13/06/2025 17:35:10] INFO: 🔄 Registrando comandos slash...
[13/06/2025 17:35:10] INFO: ✅ 36 comandos slash registrados globalmente
[13/06/2025 17:35:10] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 17:35:10] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 17:35:10] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 17:35:10] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 17:35:10] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":36,"uptime":4.1079469}
[13/06/2025 17:35:49] INFO: 🔄 Iniciando encerramento gracioso...
[13/06/2025 17:38:55] INFO: 📝 Sistema de logs inicializado
[13/06/2025 17:38:55] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 17:38:55] INFO: 💾 Banco de dados inicializado
[13/06/2025 17:38:55] INFO: 📋 36 comandos carregados
[13/06/2025 17:38:55] INFO: 🎯 Eventos carregados
[13/06/2025 17:38:55] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 17:38:55] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 17:38:55] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 17:38:55] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 17:38:55] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 17:38:55] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 17:38:55] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 17:38:55] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 17:38:55] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 17:38:55] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 17:38:55] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 17:38:55] INFO: 🌐 Servidor web inicializado
[13/06/2025 17:38:57] INFO: 🔄 Registrando comandos slash...
[13/06/2025 17:38:57] INFO: ✅ 36 comandos slash registrados globalmente
[13/06/2025 17:38:57] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 17:38:57] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 17:38:57] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 17:38:57] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 17:38:57] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":36,"uptime":4.4672143}
[13/06/2025 17:40:13] INFO: 📝 Sistema de logs inicializado
[13/06/2025 17:40:13] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 17:40:14] INFO: 💾 Banco de dados inicializado
[13/06/2025 17:40:14] INFO: 📋 36 comandos carregados
[13/06/2025 17:40:14] INFO: 🎯 Eventos carregados
[13/06/2025 17:40:14] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 17:40:14] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 17:40:14] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 17:40:14] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 17:40:14] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 17:40:14] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 17:40:14] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 17:40:14] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 17:40:14] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 17:40:14] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 17:40:14] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 17:40:14] INFO: 🌐 Servidor web inicializado
[13/06/2025 17:40:15] INFO: 🔄 Registrando comandos slash...
[13/06/2025 17:40:16] INFO: ✅ 36 comandos slash registrados globalmente
[13/06/2025 17:40:16] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 17:40:16] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 17:40:16] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 17:40:16] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 17:40:16] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":36,"uptime":3.7900421}
[13/06/2025 17:44:07] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 17:44:44] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 18:18:37] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 18:18:40] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 18:18:43] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 18:18:55] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 18:18:59] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 18:19:02] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 18:19:07] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 18:19:10] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 18:40:16] INFO: Limpeza de logs concluída: 0 arquivos removidos
[13/06/2025 19:39:39] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 19:39:41] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 19:39:43] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 19:39:44] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 19:39:49] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 19:40:16] INFO: Limpeza de logs concluída: 0 arquivos removidos
[13/06/2025 19:40:56] WARN: Comando não encontrado: executive | Meta: {"user":"558672715243061269","guild":"1381755403326455838"}
[13/06/2025 19:41:45] INFO: 📝 Sistema de logs inicializado
[13/06/2025 19:41:45] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 19:41:45] INFO: 💾 Banco de dados inicializado
[13/06/2025 19:41:45] INFO: 📋 37 comandos carregados
[13/06/2025 19:41:45] INFO: 🎯 Eventos carregados
[13/06/2025 19:41:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 19:41:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 19:41:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 19:41:45] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 19:41:45] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 19:41:45] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 19:41:45] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 19:41:45] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 19:41:45] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 19:41:45] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 19:41:45] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 19:41:45] INFO: 🌐 Servidor web inicializado
[13/06/2025 19:41:47] INFO: 🔄 Registrando comandos slash...
[13/06/2025 19:41:48] INFO: ✅ 37 comandos slash registrados globalmente
[13/06/2025 19:41:48] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 19:41:48] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 19:41:48] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 19:41:48] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 19:41:48] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":37,"uptime":5.0460423}
[13/06/2025 19:42:40] INFO: 📝 Sistema de logs inicializado
[13/06/2025 19:42:40] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 19:42:40] INFO: 💾 Banco de dados inicializado
[13/06/2025 19:42:40] INFO: 📋 37 comandos carregados
[13/06/2025 19:42:40] INFO: 🎯 Eventos carregados
[13/06/2025 19:42:40] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 19:42:40] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 19:42:40] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 19:42:40] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 19:42:40] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 19:42:40] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 19:42:40] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 19:42:40] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 19:42:40] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 19:42:40] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 19:42:40] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 19:42:40] INFO: 🌐 Servidor web inicializado
[13/06/2025 19:42:42] INFO: 🔄 Registrando comandos slash...
[13/06/2025 19:42:42] INFO: ✅ 37 comandos slash registrados globalmente
[13/06/2025 19:42:42] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 19:42:42] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 19:42:42] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 19:42:42] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 19:42:42] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":37,"uptime":3.9717424}
[13/06/2025 19:43:46] INFO: 📝 Sistema de logs inicializado
[13/06/2025 19:43:46] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 19:43:46] INFO: 💾 Banco de dados inicializado
[13/06/2025 19:43:46] INFO: 📋 37 comandos carregados
[13/06/2025 19:43:46] INFO: 🎯 Eventos carregados
[13/06/2025 19:43:46] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 19:43:46] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 19:43:46] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 19:43:46] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 19:43:46] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 19:43:46] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 19:43:46] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 19:43:46] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 19:43:46] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 19:43:46] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 19:43:46] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 19:43:46] INFO: 🌐 Servidor web inicializado
[13/06/2025 19:43:47] INFO: 🔄 Registrando comandos slash...
[13/06/2025 19:43:48] INFO: ✅ 37 comandos slash registrados globalmente
[13/06/2025 19:43:48] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 19:43:48] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 19:43:48] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 19:43:48] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 19:43:48] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":37,"uptime":4.0621981}
[13/06/2025 19:44:45] INFO: 📝 Sistema de logs inicializado
[13/06/2025 19:44:45] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 19:44:45] INFO: 💾 Banco de dados inicializado
[13/06/2025 19:44:45] INFO: 📋 37 comandos carregados
[13/06/2025 19:44:45] INFO: 🎯 Eventos carregados
[13/06/2025 19:44:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 19:44:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 19:44:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 19:44:45] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 19:44:45] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 19:44:45] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 19:44:45] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 19:44:45] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 19:44:45] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 19:44:45] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 19:44:45] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 19:44:45] INFO: 🌐 Servidor web inicializado
[13/06/2025 19:44:47] INFO: 🔄 Registrando comandos slash...
[13/06/2025 19:44:47] INFO: ✅ 37 comandos slash registrados globalmente
[13/06/2025 19:44:47] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 19:44:47] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 19:44:47] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 19:44:47] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 19:44:47] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":37,"uptime":4.0664842}
[13/06/2025 19:45:56] WARN: Comando não encontrado: executive | Meta: {"user":"558672715243061269","guild":"1381755403326455838"}
[13/06/2025 19:45:56] ERROR: Erro no comando executive: | Meta: {"user":"558672715243061269","guild":"1381755403326455838","options":[],"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\executive.js:217:17)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:104:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[13/06/2025 19:45:57] ERROR: Erro ao enviar mensagem de erro: | Meta: {"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:153:17)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[13/06/2025 19:45:57] WARN: COMANDO: executive falhou | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":false,"error":"Interaction has already been acknowledged."}
[13/06/2025 19:46:02] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 19:46:02] ERROR: Erro no comando dashboard: | Meta: {"user":"558672715243061269","guild":"1381755403326455838","options":[],"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\config\\dashboard.js:127:17)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:104:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[13/06/2025 19:46:02] ERROR: Erro ao enviar mensagem de erro: | Meta: {"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:153:17)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[13/06/2025 19:46:02] WARN: COMANDO: dashboard falhou | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":false,"error":"Interaction has already been acknowledged."}
[13/06/2025 19:52:04] WARN: Comando não encontrado: executive | Meta: {"user":"558672715243061269","guild":"1381755403326455838"}
[13/06/2025 19:52:05] ERROR: Erro no comando executive: | Meta: {"user":"558672715243061269","guild":"1381755403326455838","options":[],"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\executive.js:217:17)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:104:9)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[13/06/2025 19:52:05] ERROR: Erro ao enviar mensagem de erro: | Meta: {"error":"Interaction has already been acknowledged.","stack":"DiscordAPIError[40060]: Interaction has already been acknowledged.\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async handleSlashCommand (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:153:17)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:15:13)"}
[13/06/2025 19:52:05] WARN: COMANDO: executive falhou | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":false,"error":"Interaction has already been acknowledged."}
[13/06/2025 19:52:29] INFO: 📝 Sistema de logs inicializado
[13/06/2025 19:52:29] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 19:52:29] INFO: 💾 Banco de dados inicializado
[13/06/2025 19:52:29] INFO: 📋 38 comandos carregados
[13/06/2025 19:52:30] INFO: 🎯 Eventos carregados
[13/06/2025 19:52:30] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 19:52:30] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 19:52:30] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 19:52:30] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 19:52:30] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 19:52:30] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 19:52:30] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 19:52:30] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 19:52:30] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 19:52:30] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 19:52:30] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 19:52:30] INFO: 🌐 Servidor web inicializado
[13/06/2025 19:52:31] INFO: 🔄 Registrando comandos slash...
[13/06/2025 19:52:32] INFO: ✅ 38 comandos slash registrados globalmente
[13/06/2025 19:52:32] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 19:52:32] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 19:52:32] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 19:52:32] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 19:52:32] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":38,"uptime":3.9037951}
[13/06/2025 19:56:41] INFO: 📝 Sistema de logs inicializado
[13/06/2025 19:56:41] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 19:56:41] INFO: 💾 Banco de dados inicializado
[13/06/2025 19:56:41] INFO: 📋 39 comandos carregados
[13/06/2025 19:56:41] INFO: 🎯 Eventos carregados
[13/06/2025 19:56:41] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 19:56:41] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 19:56:41] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 19:56:41] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 19:56:41] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 19:56:41] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 19:56:41] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 19:56:42] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 19:56:42] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 19:56:42] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 19:56:42] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 19:56:42] INFO: 🌐 Servidor web inicializado
[13/06/2025 19:56:43] INFO: 🔄 Registrando comandos slash...
[13/06/2025 19:56:43] INFO: ✅ 39 comandos slash registrados globalmente
[13/06/2025 19:56:43] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 19:56:43] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 19:56:43] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 19:56:43] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 19:56:43] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":39,"uptime":3.8486566}
[13/06/2025 19:57:32] INFO: COMANDO: teste-exec executado | Meta: {"command":"teste-exec","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 19:57:45] INFO: COMANDO: executive executado | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 19:58:16] ERROR: Unhandled Rejection: | Meta: {"error":"Unknown interaction","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ButtonInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async InteractionCollector.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\commands\\admin\\executive.js:147:21)"}
[13/06/2025 19:58:19] ERROR: Erro no botão executive_help: | Meta: {"user":"558672715243061269","guild":"1381755403326455838","error":"Unknown interaction","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ButtonInteraction.reply (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:194:22)\n    at async handleButton (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:301:13)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\interactionCreate.js:20:13)"}
[13/06/2025 20:07:07] INFO: 📝 Sistema de logs inicializado
[13/06/2025 20:07:07] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 20:07:07] INFO: 💾 Banco de dados inicializado
[13/06/2025 20:07:07] INFO: 📋 39 comandos carregados
[13/06/2025 20:07:07] INFO: 🎯 Eventos carregados
[13/06/2025 20:07:07] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 20:07:07] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 20:07:07] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 20:07:07] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 20:07:07] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 20:07:07] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 20:07:07] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 20:07:07] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 20:07:07] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 20:07:07] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 20:07:08] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 20:07:08] INFO: 🌐 Servidor web inicializado
[13/06/2025 20:07:09] INFO: 🔄 Registrando comandos slash...
[13/06/2025 20:07:10] INFO: ✅ 39 comandos slash registrados globalmente
[13/06/2025 20:07:10] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 20:07:10] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 20:07:10] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 20:07:10] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 20:07:10] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":39,"uptime":4.2901016}
[13/06/2025 20:08:18] INFO: COMANDO: executive executado | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 20:18:26] INFO: 📝 Sistema de logs inicializado
[13/06/2025 20:18:26] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 20:18:26] INFO: 💾 Banco de dados inicializado
[13/06/2025 20:18:26] INFO: 📋 39 comandos carregados
[13/06/2025 20:18:26] INFO: 🎯 Eventos carregados
[13/06/2025 20:18:26] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 20:18:26] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 20:18:26] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 20:18:26] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 20:18:26] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 20:18:26] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 20:18:26] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 20:18:26] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 20:18:26] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 20:18:26] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 20:18:26] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 20:18:26] INFO: 🌐 Servidor web inicializado
[13/06/2025 20:18:27] INFO: 🔄 Registrando comandos slash...
[13/06/2025 20:18:28] INFO: ✅ 39 comandos slash registrados globalmente
[13/06/2025 20:18:28] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 20:18:28] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 20:18:28] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 20:18:28] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 20:18:28] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":39,"uptime":3.8281906}
[13/06/2025 20:21:48] INFO: COMANDO: executive executado | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 20:30:50] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 20:30:54] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 20:30:57] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 20:31:02] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 20:31:08] ERROR: Erro no processamento de IA: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at processAIModeration (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:390:29)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\messageCreate.js:29:17)"}
[13/06/2025 20:37:51] INFO: 📝 Sistema de logs inicializado
[13/06/2025 20:37:51] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 20:37:51] INFO: 💾 Banco de dados inicializado
[13/06/2025 20:37:51] INFO: 📋 39 comandos carregados
[13/06/2025 20:37:51] INFO: 🎯 Eventos carregados
[13/06/2025 20:37:51] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 20:37:51] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 20:37:51] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 20:37:51] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 20:37:51] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 20:37:51] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 20:37:51] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 20:37:51] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 20:37:51] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 20:37:51] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 20:37:51] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 20:37:51] INFO: 🌐 Servidor web inicializado
[13/06/2025 20:37:52] INFO: 🔄 Registrando comandos slash...
[13/06/2025 20:37:53] INFO: ✅ 39 comandos slash registrados globalmente
[13/06/2025 20:37:53] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 20:37:53] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 20:37:53] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 20:37:53] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 20:37:53] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":39,"uptime":3.9229468}
[13/06/2025 20:39:39] INFO: COMANDO: executive executado | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 20:56:27] INFO: 📝 Sistema de logs inicializado
[13/06/2025 20:56:27] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 20:56:27] INFO: 💾 Banco de dados inicializado
[13/06/2025 20:56:27] INFO: 📋 39 comandos carregados
[13/06/2025 20:56:28] INFO: 🎯 Eventos carregados
[13/06/2025 20:56:28] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 20:56:28] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 20:56:28] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 20:56:28] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 20:56:28] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 20:56:28] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 20:56:28] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 20:56:28] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 20:56:28] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 20:56:28] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 20:56:28] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 20:56:28] INFO: 🌐 Servidor web inicializado
[13/06/2025 20:56:29] INFO: 🔄 Registrando comandos slash...
[13/06/2025 20:56:30] INFO: ✅ 39 comandos slash registrados globalmente
[13/06/2025 20:56:30] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 20:56:30] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 20:56:30] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 20:56:30] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 20:56:30] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":32,"commands":39,"uptime":4.0457709}
[13/06/2025 20:57:25] INFO: COMANDO: executive executado | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 20:58:26] INFO: COMANDO: executive executado | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 20:58:59] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 21:05:14] ERROR: Erro ao enviar mensagem de boas-vindas: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at sendWelcomeMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\guildMemberAdd.js:119:31)\n    at Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\guildMemberAdd.js:54:19)"}
[13/06/2025 21:05:14] ERROR: Erro ao aplicar auto-roles: | Meta: {"error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at applyAutoRoles (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\guildMemberAdd.js:242:31)\n    at Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\events\\guildMemberAdd.js:57:19)"}
[13/06/2025 21:09:03] INFO: 🔄 Iniciando encerramento gracioso...
[13/06/2025 21:09:15] INFO: 📝 Sistema de logs inicializado
[13/06/2025 21:09:15] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 21:09:15] INFO: 💾 Banco de dados inicializado
[13/06/2025 21:09:15] INFO: 📋 39 comandos carregados
[13/06/2025 21:09:15] INFO: 🎯 Eventos carregados
[13/06/2025 21:09:15] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 21:09:15] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 21:09:15] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 21:09:15] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 21:09:15] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 21:09:15] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 21:09:15] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 21:09:15] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 21:09:15] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 21:09:15] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 21:09:15] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 21:09:15] INFO: 🌐 Servidor web inicializado
[13/06/2025 21:09:16] INFO: 🔄 Registrando comandos slash...
[13/06/2025 21:09:17] INFO: ✅ 39 comandos slash registrados globalmente
[13/06/2025 21:09:17] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 21:09:17] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 21:09:17] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 21:09:17] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 21:09:17] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":39,"uptime":4.0580481}
[13/06/2025 21:10:50] INFO: COMANDO: executive executado | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 21:11:33] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 21:14:45] INFO: 🔄 Iniciando encerramento gracioso...
[13/06/2025 21:22:01] INFO: 📝 Sistema de logs inicializado
[13/06/2025 21:22:01] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 21:22:01] INFO: 💾 Banco de dados inicializado
[13/06/2025 21:22:01] INFO: 📋 39 comandos carregados
[13/06/2025 21:22:01] INFO: 🎯 Eventos carregados
[13/06/2025 21:22:01] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 21:22:01] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 21:22:01] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 21:22:01] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 21:22:01] INFO: 💰 Sistema de Economia inicializado
[13/06/2025 21:22:02] INFO: 🏆 Sistema de Níveis inicializado
[13/06/2025 21:22:02] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 21:22:02] INFO: 🎵 Sistema de Música inicializado
[13/06/2025 21:22:02] INFO: 🎉 Sistema de Eventos inicializado
[13/06/2025 21:22:02] INFO: 📊 Sistema de Analytics inicializado
[13/06/2025 21:22:02] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 21:22:02] INFO: 🌐 Servidor web inicializado
[13/06/2025 21:22:03] INFO: 🔄 Registrando comandos slash...
[13/06/2025 21:22:04] INFO: ✅ 39 comandos slash registrados globalmente
[13/06/2025 21:22:04] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 21:22:04] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 21:22:04] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 21:22:04] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 21:22:04] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":39,"uptime":4.8331218}
[13/06/2025 21:22:11] INFO: COMANDO: executive executado | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 22:22:04] INFO: Limpeza de logs concluída: 0 arquivos removidos
[13/06/2025 22:53:15] INFO: COMANDO: executive executado | Meta: {"command":"executive","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 22:55:07] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[13/06/2025 22:56:50] INFO: 🔄 Iniciando encerramento gracioso...
[13/06/2025 23:14:38] INFO: 📝 Sistema de logs inicializado
[13/06/2025 23:14:38] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 23:14:38] INFO: 💾 Banco de dados inicializado
[13/06/2025 23:14:39] INFO: 📋 30 comandos carregados
[13/06/2025 23:14:39] INFO: 🎯 Eventos carregados
[13/06/2025 23:14:39] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 23:14:39] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 23:14:39] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 23:14:39] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 23:14:39] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 23:14:39] INFO: 📊 Sistema de Analytics de Moderação inicializado
[13/06/2025 23:14:39] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 23:14:39] INFO: 🌐 Servidor web inicializado
[13/06/2025 23:14:41] INFO: 🔄 Registrando comandos slash...
[13/06/2025 23:14:42] INFO: ✅ 30 comandos slash registrados globalmente
[13/06/2025 23:14:42] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 23:14:42] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 23:14:42] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 23:14:42] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 23:14:42] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":30,"uptime":21.0991029}
[13/06/2025 23:15:39] INFO: 🔄 Iniciando encerramento gracioso...
[13/06/2025 23:17:01] INFO: 📝 Sistema de logs inicializado
[13/06/2025 23:17:01] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 23:17:01] INFO: 💾 Banco de dados inicializado
[13/06/2025 23:17:01] INFO: 📋 30 comandos carregados
[13/06/2025 23:17:02] INFO: 🎯 Eventos carregados
[13/06/2025 23:17:02] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 23:17:02] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 23:17:02] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 23:17:02] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 23:17:02] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 23:17:02] INFO: 📊 Sistema de Analytics de Moderação inicializado
[13/06/2025 23:17:02] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 23:17:02] INFO: 🌐 Servidor web inicializado
[13/06/2025 23:17:03] INFO: 🔄 Registrando comandos slash...
[13/06/2025 23:17:04] INFO: ✅ 30 comandos slash registrados globalmente
[13/06/2025 23:17:04] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 23:17:04] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 23:17:04] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 23:17:04] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 23:17:04] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":30,"uptime":4.0057936}
[13/06/2025 23:44:44] INFO: 📝 Sistema de logs inicializado
[13/06/2025 23:44:44] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 23:44:44] INFO: 💾 Banco de dados inicializado
[13/06/2025 23:44:44] INFO: 📋 30 comandos carregados
[13/06/2025 23:44:45] INFO: 🎯 Eventos carregados
[13/06/2025 23:44:45] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 23:44:45] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 23:44:45] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 23:44:45] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 23:44:45] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 23:44:45] INFO: 📊 Sistema de Analytics de Moderação inicializado
[13/06/2025 23:44:45] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 23:44:45] INFO: 🌐 Servidor web inicializado
[13/06/2025 23:44:45] ERROR: Unhandled Rejection: | Meta: {"error":"Cannot read properties of undefined (reading 'bind')","stack":"TypeError: Cannot read properties of undefined (reading 'bind')\n    at RealTimeIntegration.setupEventListeners (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:53:58)\n    at RealTimeIntegration.init (C:\\Users\\<USER>\\Desktop\\Nova pasta (4)\\web\\services\\real-time-integration.js:30:14)"}
[13/06/2025 23:44:47] INFO: 🔄 Registrando comandos slash...
[13/06/2025 23:44:48] INFO: ✅ 30 comandos slash registrados globalmente
[13/06/2025 23:44:48] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 23:44:48] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 23:44:48] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 23:44:48] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 23:44:48] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":30,"uptime":20.2888855}
[13/06/2025 23:45:23] INFO: 📝 Sistema de logs inicializado
[13/06/2025 23:45:23] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 23:45:23] INFO: 💾 Banco de dados inicializado
[13/06/2025 23:45:24] INFO: 📋 30 comandos carregados
[13/06/2025 23:45:24] INFO: 🎯 Eventos carregados
[13/06/2025 23:45:24] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 23:45:24] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 23:45:24] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 23:45:24] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 23:45:24] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 23:45:24] INFO: 📊 Sistema de Analytics de Moderação inicializado
[13/06/2025 23:45:24] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 23:45:24] INFO: 🌐 Servidor web inicializado
[13/06/2025 23:45:25] INFO: 🔄 Registrando comandos slash...
[13/06/2025 23:45:26] INFO: ✅ 30 comandos slash registrados globalmente
[13/06/2025 23:45:26] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 23:45:26] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 23:45:26] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 23:45:26] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 23:45:26] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":30,"uptime":3.9429921}
[13/06/2025 23:47:32] INFO: 📝 Sistema de logs inicializado
[13/06/2025 23:47:32] INFO: ⚙️ Gerenciador de configurações inicializado
[13/06/2025 23:47:32] INFO: 💾 Banco de dados inicializado
[13/06/2025 23:47:32] INFO: 📋 30 comandos carregados
[13/06/2025 23:47:32] INFO: 🎯 Eventos carregados
[13/06/2025 23:47:32] INFO: 🛡️ Sistema Anti-Raid inicializado
[13/06/2025 23:47:32] INFO: 🤖 Sistema de Auto-Moderação inicializado
[13/06/2025 23:47:32] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[13/06/2025 23:47:32] INFO: 🛡️ Sistemas de moderação inicializados
[13/06/2025 23:47:32] INFO: 🎫 Sistema de Tickets inicializado
[13/06/2025 23:47:32] INFO: 📊 Sistema de Analytics de Moderação inicializado
[13/06/2025 23:47:32] INFO: 💾 Sistema de Backup inicializado
[13/06/2025 23:47:32] INFO: 🌐 Servidor web inicializado
[13/06/2025 23:47:34] INFO: 🔄 Registrando comandos slash...
[13/06/2025 23:47:35] INFO: ✅ 30 comandos slash registrados globalmente
[13/06/2025 23:47:35] INFO: 🔍 Verificando configurações dos servidores...
[13/06/2025 23:47:35] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[13/06/2025 23:47:35] INFO: 📋 Cache de configurações carregado para 3 servidores
[13/06/2025 23:47:35] INFO: ✅ Tarefas periódicas inicializadas
[13/06/2025 23:47:35] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":33,"commands":30,"uptime":3.9824022}
[13/06/2025 23:50:14] INFO: COMANDO: dashboard executado | Meta: {"command":"dashboard","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
