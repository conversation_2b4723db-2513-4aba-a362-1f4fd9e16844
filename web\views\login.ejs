<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/custom-scrollbar.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/emoji-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="/js/svgIcons.js"></script>
    <script src="/js/emojiReplacer.js"></script>
</head>
<body class="login-page">
    <!-- Background -->
    <div class="login-background">
        <div class="login-particles"></div>
    </div>

    <!-- Login Container -->
    <div class="login-container">
        <div class="login-card">
            <!-- Logo -->
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="login-title">
                    <span class="gradient-text">Nodex</span> | Moderação
                </h1>
                <p class="login-subtitle">
                    Faça login com Discord para acessar o dashboard
                </p>
            </div>

            <!-- Login Content -->
            <div class="login-content">
                <div class="login-info">
                    <div class="info-item">
                        <i class="fas fa-robot"></i>
                        <span>Moderação com IA</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-music"></i>
                        <span>Sistema de Música</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-coins"></i>
                        <span>Economia Gamificada</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-cog"></i>
                        <span>Dashboard Completo</span>
                    </div>
                </div>

                <!-- Error Message -->
                <% if (typeof error !== 'undefined' && error) { %>
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span><%= error %></span>
                    <div class="error-help">
                        <p>Para corrigir este erro:</p>
                        <ol>
                            <li>Acesse <a href="https://discord.com/developers/applications" target="_blank">Discord Developer Portal</a></li>
                            <li>Selecione sua aplicação</li>
                            <li>Vá em OAuth2 → General</li>
                            <li>Adicione <code>http://localhost:3000/auth/discord/callback</code> em Redirects</li>
                            <li>Configure o Client Secret no arquivo .env</li>
                        </ol>
                    </div>
                </div>
                <% } %>

                <!-- Discord Login Button -->
                <div class="login-actions">
                    <a href="/auth/discord" class="discord-login-btn">
                        <i class="fab fa-discord"></i>
                        <span>Entrar com Discord</span>
                        <div class="btn-glow"></div>
                    </a>

                    <p class="login-note">
                        Você será redirecionado para o Discord para autorizar o acesso
                    </p>
                </div>

                <!-- Features -->
                <div class="login-features">
                    <h3>O que você pode fazer:</h3>
                    <ul>
                        <li><i class="fas fa-check"></i> Configurar moderação automática</li>
                        <li><i class="fas fa-check"></i> Personalizar sistemas do bot</li>
                        <li><i class="fas fa-check"></i> Ver estatísticas em tempo real</li>
                        <li><i class="fas fa-check"></i> Gerenciar múltiplos servidores</li>
                    </ul>
                </div>
            </div>

            <!-- Footer -->
            <div class="login-footer">
                <p>
                    <i class="fas fa-shield-check"></i>
                    Seguro e confiável - OAuth2 Discord
                </p>
                <div class="login-legal-links">
                    <a href="/privacy">Privacidade</a>
                    <span>•</span>
                    <a href="/terms">Termos</a>
                </div>
            </div>
        </div>

        <!-- Back to Home -->
        <div class="back-home">
            <a href="/" class="back-link">
                <i class="fas fa-arrow-left"></i>
                Voltar ao início
            </a>
        </div>
    </div>

    <style>
        .login-page {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 50%, rgba(0, 255, 127, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(0, 255, 127, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, rgba(0, 255, 127, 0.05) 0%, transparent 50%);
        }

        .login-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(2px 2px at 20px 30px, rgba(0, 255, 127, 0.3), transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(0, 255, 127, 0.2), transparent),
                radial-gradient(1px 1px at 90px 40px, rgba(0, 255, 127, 0.4), transparent);
            background-repeat: repeat;
            background-size: 100px 100px;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translate(0, 0); }
            100% { transform: translate(-100px, -100px); }
        }

        .login-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 450px;
            padding: 2rem;
        }

        .login-card {
            background: rgba(26, 26, 26, 0.95);
            border: 1px solid rgba(0, 255, 127, 0.2);
            border-radius: 20px;
            padding: 3rem 2.5rem;
            backdrop-filter: blur(20px);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(0, 255, 127, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 127, 0.5), transparent);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #00ff7f, #00cc66);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: #000;
            box-shadow: 0 10px 30px rgba(0, 255, 127, 0.3);
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .login-subtitle {
            color: #888;
            font-size: 1rem;
            margin: 0;
        }

        .login-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: #ccc;
            font-size: 0.9rem;
        }

        .info-item i {
            color: #00ff7f;
            width: 16px;
        }

        .login-actions {
            text-align: center;
            margin-bottom: 2rem;
        }

        .discord-login-btn {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            background: linear-gradient(135deg, #5865f2, #4752c4);
            color: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(88, 101, 242, 0.3);
        }

        .discord-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(88, 101, 242, 0.4);
        }

        .discord-login-btn i {
            font-size: 1.5rem;
        }

        .btn-glow {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .discord-login-btn:hover .btn-glow {
            left: 100%;
        }

        .login-note {
            color: #888;
            font-size: 0.85rem;
            margin-top: 1rem;
            margin-bottom: 0;
        }

        .login-features {
            border-top: 1px solid rgba(0, 255, 127, 0.1);
            padding-top: 1.5rem;
        }

        .login-features h3 {
            color: #fff;
            font-size: 1rem;
            margin-bottom: 1rem;
            text-align: center;
        }

        .login-features ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .login-features li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: #ccc;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .login-features li i {
            color: #00ff7f;
            width: 16px;
        }

        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .login-footer p {
            color: #888;
            font-size: 0.85rem;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .login-footer i {
            color: #00ff7f;
        }

        .login-legal-links {
            margin-top: 1rem;
            text-align: center;
        }

        .login-legal-links a {
            color: #888;
            text-decoration: none;
            font-size: 0.85rem;
            transition: color 0.3s ease;
        }

        .login-legal-links a:hover {
            color: #00ff7f;
        }

        .login-legal-links span {
            color: #555;
            margin: 0 0.75rem;
        }

        .back-home {
            text-align: center;
            margin-top: 2rem;
        }

        .back-link {
            color: #888;
            text-decoration: none;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: #00ff7f;
        }

        /* Error Message */
        .error-message {
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid rgba(255, 71, 87, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            color: #ff4757;
        }

        .error-message i {
            color: #ff4757;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .error-help {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(255, 71, 87, 0.2);
        }

        .error-help p {
            color: #ccc;
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
        }

        .error-help ol {
            color: #ccc;
            font-size: 0.85rem;
            padding-left: 1.5rem;
        }

        .error-help li {
            margin-bottom: 0.5rem;
        }

        .error-help a {
            color: #00ff7f;
            text-decoration: none;
        }

        .error-help a:hover {
            text-decoration: underline;
        }

        .error-help code {
            background: rgba(0, 0, 0, 0.3);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            color: #00ff7f;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .login-container {
                padding: 1rem;
            }

            .login-card {
                padding: 2rem 1.5rem;
            }

            .login-info {
                grid-template-columns: 1fr;
            }

            .login-title {
                font-size: 1.75rem;
            }
        }
    </style>

    <script>
        // Adicionar efeito de partículas animadas
        document.addEventListener('DOMContentLoaded', function() {
            const particles = document.querySelector('.login-particles');
            
            // Criar partículas adicionais
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.width = Math.random() * 3 + 'px';
                particle.style.height = particle.style.width;
                particle.style.background = 'rgba(0, 255, 127, ' + (Math.random() * 0.5 + 0.1) + ')';
                particle.style.borderRadius = '50%';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animation = `float ${Math.random() * 20 + 10}s infinite linear`;
                particles.appendChild(particle);
            }
        });
    </script>
</body>
</html>
