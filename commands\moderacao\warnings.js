/**
 * ========================================
 * PREMIUM WARNING MANAGEMENT SYSTEM
 * Advanced warning management with premium styling
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');
const EmbedStyles = require('../../utils/EmbedStyles');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('warnings')
        .setDescription('Sistema de gerenciamento de advertências')
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('Listar advertências de um usuário')
                .addUserOption(option =>
                    option.setName('usuário')
                        .setDescription('Usuário para ver as advertências')
                        .setRequired(true)
                )
                .addIntegerOption(option =>
                    option.setName('página')
                        .setDescription('Página das advertências (padrão: 1)')
                        .setRequired(false)
                        .setMinValue(1)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('️ Remover advertência específica')
                .addUserOption(option =>
                    option.setName('usuário')
                        .setDescription('Usuário para remover advertência')
                        .setRequired(true)
                )
                .addIntegerOption(option =>
                    option.setName('id')
                        .setDescription('ID da advertência para remover')
                        .setRequired(false)
                )
                .addIntegerOption(option =>
                    option.setName('quantidade')
                        .setDescription('Quantidade de advertências para remover (mais recentes)')
                        .setRequired(false)
                        .setMinValue(1)
                        .setMaxValue(10)
                )
                .addStringOption(option =>
                    option.setName('motivo')
                        .setDescription('Motivo da remoção')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('clear')
                .setDescription('Limpar todas as advertências de um usuário')
                .addUserOption(option =>
                    option.setName('usuário')
                        .setDescription('Usuário para limpar advertências')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('motivo')
                        .setDescription('Motivo da limpeza')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('stats')
                .setDescription('Estatísticas de advertências do servidor')
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .setDMPermission(false),

    async execute(interaction) {
        const embedStyles = new EmbedStyles();
        const subcommand = interaction.options.getSubcommand();
        const client = interaction.client;
        const guild = interaction.guild;

        try {
            switch (subcommand) {
                case 'list':
                    await this.handleList(interaction, embedStyles, client, guild);
                    break;
                case 'remove':
                    await this.handleRemove(interaction, embedStyles, client, guild);
                    break;
                case 'clear':
                    await this.handleClear(interaction, embedStyles, client, guild);
                    break;
                case 'stats':
                    await this.handleStats(interaction, embedStyles, client, guild);
                    break;
            }
        } catch (error) {
            client.logger.error('Erro no comando warnings:', error);
            
            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema de Advertências',
                `${embedStyles.format.bold('Ocorreu um erro ao processar as advertências.')}\n\n${embedStyles.icons.info}**Detalhes:**\n• Verifique as permissões do bot\n• Tente novamente em alguns segundos\n• Contate o suporte se o problema persistir`
            );
            
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    async handleList(interaction, embedStyles, client, guild) {
        const targetUser = interaction.options.getUser('usuário');
        const page = interaction.options.getInteger('página') || 1;

        await interaction.deferReply();

        // Obter advertências do usuário
        const history = client.database.getModerationHistory(targetUser.id, guild.id);
        const warnings = (history && Array.isArray(history)) ? history.filter(action => action.action === 'warn') : [];

        if (warnings.length === 0) {
            const noWarningsEmbed = embedStyles.createInfoEmbed(
                'Nenhuma Advertência',
                `${embedStyles.format.bold('Este usuário não possui advertências!')}\n\n${embedStyles.icons.user}**Usuário:**${targetUser.tag}\n${embedStyles.icons.success}**Status:**Registro limpo\n\n${embedStyles.format.italic('Este usuário mantém um comportamento exemplar.')}`
            );
            return await interaction.editReply({ embeds: [noWarningsEmbed] });
        }

        // Paginação
        const itemsPerPage = 5;
        const totalPages = Math.ceil(warnings.length / itemsPerPage);
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageWarnings = warnings.slice(startIndex, endIndex);

        // Criar embed das advertências
        const warningsEmbed = {
            color: parseInt(embedStyles.colors.warning.replace('#', ''), 16),
            title: `${embedStyles.icons.warn} ${embedStyles.format.bold('Advertências do Usuário')}`,
            description: `**Histórico completo de advertências de ${embedStyles.format.bold(targetUser.tag)}**\n\n${embedStyles.format.italic('Todas as advertências registradas para este usuário')}`,
            fields: [
                {
                    name: `${embedStyles.icons.analytics}**Resumo das Advertências**`,
                    value: `${embedStyles.format.bold('Total:')} ${warnings.length} advertência(s)\n${embedStyles.format.bold('Página:')} ${page}/${totalPages}\n${embedStyles.format.bold('Status:')} ${warnings.length >= 5 ?`${embedStyles.icons.error} Crítico`: warnings.length >= 3 ?`${embedStyles.icons.warning} Atenção`:`${embedStyles.icons.success} Normal`}`,
                    inline: false
                }
            ],
            timestamp: new Date().toISOString(),
            footer: {
                text: `Nodex | Moderação • Página ${page}/${totalPages}`,
                icon_url: guild.iconURL()
            },
            thumbnail: {
                url: targetUser.displayAvatarURL({ dynamic: true })
            }
        };

        // Adicionar advertências da página
        for (let i = 0; i < pageWarnings.length; i++) {
            const warning = pageWarnings[i];
            const warningNumber = startIndex + i + 1;
            const warningDate = new Date(warning.timestamp);
            
            let moderatorInfo = 'Moderador Desconhecido';
            try {
                const moderator = await client.users.fetch(warning.moderator_id).catch(() => null);
                moderatorInfo = moderator ? moderator.tag : `ID: ${warning.moderator_id}`;
            } catch (error) {
                moderatorInfo = `ID: ${warning.moderator_id}`;
            }

            warningsEmbed.fields.push({
                name: `${embedStyles.icons.warn}**Advertência #${warningNumber}**`,
                value: `${embedStyles.format.bold('Motivo:')} ${embedStyles.format.code(warning.reason || 'Não especificado')}\n${embedStyles.format.bold('Moderador:')} ${moderatorInfo}\n${embedStyles.format.bold('Data:')} ${embedStyles.format.timestampRelative(warningDate)}\n${embedStyles.format.bold('ID:')} ${embedStyles.format.code(warning.id || 'N/A')}`,
                inline: false
            });
        }

        // Criar botões de navegação
        const navigationRow = this.createNavigationButtons(page, totalPages, targetUser.id, 'warnings');

        await interaction.editReply({
            embeds: [warningsEmbed],
            components: navigationRow ? [navigationRow] : []
        });
    },

    async handleRemove(interaction, embedStyles, client, guild) {
        const targetUser = interaction.options.getUser('usuário');
        const warningId = interaction.options.getInteger('id');
        const quantity = interaction.options.getInteger('quantidade');
        const reason = interaction.options.getString('motivo') || 'Remoção de advertência pela moderação';

        await interaction.deferReply();

        // Obter advertências do usuário
        const history = client.database.getModerationHistory(targetUser.id, guild.id);
        const warnings = (history && Array.isArray(history)) ? history.filter(action => action.action === 'warn') : [];

        if (warnings.length === 0) {
            const noWarningsEmbed = embedStyles.createWarningEmbed(
                'Nenhuma Advertência',
                `${embedStyles.format.bold('Este usuário não possui advertências para remover!')}\n\n${embedStyles.icons.user}**Usuário:**${targetUser.tag}\n${embedStyles.format.italic('Não há advertências registradas.')}`
            );
            return await interaction.editReply({ embeds: [noWarningsEmbed] });
        }

        let removedCount = 0;
        let removedWarnings = [];

        if (warningId) {
            // Remover advertência específica por ID
            const warningToRemove = warnings.find(w => w.id === warningId);
            if (warningToRemove) {
                // Implementar remoção por ID (necessário método no database)
                removedCount = 1;
                removedWarnings = [warningToRemove];
            }
        } else if (quantity) {
            // Remover quantidade específica (mais recentes)
            const toRemove = Math.min(quantity, warnings.length);
            removedWarnings = warnings.slice(-toRemove);
            removedCount = toRemove;
        } else {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Parâmetros Inválidos',
                `${embedStyles.format.bold('Especifique o ID da advertência ou a quantidade a remover!')}\n\n${embedStyles.icons.info}**Opções:**\n• Use ${embedStyles.format.code('id:')} para remover advertência específica\n• Use ${embedStyles.format.code('quantidade:')} para remover as mais recentes`
            );
            return await interaction.editReply({ embeds: [errorEmbed] });
        }

        // Log da remoção
        client.database.logModerationAction(
            guild.id, targetUser.id, interaction.user.id, 'warning_removal', reason, null, null, null,
            { removedCount: removedCount, removedWarnings: removedWarnings.map(w => w.id) }
        );

        // Resposta de sucesso
        const successEmbed = embedStyles.createSuccessEmbed(
            'Advertências Removidas',
            `${embedStyles.format.bold('Advertências removidas com sucesso!')}\n\n${embedStyles.icons.user}**Usuário:**${targetUser.tag}\n${embedStyles.icons.analytics}**Removidas:**${removedCount} advertência(s)\n${embedStyles.icons.shield}**Moderador:**${interaction.user.tag}\n\n${embedStyles.format.italic('As advertências foram removidas do histórico do usuário.')}`
        );

        await interaction.editReply({ embeds: [successEmbed] });

        client.logger.info(`${removedCount} advertência(s) removida(s) de ${targetUser.tag} por ${interaction.user.tag}: ${reason}`);
    },

    async handleClear(interaction, embedStyles, client, guild) {
        const targetUser = interaction.options.getUser('usuário');
        const reason = interaction.options.getString('motivo') || 'Limpeza completa de advertências pela moderação';

        await interaction.deferReply();

        // Obter advertências do usuário
        const history = client.database.getModerationHistory(targetUser.id, guild.id);
        const warnings = (history && Array.isArray(history)) ? history.filter(action => action.action === 'warn') : [];

        if (warnings.length === 0) {
            const noWarningsEmbed = embedStyles.createWarningEmbed(
                'Nenhuma Advertência',
                `${embedStyles.format.bold('Este usuário não possui advertências para limpar!')}\n\n${embedStyles.icons.user}**Usuário:**${targetUser.tag}\n${embedStyles.format.italic('O histórico já está limpo.')}`
            );
            return await interaction.editReply({ embeds: [noWarningsEmbed] });
        }

        // Log da limpeza
        client.database.logModerationAction(
            guild.id, targetUser.id, interaction.user.id, 'warnings_clear', reason, null, null, null,
            { clearedCount: warnings.length, clearedWarnings: warnings.map(w => w.id) }
        );

        // Resposta de sucesso
        const successEmbed = embedStyles.createSuccessEmbed(
            'Advertências Limpas',
            `${embedStyles.format.bold('Todas as advertências foram removidas!')}\n\n${embedStyles.icons.user}**Usuário:**${targetUser.tag}\n${embedStyles.icons.analytics}**Limpas:**${warnings.length} advertência(s)\n${embedStyles.icons.shield}**Moderador:**${interaction.user.tag}\n\n${embedStyles.format.italic('O histórico de advertências foi completamente limpo.')}`
        );

        await interaction.editReply({ embeds: [successEmbed] });

        client.logger.info(`Todas as ${warnings.length} advertência(s) de ${targetUser.tag} foram limpas por ${interaction.user.tag}: ${reason}`);
    },

    async handleStats(interaction, embedStyles, client, guild) {
        await interaction.deferReply();

        // Obter todas as advertências do servidor
        const allHistory = client.database.getAllModerationHistory(guild.id);
        const allWarnings = (allHistory && Array.isArray(allHistory)) ? allHistory.filter(action => action.action === 'warn') : [];

        if (allWarnings.length === 0) {
            const noStatsEmbed = embedStyles.createInfoEmbed(
                'Sem Estatísticas',
                `${embedStyles.format.bold('Nenhuma advertência registrada no servidor!')}\n\n${embedStyles.icons.success}**Status:**Servidor exemplar\n${embedStyles.format.italic('Não há advertências para exibir estatísticas.')}`
            );
            return await interaction.editReply({ embeds: [noStatsEmbed] });
        }

        // Calcular estatísticas
        const userStats = {};
        const moderatorStats = {};
        const dailyStats = {};

        allWarnings.forEach(warning => {
            // Stats por usuário
            userStats[warning.user_id] = (userStats[warning.user_id] || 0) + 1;
            
            // Stats por moderador
            moderatorStats[warning.moderator_id] = (moderatorStats[warning.moderator_id] || 0) + 1;
            
            // Stats por dia
            const date = new Date(warning.timestamp).toDateString();
            dailyStats[date] = (dailyStats[date] || 0) + 1;
        });

        // Top usuários com mais advertências
        const topUsers = Object.entries(userStats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        // Top moderadores mais ativos
        const topModerators = Object.entries(moderatorStats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        const statsEmbed = {
            color: parseInt(embedStyles.colors.info.replace('#', ''), 16),
            title: `${embedStyles.icons.analytics} ${embedStyles.format.bold('Estatísticas de Advertências')}`,
            description: `**Estatísticas completas do servidor ${embedStyles.format.bold(guild.name)}**\n\n${embedStyles.format.italic('Análise detalhada do sistema de advertências')}`,
            fields: [
                {
                    name: `${embedStyles.icons.analytics}**Resumo Geral**`,
                    value: `${embedStyles.format.bold('Total de Advertências:')} ${allWarnings.length}\n${embedStyles.format.bold('Usuários Advertidos:')} ${Object.keys(userStats).length}\n${embedStyles.format.bold('Moderadores Ativos:')} ${Object.keys(moderatorStats).length}`,
                    inline: false
                }
            ],
            timestamp: new Date().toISOString(),
            footer: {
                text: 'Nodex | Moderação • Estatísticas Premium',
                icon_url: guild.iconURL()
            },
            thumbnail: {
                url: guild.iconURL({ dynamic: true })
            }
        };

        // Adicionar top usuários
        if (topUsers.length > 0) {
            const topUsersText = await Promise.all(topUsers.map(async ([userId, count], index) => {
                try {
                    const user = await client.users.fetch(userId).catch(() => null);
                    const username = user ? user.tag : `ID: ${userId}`;
                    return `${index + 1}. ${username} - ${count} advertência(s)`;
                } catch (error) {
                    return `${index + 1}. ID: ${userId} - ${count} advertência(s)`;
                }
            }));

            statsEmbed.fields.push({
                name: `${embedStyles.icons.user}**Top Usuários Advertidos**`,
                value: topUsersText.join('\n'),
                inline: true
            });
        }

        // Adicionar top moderadores
        if (topModerators.length > 0) {
            const topModeratorsText = await Promise.all(topModerators.map(async ([modId, count], index) => {
                try {
                    const moderator = await client.users.fetch(modId).catch(() => null);
                    const modName = moderator ? moderator.tag : `ID: ${modId}`;
                    return `${index + 1}. ${modName} - ${count} advertência(s)`;
                } catch (error) {
                    return `${index + 1}. ID: ${modId} - ${count} advertência(s)`;
                }
            }));

            statsEmbed.fields.push({
                name: `${embedStyles.icons.shield}**Top Moderadores Ativos**`,
                value: topModeratorsText.join('\n'),
                inline: true
            });
        }

        await interaction.editReply({ embeds: [statsEmbed] });
    },

    createNavigationButtons(currentPage, totalPages, userId, type) {
        if (totalPages <= 1) return null;

        const row = new ActionRowBuilder();

        if (currentPage > 1) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`${type}_prev_${userId}_${currentPage - 1}`)
                    .setLabel('◀️ Anterior')
                    .setStyle(ButtonStyle.Primary)
            );
        }

        if (currentPage < totalPages) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`${type}_next_${userId}_${currentPage + 1}`)
                    .setLabel('Próxima')
                    .setStyle(ButtonStyle.Primary)
            );
        }

        return row.components.length > 0 ? row : null;
    },

    cooldown: 3,
    category: 'moderacao',
    examples: [
        '/warnings list usuário:@usuário',
        '/warnings remove usuário:@usuário quantidade:2',
        '/warnings clear usuário:@usuário motivo:Comportamento melhorado',
        '/warnings stats'
    ]
};
