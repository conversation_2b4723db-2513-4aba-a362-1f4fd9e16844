<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurações - <%= guild.name %></title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/dashboard-advanced.css?v=2.1.3">
    <link rel="stylesheet" href="/css/immersive-effects.css">
    <link rel="stylesheet" href="/css/custom-scrollbar.css">
    <link rel="stylesheet" href="/css/dashboard-help.css">
    <link rel="stylesheet" href="/css/navbar-compact.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/emoji-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/svgIcons.js"></script>
    <script src="/js/emojiReplacer.js"></script>
    <script src="/js/immersive-effects.js"></script>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="https://cdn.discordapp.com/icons/1381755403326455838/a401a3f3c833d228490b9f4bda8e9ca3.png" alt="Nodex | Community" class="server-avatar">
                <span>Nodex | Moderação</span>
            </div>
            <div class="nav-links">
                <a href="/" class="nav-link">Início</a>
                <a href="/dashboard" class="nav-link">Dashboard</a>
                <a href="/docs" class="nav-link">Documentação</a>
                <a href="/support" class="nav-link">Suporte</a>
            </div>
            <div class="nav-actions">
                <div class="server-info">
                    <img src="<%= guild.icon ? `https://cdn.discordapp.com/icons/${guild.id}/${guild.icon}.png` : '/images/default-server.svg' %>" alt="<%= guild.name %>" class="server-avatar">
                    <span><%= guild.name %></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="dashboard-main">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar">
            <div class="sidebar-header">
                <h2><span id="sidebar-shield"></span> Moderação</h2>
                <p>Configure seu sistema de segurança</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="#geral" class="nav-item active slide-in-left hover-lift" data-section="geral">
                    <i class="fas fa-cog"></i>
                    <span>Configurações Gerais</span>
                </a>
                <a href="#moderacao" class="nav-item slide-in-left hover-lift" data-section="moderacao">
                    <i class="fas fa-shield-alt"></i>
                    <span>Moderação</span>
                </a>
                <a href="#ai" class="nav-item slide-in-left hover-lift" data-section="ai">
                    <i class="fas fa-brain"></i>
                    <span>IA & Auto-Moderação</span>
                </a>
                <a href="#antiraid" class="nav-item slide-in-left hover-lift" data-section="antiraid">
                    <i class="fas fa-shield-virus"></i>
                    <span>Anti-Raid</span>
                </a>
                <a href="#logs" class="nav-item slide-in-left hover-lift" data-section="logs">
                    <i class="fas fa-history"></i>
                    <span>Logs & Auditoria</span>
                </a>
                <a href="#comandos" class="nav-item slide-in-left hover-lift" data-section="comandos">
                    <i class="fas fa-terminal"></i>
                    <span>Comandos</span>
                </a>
                <a href="#cargos" class="nav-item slide-in-left hover-lift" data-section="cargos">
                    <i class="fas fa-users-cog"></i>
                    <span>Cargos & Permissões</span>
                </a>


                <a href="#verificacao" class="nav-item slide-in-left hover-lift" data-section="verificacao">
                    <i class="fas fa-user-check"></i>
                    <span>Verificação</span>
                </a>

                <a href="#analytics" class="nav-item slide-in-left hover-lift" data-section="analytics">
                    <i class="fas fa-chart-line"></i>
                    <span>Analytics</span>
                </a>
                <a href="#backup" class="nav-item slide-in-left hover-lift" data-section="backup">
                    <i class="fas fa-download"></i>
                    <span>Backup & Restore</span>
                </a>
            </nav>
        </aside>

        <!-- Content Area -->
        <section class="dashboard-content" data-guild-id="<%= guild.id %>">
            <div class="content-header fade-in-scroll">
                <h1 class="text-glow"><span id="main-shield"></span> Nodex | Moderação - Dashboard</h1>
                <p class="text-reveal">Configure o sistema de moderação mais avançado do Discord! Cada módulo tem seu próprio botão de salvar.</p>
                <div class="save-info scale-in">
                    <i class="fas fa-shield-alt"></i>
                    <span>Sistema focado 100% em moderação e segurança do seu servidor!</span>
                </div>
            </div>

            <!-- Configurações Gerais -->
            <div id="geral" class="config-section active">
                <div class="section-header">
                    <h2><i class="fas fa-cog"></i> Configurações Gerais</h2>
                    <p>As configurações básicas que fazem seu bot funcionar do jeito que você quer!</p>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-save-section btn-enhanced" data-section="geral" style="position: relative; overflow: hidden;">
                            <i class="fas fa-save"></i>
                            Salvar Configurações Gerais
                        </button>
                    </div>
                </div>

                <div class="config-grid">
                    <div class="config-card interactive-card fade-in-scroll hover-lift">
                        <div class="config-header">
                            <h3 class="config-title text-glow"><span id="settings-icon"></span> Prefixo do Bot</h3>
                            <p class="config-description">O símbolo que vai antes dos comandos (ex: !ban, ?kick)</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="prefix">Prefixo</label>
                                <input type="text" id="prefix" name="prefix" value="<%= config.prefix || '!' %>" maxlength="3" placeholder="!">
                                <small>Máximo 3 caracteres. Evite usar @ # ou espaços</small>
                            </div>
                        </div>
                    </div>

                    <div class="config-card interactive-card fade-in-scroll hover-lift">
                        <div class="config-header">
                            <h3 class="config-title text-glow"><span id="language-icon"></span> Idioma</h3>
                            <p class="config-description">Idioma das mensagens do bot</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="language">Idioma</label>
                                <select id="language" name="language">
                                    <option value="pt-BR" <%= config.language === 'pt-BR' ? 'selected' : '' %>>🇧🇷 Português (Brasil)</option>
                                    <option value="en-US" <%= config.language === 'en-US' ? 'selected' : '' %>>🇺🇸 English (US)</option>
                                    <option value="es-ES" <%= config.language === 'es-ES' ? 'selected' : '' %>>🇪🇸 Español</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="config-card interactive-card fade-in-scroll hover-lift">
                        <div class="config-header">
                            <h3 class="config-title text-glow"><span id="time-icon"></span> Fuso Horário</h3>
                            <p class="config-description">Para logs e eventos com horário correto</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="timezone">Fuso Horário</label>
                                <select id="timezone" name="timezone">
                                    <option value="America/Sao_Paulo" <%= config.timezone === 'America/Sao_Paulo' ? 'selected' : '' %>>🇧🇷 Brasília (UTC-3)</option>
                                    <option value="America/New_York" <%= config.timezone === 'America/New_York' ? 'selected' : '' %>>🇺🇸 Nova York (UTC-5)</option>
                                    <option value="Europe/London" <%= config.timezone === 'Europe/London' ? 'selected' : '' %>>🇬🇧 Londres (UTC+0)</option>
                                </select>
                            </div>
                        </div>
                    </div>


                </div>
            </div>

            <!-- Moderação -->
            <div id="moderacao" class="config-section">
                <div class="section-header">
                    <h2><i class="fas fa-shield-alt"></i> Sistema de Moderação</h2>
                    <p>Configure o coração da moderação! Aqui você define como o bot vai manter a ordem no seu servidor.</p>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-save-section btn-enhanced" data-section="moderacao" style="position: relative; overflow: hidden;">
                            <i class="fas fa-save"></i>
                            Salvar Configurações de Moderação
                        </button>
                    </div>
                </div>

                <div class="config-grid">
                    <div class="config-card featured">
                        <div class="config-header">
                            <h3 class="config-title">🤖 Auto-Moderação</h3>
                            <p class="config-description">Liga/desliga a moderação automática</p>
                        </div>
                        <div class="config-content">
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="auto_mod_enabled" name="auto_mod_enabled" <%= config.auto_mod_enabled ? 'checked' : '' %>>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Auto-Moderação Ativa</span>
                            </div>
                            <div class="feature-list">
                                <span class="feature-item">✅ Detecção de spam</span>
                                <span class="feature-item">✅ Links maliciosos</span>
                                <span class="feature-item">✅ Flood de mensagens</span>
                                <span class="feature-item">✅ Palavrões extremos</span>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">📝 Canal de Logs</h3>
                            <p class="config-description">Onde todas as ações de moderação serão registradas</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="mod_log_channel_id">Canal de Logs</label>
                                <select id="mod_log_channel_id" name="mod_log_channel_id">
                                    <option value="">Selecione um canal</option>
                                    <% channels.forEach(channel => { %>
                                        <option value="<%= channel.id %>" <%= config.mod_log_channel_id === channel.id ? 'selected' : '' %>>
                                            #<%= channel.name %>
                                        </option>
                                    <% }); %>
                                </select>
                                <small>Recomendamos criar um canal privado só para logs</small>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">🔇 Cargo de Timeout</h3>
                            <p class="config-description">Cargo aplicado quando alguém leva timeout</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="timeout_role_id">Cargo de Timeout</label>
                                <select id="timeout_role_id" name="timeout_role_id">
                                    <option value="">Criar automaticamente</option>
                                    <% roles.forEach(role => { %>
                                        <option value="<%= role.id %>" <%= config.timeout_role_id === role.id ? 'selected' : '' %>>
                                            <%= role.name %>
                                        </option>
                                    <% }); %>
                                </select>
                                <small>Se não selecionar, o bot criará um cargo "Timeout" automaticamente</small>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">🚫 Filtros Avançados</h3>
                            <p class="config-description">Configurações específicas de auto-moderação</p>
                        </div>
                        <div class="config-content">
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="anti_spam_enabled" name="anti_spam_enabled" <%= config.anti_spam_enabled ? 'checked' : '' %>>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Anti-Spam</span>
                            </div>
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="anti_links_enabled" name="anti_links_enabled" <%= config.anti_links_enabled ? 'checked' : '' %>>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Anti-Links</span>
                            </div>
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="anti_caps_enabled" name="anti_caps_enabled" <%= config.anti_caps_enabled ? 'checked' : '' %>>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Anti-Caps</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- IA & Auto-Moderação -->
            <div id="ai" class="config-section">
                <div class="section-header">
                    <h2><i class="fas fa-brain"></i> IA & Auto-Moderação</h2>
                    <p>O cérebro do seu bot! Inteligência artificial que detecta toxicidade, racismo e comportamentos ruins automaticamente.</p>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-save-section btn-enhanced" data-section="ai" style="position: relative; overflow: hidden;">
                            <i class="fas fa-save"></i>
                            Salvar Configurações de IA
                        </button>
                    </div>
                </div>

                <div class="config-grid">
                    <div class="config-card featured">
                        <div class="config-header">
                            <h3 class="config-title">🧠 Moderação por IA</h3>
                            <p class="config-description">Sistema inteligente que entende contexto e intenção das mensagens</p>

                        </div>
                        <div class="config-content">
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="ai_moderation_enabled" name="ai_moderation_enabled" <%= config.ai_moderation_enabled ? 'checked' : '' %>>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">IA Ativa</span>

                            </div>
                            <div class="doc-section">
                                <h4>🤖 Como funciona a IA de Moderação:</h4>
                                <p>• <strong>Analisa todas as mensagens</strong> em tempo real</p>
                                <p>• <strong>Entende contexto</strong> - não só palavras isoladas</p>
                                <p>• <strong>Detecta intenção</strong> - diferencia brincadeira de ataque real</p>
                                <p>• <strong>Pune automaticamente</strong> conteúdo inadequado</p>
                                <p>• <strong>Aprende com o servidor</strong> - fica mais precisa com o tempo</p>
                            </div>
                            <div class="doc-section info-doc">
                                <h4>✅ O que a IA detecta e pune:</h4>
                                <p>• <strong>Racismo e discriminação</strong> - tolerância zero</p>
                                <p>• <strong>Assédio e bullying</strong> - ataques pessoais</p>
                                <p>• <strong>Ameaças e violência</strong> - conteúdo perigoso</p>
                                <p>• <strong>Spam excessivo</strong> - flood de mensagens</p>
                                <p>• <strong>Links maliciosos</strong> - phishing e malware</p>
                                <p>• <strong>Conteúdo sexual inadequado</strong> - NSFW não autorizado</p>
                            </div>
                            <div class="doc-section success-doc">
                                <h4>❌ O que a IA NÃO pune:</h4>
                                <p>• <strong>Palavrões casuais</strong> - "porra", "merda", etc.</p>
                                <p>• <strong>Discussões normais</strong> - mesmo que acaloradas</p>
                                <p>• <strong>Piadas entre amigos</strong> - contexto amigável</p>
                                <p>• <strong>Críticas construtivas</strong> - feedback honesto</p>
                                <p>• <strong>Expressões regionais</strong> - gírias locais</p>
                            </div>
                            <div class="ai-features">
                                <div class="ai-feature">
                                    <i class="fas fa-fire"></i>
                                    <span>Detecta ataques pessoais</span>
                                </div>
                                <div class="ai-feature">
                                    <i class="fas fa-skull"></i>
                                    <span>Identifica racismo e discriminação</span>
                                </div>
                                <div class="ai-feature">
                                    <i class="fas fa-angry"></i>
                                    <span>Reconhece assédio e bullying</span>
                                </div>
                                <div class="ai-feature">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Ignora palavrões casuais</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">🎯 Sensibilidade da IA</h3>
                            <p class="config-description">Quão rigorosa a IA deve ser na detecção de conteúdo inadequado</p>

                        </div>
                        <div class="config-content">
                            <div class="sensitivity-slider">
                                <input type="range" id="ai_sensitivity" name="ai_sensitivity" min="1" max="5" value="<%= config.ai_sensitivity || 3 %>">
                                <div class="sensitivity-labels">
                                    <span>🟢 Suave</span>
                                    <span>🟡 Normal</span>
                                    <span>🔴 Rigorosa</span>
                                </div>
                            </div>
                            <div class="sensitivity-description">
                                <p id="sensitivityDesc">Moderação balanceada - detecta ataques claros mas ignora palavrões casuais</p>
                            </div>

                            <div class="doc-section">
                                <h4>⚖️ Níveis de Sensibilidade:</h4>
                                <p><strong>🟢 Nível 1-2 (Suave):</strong></p>
                                <p>• Só pune conteúdo <strong>muito grave</strong> (racismo, ameaças sérias)</p>
                                <p>• <strong>Ignora</strong> a maioria dos palavrões e discussões</p>
                                <p>• Ideal para <strong>servidores de amigos</strong> ou comunidades maduras</p>

                                <p><strong>🟡 Nível 3 (Normal - Recomendado):</strong></p>
                                <p>• <strong>Equilibrio perfeito</strong> entre proteção e liberdade</p>
                                <p>• Pune ataques pessoais mas permite <strong>discussões normais</strong></p>
                                <p>• Ideal para a <strong>maioria dos servidores</strong></p>

                                <p><strong>🔴 Nível 4-5 (Rigorosa):</strong></p>
                                <p>• Muito sensível - pune até <strong>linguagem levemente ofensiva</strong></p>
                                <p>• Ideal para <strong>servidores familiares</strong> ou educacionais</p>
                                <p>• <strong>Cuidado:</strong> Pode punir conversas normais</p>
                            </div>

                            <div class="doc-section warning-doc">
                                <h4>⚠️ Importante:</h4>
                                <p>Independente do nível, a IA <strong>NUNCA</strong> pune palavrões casuais como "porra", "merda", etc. Ela foca em <strong>intenção maliciosa</strong>, não em palavras específicas.</p>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">🎯 Sistema de Segunda Chance</h3>
                            <p class="config-description">Dar uma chance antes de punir severamente</p>
                        </div>
                        <div class="config-content">
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="second_chance_enabled" name="second_chance_enabled" <%= config.second_chance_enabled ? 'checked' : '' %>>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Segunda Chance Ativa</span>
                            </div>
                            <div class="second-chance-info">
                                <p>✅ Primeira infração = Advertência</p>
                                <p>⚠️ Segunda infração (24h) = Timeout</p>
                                <p>🔨 Terceira infração = Punição severa</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Anti-Raid -->
            <div id="antiraid" class="config-section">
                <div class="section-header">
                    <h2><i class="fas fa-shield-virus"></i> Sistema Anti-Raid</h2>
                    <p>Proteção militar contra raids, bots maliciosos e ataques coordenados. Seu servidor vira uma fortaleza!</p>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-save-section btn-enhanced" data-section="antiraid" style="position: relative; overflow: hidden;">
                            <i class="fas fa-save"></i>
                            Salvar Configurações Anti-Raid
                        </button>
                    </div>
                </div>

                <div class="config-grid">
                    <div class="config-card featured">
                        <div class="config-header">
                            <h3 class="config-title">🛡️ Proteção Anti-Raid</h3>
                            <p class="config-description">Sistema inteligente que detecta e bloqueia automaticamente tentativas de raid</p>

                        </div>
                        <div class="config-content">
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="anti_raid_enabled" name="anti_raid_enabled" <%= config.anti_raid_enabled ? 'checked' : '' %>>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Anti-Raid Ativo</span>

                            </div>
                            <div class="doc-section">
                                <h4>🛡️ Como funciona o Anti-Raid:</h4>
                                <p>• <strong>Monitora entradas</strong> - detecta muitas pessoas entrando rapidamente</p>
                                <p>• <strong>Analisa padrões</strong> - identifica comportamento suspeito</p>
                                <p>• <strong>Detecta bots</strong> - contas criadas recentemente em massa</p>
                                <p>• <strong>Ação automática</strong> - ativa proteções sem intervenção manual</p>
                                <p>• <strong>Recuperação inteligente</strong> - restaura o servidor após o ataque</p>
                            </div>
                            <div class="doc-section info-doc">
                                <h4>🚨 O que é considerado RAID:</h4>
                                <p>• <strong>Entradas em massa:</strong> Muitas pessoas entrando em pouco tempo</p>
                                <p>• <strong>Spam coordenado:</strong> Várias contas enviando mensagens iguais</p>
                                <p>• <strong>Bots maliciosos:</strong> Contas automatizadas com nomes similares</p>
                                <p>• <strong>Ataques de convite:</strong> Uso de bots para gerar convites em massa</p>
                                <p>• <strong>Flood de canais:</strong> Criação/spam em vários canais simultaneamente</p>
                            </div>
                            <div class="raid-features">
                                <div class="raid-feature">
                                    <i class="fas fa-users"></i>
                                    <span>Detecta entradas em massa</span>
                                </div>
                                <div class="raid-feature">
                                    <i class="fas fa-robot"></i>
                                    <span>Bloqueia bots suspeitos</span>
                                </div>
                                <div class="raid-feature">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Anti-spam inteligente</span>
                                </div>
                                <div class="raid-feature">
                                    <i class="fas fa-lock"></i>
                                    <span>Lockdown automático</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">👥 Limite de Entradas</h3>
                            <p class="config-description">Quantos usuários podem entrar por minuto</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="max_joins_per_minute">Entradas por minuto</label>
                                <input type="number" id="max_joins_per_minute" name="max_joins_per_minute" value="<%= config.max_joins_per_minute || 10 %>" min="3" max="50">
                                <small>Acima desse número, o sistema entra em alerta</small>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">📅 Idade Mínima da Conta</h3>
                            <p class="config-description">Idade mínima da conta Discord (em dias)</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="min_account_age">Idade mínima (dias)</label>
                                <input type="number" id="min_account_age" name="min_account_age" value="<%= config.min_account_age || 7 %>" min="0" max="365">
                                <small>Contas mais novas que isso serão consideradas suspeitas</small>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">🔒 Sistema de Quarentena</h3>
                            <p class="config-description">Isolar usuários suspeitos automaticamente</p>
                        </div>
                        <div class="config-content">
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="quarantine_enabled" name="quarantine_enabled" <%= config.quarantine_enabled ? 'checked' : '' %>>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Quarentena Ativa</span>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">🚨 Cargo de Quarentena</h3>
                            <p class="config-description">Cargo aplicado aos usuários suspeitos durante raids</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="quarantine_role_id">Cargo de Quarentena</label>
                                <select id="quarantine_role_id" name="quarantine_role_id">
                                    <option value="">Criar automaticamente</option>
                                    <% roles.forEach(role => { %>
                                        <option value="<%= role.id %>" <%= config.quarantine_role_id === role.id ? 'selected' : '' %>>
                                            <%= role.name %>
                                        </option>
                                    <% }); %>
                                </select>
                                <small>Cargo aplicado durante quarentena anti-raid</small>
                            </div>

                            <div class="input-group">
                                <label for="quarantine_duration">Duração da Quarentena (minutos)</label>
                                <input type="number" id="quarantine_duration" name="quarantine_duration" value="<%= config.quarantine_duration || 60 %>" min="5" max="1440">
                                <small>Tempo que o usuário fica em quarentena (máximo 24h)</small>
                            </div>

                            <div class="doc-section info-doc">
                                <h4>🔒 Como funciona a Quarentena:</h4>
                                <p>• <strong>Aplicação automática:</strong> Usuários suspeitos recebem este cargo</p>
                                <p>• <strong>Permissões limitadas:</strong> Configure o cargo para restringir acesso</p>
                                <p>• <strong>Remoção automática:</strong> Cargo removido após análise ou tempo limite</p>
                                <p>• <strong>Criação automática:</strong> Se não selecionar, o bot cria um cargo "🚨 Quarentena"</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logs & Auditoria -->
            <div id="logs" class="config-section">
                <div class="section-header">
                    <h2><i class="fas fa-history"></i> Logs & Auditoria</h2>
                    <p>Monitore tudo que acontece no seu servidor! Logs detalhados de todas as ações importantes.</p>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-save-section btn-enhanced" data-section="logs" style="position: relative; overflow: hidden;">
                            <i class="fas fa-save"></i>
                            Salvar Configurações de Logs
                        </button>
                    </div>
                </div>

                <div class="config-grid">
                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">🔧 Sistema de Logs</h3>
                            <p class="config-description">Ativar ou desativar o sistema completo de logs</p>
                        </div>
                        <div class="config-content">
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="logs_enabled" name="logs_enabled" <%= config.logs_enabled ? 'checked' : '' %>>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Sistema de Logs Ativo</span>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">📝 Canal de Logs Gerais</h3>
                            <p class="config-description">Canal PRINCIPAL onde todos os logs importantes são enviados</p>

                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="general_log_channel">Canal</label>
                                <select id="general_log_channel" name="general_log_channel">
                                    <option value="">Selecione um canal</option>
                                    <% channels.forEach(channel => { %>
                                        <option value="<%= channel.id %>" <%= config.general_log_channel === channel.id ? 'selected' : '' %>>
                                            #<%= channel.name %>
                                        </option>
                                    <% }); %>
                                </select>
                            </div>

                            <div class="doc-section">
                                <h4>📋 O que é registrado neste canal:</h4>
                                <p>• <strong>🛡️ Logs de Verificação:</strong> Quem se verificou, quando, método usado</p>
                                <p>• <strong>🔨 Ações de Moderação:</strong> Bans, kicks, warns, timeouts</p>
                                <p>• <strong>👥 Entradas e Saídas:</strong> Membros entrando/saindo do servidor</p>
                                <p>• <strong>📝 Mensagens Editadas/Deletadas:</strong> Histórico de mudanças</p>
                                <p>• <strong>🏷️ Mudanças de Cargos:</strong> Cargos dados/removidos</p>
                                <p>• <strong>⚙️ Mudanças de Configuração:</strong> Alterações no bot</p>
                                <p>• <strong>🤖 Detecções de IA:</strong> Conteúdo punido automaticamente</p>
                                <p>• <strong>🛡️ Ativações Anti-Raid:</strong> Proteções ativadas</p>
                            </div>

                            <div class="doc-section warning-doc">
                                <h4>⚠️ MUITO IMPORTANTE:</h4>
                                <p><strong>Este canal é OBRIGATÓRIO</strong> para que os logs de verificação funcionem!</p>
                                <p>Se não configurar este canal, <strong>NENHUM log será enviado</strong>, incluindo os logs de verificação.</p>
                            </div>

                            <div class="doc-section success-doc">
                                <h4>💡 Recomendações:</h4>
                                <p>• Crie um canal privado chamado <strong>#logs</strong> ou <strong>#auditoria</strong></p>
                                <p>• Configure para que <strong>apenas moderadores</strong> possam ver</p>
                                <p>• <strong>Não permita</strong> que membros normais vejam os logs</p>
                                <p>• Considere usar um canal separado se receber muitos logs</p>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">🤖 Logs de IA</h3>
                            <p class="config-description">Detecções da inteligência artificial</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="ai_log_channel">Canal</label>
                                <select id="ai_log_channel" name="ai_log_channel">
                                    <option value="">Usar canal geral</option>
                                    <% channels.forEach(channel => { %>
                                        <option value="<%= channel.id %>" <%= config.ai_log_channel === channel.id ? 'selected' : '' %>>
                                            #<%= channel.name %>
                                        </option>
                                    <% }); %>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="config-card dependency-transition">
                        <div class="config-header">
                            <h3 class="config-title">🛡️ Logs de Anti-Raid</h3>
                            <p class="config-description">Tentativas de raid e proteções ativadas</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="raid_log_channel">Canal</label>
                                <select id="raid_log_channel" name="raid_log_channel">
                                    <option value="">Usar canal geral</option>
                                    <% channels.forEach(channel => { %>
                                        <option value="<%= channel.id %>" <%= config.raid_log_channel === channel.id ? 'selected' : '' %>>
                                            #<%= channel.name %>
                                        </option>
                                    <% }); %>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Comandos -->
            <div id="comandos" class="config-section">
                <div class="section-header">
                    <h2><i class="fas fa-terminal"></i> Gerenciamento de Comandos</h2>
                    <p>Configure quais comandos estão ativos e suas permissões específicas. Você pode ligar/desligar cada comando individualmente.</p>
                    <div class="doc-section info-doc">
                        <h4>💡 Como funciona:</h4>
                        <p>• <strong>Toggle Verde:</strong> Comando ativo e funcionando</p>
                        <p>• <strong>Toggle Cinza:</strong> Comando desabilitado (não funciona)</p>
                        <p>• <strong>Mudanças são instantâneas</strong> - não precisa reiniciar o bot</p>
                        <p>• <strong>Permissões do Discord</strong> ainda se aplicam (ex: ban precisa de permissão de ban)</p>
                    </div>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-save-section btn-enhanced" data-section="comandos" style="position: relative; overflow: hidden;">
                            <i class="fas fa-save"></i>
                            Salvar Configurações de Comandos
                        </button>
                    </div>
                </div>

                <div class="config-grid">
                    <!-- Comandos de Moderação -->
                    <div class="config-card featured">
                        <div class="config-header">
                            <h3 class="config-title">🔨 Comandos de Moderação (11)</h3>
                            <p class="config-description">Comandos principais para moderação do servidor</p>
                        </div>
                        <div class="config-content">
                            <div class="command-toggles">
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_ban_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/ban</span>
                                    <span class="command-desc">Bane um usuário do servidor</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_kick_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/kick</span>
                                    <span class="command-desc">Expulsa um usuário do servidor</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_warn_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/warn</span>
                                    <span class="command-desc">Aplica uma advertência a um usuário</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_warnings_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/warnings</span>
                                    <span class="command-desc">Sistema de gerenciamento de advertências</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_timeout_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/timeout</span>
                                    <span class="command-desc">Coloca um usuário em timeout</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_mute_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/mute</span>
                                    <span class="command-desc">Aplicar timeout/mute premium</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_unmute_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/unmute</span>
                                    <span class="command-desc">Remover timeout/mute premium</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_unban_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/unban</span>
                                    <span class="command-desc">Remove o banimento de um usuário</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_clear_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/clear</span>
                                    <span class="command-desc">Remove mensagens do canal</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_mod_logs_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/mod-logs</span>
                                    <span class="command-desc">Visualizar histórico de moderação</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_comandos_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/comandos</span>
                                    <span class="command-desc">Lista todos os comandos de moderação</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comandos Administrativos -->
                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">👑 Comandos Administrativos (4)</h3>
                            <p class="config-description">Comandos para administração avançada</p>
                        </div>
                        <div class="config-content">
                            <div class="command-toggles">
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_backup_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/backup</span>
                                    <span class="command-desc">Gerenciar backups do servidor</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_deploy_local_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/deploy-local</span>
                                    <span class="command-desc">Registra comandos slash neste servidor</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_setup_panels_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/setup-panels</span>
                                    <span class="command-desc">Configura painéis de demonstração</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_stats_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/stats</span>
                                    <span class="command-desc">Ver estatísticas do servidor</span>
                                </div>

                            </div>
                        </div>
                    </div>

                    <!-- Comandos de Configuração -->
                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">⚙️ Comandos de Configuração (2)</h3>
                            <p class="config-description">Comandos para configurar o bot</p>
                        </div>
                        <div class="config-content">
                            <div class="command-toggles">
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_dashboard_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/dashboard</span>
                                    <span class="command-desc">Abre o dashboard web para configurar o bot</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_config_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/config</span>
                                    <span class="command-desc">Configurações do bot para o servidor</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comandos Gerais -->
                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">📚 Comandos Gerais (4)</h3>
                            <p class="config-description">Comandos informativos e de ajuda</p>
                        </div>
                        <div class="config-content">
                            <div class="command-toggles">
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_botinfo_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/botinfo</span>
                                    <span class="command-desc">Informações sobre o Nodex | Moderação</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_help_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/help</span>
                                    <span class="command-desc">Mostra informações de ajuda sobre o bot</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_recursos_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/recursos</span>
                                    <span class="command-desc">Mostra todos os recursos de moderação</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_stats_moderacao_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/stats-moderacao</span>
                                    <span class="command-desc">Estatísticas de moderação do servidor</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comandos de Utilidades -->
                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">🛠️ Comandos de Utilidades (5)</h3>
                            <p class="config-description">Comandos utilitários diversos</p>
                        </div>
                        <div class="config-content">
                            <div class="command-toggles">
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_avatar_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/avatar</span>
                                    <span class="command-desc">Mostra o avatar de um usuário</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_ping_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/ping</span>
                                    <span class="command-desc">Mostra a latência do bot</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_say_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/say</span>
                                    <span class="command-desc">Faz o bot enviar uma mensagem</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_serverinfo_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/serverinfo</span>
                                    <span class="command-desc">Informações detalhadas do servidor</span>
                                </div>
                                <div class="command-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="cmd_userinfo_enabled" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="command-name">/userinfo</span>
                                    <span class="command-desc">Informações detalhadas de um usuário</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">🔒 Permissões de Comandos</h3>
                            <p class="config-description">Definir quem pode usar cada comando</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="mod_role_required">Cargo Mínimo para Moderação</label>
                                <select id="mod_role_required" name="mod_role_required">
                                    <option value="">Permissões padrão do Discord</option>
                                    <% roles.forEach(role => { %>
                                        <option value="<%= role.id %>">
                                            <%= role.name %>
                                        </option>
                                    <% }); %>
                                </select>
                                <small>Cargo mínimo necessário para usar comandos de moderação</small>
                            </div>

                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="admin_only_config" name="admin_only_config" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Apenas administradores podem configurar o bot</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cargos & Permissões -->
            <div id="cargos" class="config-section">
                <div class="section-header">
                    <h2><i class="fas fa-users-cog"></i> Cargos & Permissões</h2>
                    <p>Configure cargos especiais e permissões avançadas do sistema de moderação.</p>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-save-section btn-enhanced" data-section="cargos" style="position: relative; overflow: hidden;">
                            <i class="fas fa-save"></i>
                            Salvar Configurações de Cargos
                        </button>
                    </div>
                </div>

                <div class="config-grid">
                    <div class="config-card featured">
                        <div class="config-header">
                            <h3 class="config-title">👮 Cargos de Moderação</h3>
                            <p class="config-description">Definir hierarquia de moderação</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="admin_role">Cargo de Administrador</label>
                                <select id="admin_role" name="admin_role">
                                    <option value="">Nenhum cargo específico</option>
                                    <% roles.forEach(role => { %>
                                        <option value="<%= role.id %>">
                                            <%= role.name %>
                                        </option>
                                    <% }); %>
                                </select>
                                <small>Cargo com acesso total ao bot</small>
                            </div>

                            <div class="input-group">
                                <label for="moderator_role">Cargo de Moderador</label>
                                <select id="moderator_role" name="moderator_role">
                                    <option value="">Nenhum cargo específico</option>
                                    <% roles.forEach(role => { %>
                                        <option value="<%= role.id %>">
                                            <%= role.name %>
                                        </option>
                                    <% }); %>
                                </select>
                                <small>Cargo com permissões de moderação básica</small>
                            </div>

                            <div class="input-group">
                                <label for="helper_role">Cargo de Ajudante</label>
                                <select id="helper_role" name="helper_role">
                                    <option value="">Nenhum cargo específico</option>
                                    <% roles.forEach(role => { %>
                                        <option value="<%= role.id %>">
                                            <%= role.name %>
                                        </option>
                                    <% }); %>
                                </select>
                                <small>Cargo com permissões limitadas (apenas warns e timeouts)</small>
                            </div>
                        </div>
                    </div>


                </div>
            </div>


            <!-- Verificação -->
            <div id="verificacao" class="config-section">
                <div class="section-header">
                    <h2><i class="fas fa-shield-check shield-icon"></i> Sistema de Verificação</h2>
                    <p>Configure um sistema robusto de verificação para proteger seu servidor contra bots, raids e usuários maliciosos.</p>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-save-section btn-enhanced" data-section="verificacao" style="position: relative; overflow: hidden;" onclick="console.log('🔧 CLIQUE DETECTADO NO BOTÃO!'); if(window.dashboardManager) { window.dashboardManager.saveSectionConfiguration('verificacao', this); } else { console.error('DashboardManager não encontrado!'); }">
                            <i class="fas fa-save"></i>
                            Salvar Configurações de Verificação
                        </button>
                    </div>
                </div>

                <!-- Módulo de Verificação Consolidado -->
                <div class="verification-module-container">
                    <div class="verification-main-card">
                        <!-- Header com Status e Controle Principal -->
                        <div class="verification-header">
                            <div class="verification-title-section">
                                <h3 class="verification-main-title">
                                    <i class="fas fa-shield-check"></i>
                                    Nodex | Verificação
                                </h3>
                                <p class="verification-subtitle">Sistema inteligente de verificação de membros</p>
                            </div>

                            <div class="verification-master-toggle">
                                <label class="toggle toggle-enhanced">
                                    <input type="checkbox" id="verification_enabled" name="verification_enabled" <%= config.verification_enabled ? 'checked' : '' %>>
                                    <span class="toggle-slider toggle-slider-enhanced"></span>
                                </label>
                                <div class="toggle-status">
                                    <div class="status-indicator" id="verificationIndicator">
                                        <span class="status-dot <%= config.verification_enabled ? 'active' : 'inactive' %>"></span>
                                        <span class="status-text"><%= config.verification_enabled ? 'Sistema Ativo' : 'Sistema Inativo' %></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Estatísticas em Tempo Real -->
                        <div class="verification-stats-section" id="verificationStats">
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-number" id="totalMembers">0</span>
                                        <span class="stat-label">Total de Membros</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-user-check"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-number" id="verifiedMembers">0</span>
                                        <span class="stat-label">Verificados</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-user-clock"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-number" id="pendingMembers">0</span>
                                        <span class="stat-label">Pendentes</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-number" id="successRate">0%</span>
                                        <span class="stat-label">Taxa de Sucesso</span>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Seções de Configuração com Abas -->
                        <div class="verification-config-tabs dependency-transition">
                            <div class="tab-navigation">
                                <button class="tab-btn active" data-tab="basic">
                                    <i class="fas fa-cog"></i>
                                    Configuração Básica
                                </button>
                                <button class="tab-btn" data-tab="advanced">
                                    <i class="fas fa-sliders-h"></i>
                                    Configurações Avançadas
                                </button>
                                <button class="tab-btn" data-tab="messages">
                                    <i class="fas fa-comment-alt"></i>
                                    Mensagens & Preview
                                </button>
                            </div>

                            <!-- Aba: Configuração Básica -->
                            <div class="tab-content active" id="basic-tab">
                                <div class="config-section-grid">
                                    <!-- Canal de Verificação -->
                                    <div class="config-group">
                                        <h4 class="config-group-title">
                                            <i class="fas fa-hashtag"></i>
                                            Canal de Verificação
                                        </h4>
                                        <div class="input-group">
                                            <label for="verificationChannel">Canal</label>
                                            <select id="verificationChannel" name="verification_channel_id">
                                                <option value="">Selecione um canal</option>
                                                <% channels.forEach(channel => { %>
                                                    <option value="<%= channel.id %>" <%= config.verification_channel_id === channel.id ? 'selected' : '' %>>
                                                        #<%= channel.name %>
                                                    </option>
                                                <% }); %>
                                            </select>
                                            <small>Canal onde os usuários farão a verificação</small>
                                        </div>

                                        <div class="doc-section info-doc">
                                            <h4>📍 Como funciona o Canal de Verificação:</h4>
                                            <p>• O bot <strong>cria automaticamente</strong> uma mensagem neste canal</p>
                                            <p>• A mensagem contém o <strong>botão "Verificar-me"</strong> verde</p>
                                            <p>• <strong>Novos membros</strong> clicam no botão para se verificar</p>
                                            <p>• O canal deve ser <strong>visível</strong> para membros não verificados</p>
                                            <p>• Recomendado: criar um canal específico como <strong>#verificação</strong></p>
                                        </div>

                                        <div class="doc-section warning-doc">
                                            <h4>⚠️ Configuração de Permissões:</h4>
                                            <p>Configure as permissões do canal para que:</p>
                                            <p>• <strong>@everyone:</strong> Pode ver o canal e ler mensagens</p>
                                            <p>• <strong>@everyone:</strong> NÃO pode enviar mensagens</p>
                                            <p>• <strong>Bot:</strong> Pode enviar mensagens e gerenciar mensagens</p>
                                        </div>
                                    </div>

                                    <!-- Método de Verificação -->
                                    <div class="config-group">
                                        <h4 class="config-group-title">
                                            <i class="fas fa-shield-alt"></i>
                                            Método de Verificação
                                        </h4>
                                        <div class="input-group">
                                            <label for="verificationMethod">Método de Verificação</label>
                                            <select id="verificationMethod" name="verification_method" onchange="updateVerificationPreview()">
                                                <option value="">Selecione o método</option>
                                                <option value="reaction" <%= config.verification_method === 'reaction' ? 'selected' : '' %>>🖱️ Reação Simples - Clique no botão</option>
                                                <option value="captcha_math" <%= config.verification_method === 'captcha_math' ? 'selected' : '' %>>🧮 Captcha Matemático - Resolva uma conta</option>
                                                <option value="captcha_text" <%= config.verification_method === 'captcha_text' ? 'selected' : '' %>>📝 Captcha Texto - Digite a palavra</option>
                                                <option value="captcha_emoji" <%= config.verification_method === 'captcha_emoji' ? 'selected' : '' %>>😀 Captcha Emoji - Selecione o emoji</option>
                                                <option value="manual" <%= config.verification_method === 'manual' ? 'selected' : '' %>>👤 Aprovação Manual - Moderador aprova</option>
                                                <option value="combined" <%= config.verification_method === 'combined' ? 'selected' : '' %>>🔗 Verificação Combinada - Múltiplas etapas</option>
                                            </select>
                                            <small>Escolha como os novos membros serão verificados</small>
                                        </div>

                                        <div class="doc-section">
                                            <h4>🛡️ Explicação dos Métodos:</h4>
                                            <div class="method-explanations">
                                                <p><strong>🖱️ Reação Simples:</strong> Usuário só precisa clicar no botão "Verificar-me". Mais fácil, mas menos seguro contra bots.</p>

                                                <p><strong>🧮 Captcha Matemático:</strong> Usuário resolve uma conta simples (ex: 5+3=?). Boa proteção contra bots, fácil para humanos.</p>

                                                <p><strong>📝 Captcha Texto:</strong> Usuário digita uma palavra mostrada em imagem. Proteção média, pode ser difícil para alguns.</p>

                                                <p><strong>😀 Captcha Emoji:</strong> Usuário seleciona o emoji correto entre várias opções. Divertido e eficaz.</p>

                                                <p><strong>👤 Aprovação Manual:</strong> Moderador precisa aprovar cada pessoa manualmente. Máxima segurança, mas dá mais trabalho.</p>

                                                <p><strong>🔗 Verificação Combinada:</strong> Múltiplas etapas (captcha + regras + aprovação). Máxima proteção para servidores sensíveis.</p>
                                            </div>
                                        </div>

                                        <div class="doc-section success-doc">
                                            <h4>💡 Recomendações:</h4>
                                            <p>• <strong>Servidor pequeno/amigos:</strong> Reação Simples</p>
                                            <p>• <strong>Servidor público médio:</strong> Captcha Matemático</p>
                                            <p>• <strong>Servidor grande/problemas com bots:</strong> Verificação Combinada</p>
                                            <p>• <strong>Servidor muito sensível:</strong> Aprovação Manual</p>
                                        </div>

                                        <!-- Campo hidden para armazenar tipo de captcha -->
                                        <input type="hidden" id="verificationCaptcha" name="verification_captcha_type" value="math">

                                        <!-- Configurações específicas do método -->
                                        <div id="methodSpecificConfig" class="method-config">
                                            <!-- Configurações da Aprovação Manual -->
                                            <div id="manualConfig" class="method-section" style="display: none;">
                                                <div class="info-box">
                                                    <i class="fas fa-info-circle"></i>
                                                    <span>Moderadores receberão notificação para aprovar novos membros manualmente</span>
                                                </div>
                                            </div>

                                            <!-- Configurações da Aprovação Manual -->
                                            <div id="manualConfig" class="method-section" style="display: none;">
                                                <div class="info-box">
                                                    <i class="fas fa-info-circle"></i>
                                                    <span>Moderadores receberão notificação para aprovar novos membros manualmente</span>
                                                </div>
                                            </div>

                                            <!-- Configurações da Verificação Combinada -->
                                            <div id="combinedConfig" class="method-section" style="display: none;">
                                                <div class="info-box">
                                                    <i class="fas fa-info-circle"></i>
                                                    <span>Verificação em múltiplas etapas: captcha + aceitar regras + aprovação manual</span>
                                                </div>
                                                <div class="toggle-group">
                                                    <label class="toggle">
                                                        <input type="checkbox" id="combined_captcha" name="combined_captcha" checked>
                                                        <span class="toggle-slider"></span>
                                                    </label>
                                                    <span class="toggle-label">Incluir Captcha Matemático</span>
                                                </div>
                                                <div class="toggle-group">
                                                    <label class="toggle">
                                                        <input type="checkbox" id="combined_rules" name="combined_rules" checked>
                                                        <span class="toggle-slider"></span>
                                                    </label>
                                                    <span class="toggle-label">Aceitar Regras</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Cargos de Verificação -->
                                    <div class="config-group">
                                        <h4 class="config-group-title">
                                            <i class="fas fa-user-tag"></i>
                                            Cargos de Verificação
                                        </h4>
                                        <div class="input-group">
                                            <label for="verifiedRole">Cargo de Verificado</label>
                                            <select id="verifiedRole" name="verified_role_id">
                                                <option value="">Selecione um cargo</option>
                                                <% roles.forEach(role => { %>
                                                    <option value="<%= role.id %>" <%= config.verified_role_id === role.id ? 'selected' : '' %>>
                                                        <%= role.name %>
                                                    </option>
                                                <% }); %>
                                            </select>
                                        </div>
                                        <div class="input-group">
                                            <label for="unverifiedRole">Cargo de Não Verificado (Opcional)</label>
                                            <select id="unverifiedRole" name="unverified_role_id">
                                                <option value="">Nenhum</option>
                                                <% roles.forEach(role => { %>
                                                    <option value="<%= role.id %>" <%= config.unverified_role_id === role.id ? 'selected' : '' %>>
                                                        <%= role.name %>
                                                    </option>
                                                <% }); %>
                                            </select>
                                        </div>

                                        <div class="doc-section">
                                            <h4>🏷️ Como funcionam os Cargos:</h4>
                                            <p><strong>Cargo de Verificado:</strong></p>
                                            <p>• Dado <strong>automaticamente</strong> quando a pessoa se verifica</p>
                                            <p>• Configure as <strong>permissões deste cargo</strong> para controlar o que membros verificados podem fazer</p>
                                            <p>• Exemplo: acesso a canais principais, poder falar, etc.</p>

                                            <p><strong>Cargo de Não Verificado (opcional):</strong></p>
                                            <p>• Dado para <strong>novos membros</strong> quando entram no servidor</p>
                                            <p>• <strong>Removido automaticamente</strong> quando se verificam</p>
                                            <p>• Útil para <strong>restringir acesso</strong> até a verificação</p>
                                            <p>• Configure com permissões limitadas (só ver canal de verificação)</p>
                                        </div>

                                        <div class="doc-section success-doc">
                                            <h4>💡 Configuração Recomendada:</h4>
                                            <p>1. <strong>Crie um cargo "Verificado"</strong> com permissões normais</p>
                                            <p>2. <strong>Crie um cargo "Não Verificado"</strong> com permissões limitadas</p>
                                            <p>3. <strong>Configure @everyone</strong> para não ter acesso aos canais principais</p>
                                            <p>4. <strong>Só o cargo "Verificado"</strong> pode acessar o servidor completo</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Aba: Configurações Avançadas -->
                            <div class="tab-content" id="advanced-tab">
                                <div class="config-section-grid">
                                    <!-- Configurações de Tempo -->
                                    <div class="config-group">
                                        <h4 class="config-group-title">
                                            <i class="fas fa-clock"></i>
                                            Configurações de Tempo
                                        </h4>
                                        <div class="input-group">
                                            <label for="verificationTimeout">Tempo Limite (minutos)</label>
                                            <input type="number" id="verificationTimeout" name="verification_timeout_minutes" value="<%= config.verification_timeout_minutes || 30 %>" min="5" max="1440" onchange="updateTimeoutPreview()">
                                            <small>Tempo para completar a verificação</small>
                                        </div>
                                        <div class="toggle-group">
                                            <label class="toggle">
                                                <input type="checkbox" id="verification_auto_kick" name="verification_auto_kick" <%= config.verification_auto_kick ? 'checked' : '' %> onchange="toggleAutoKick()">
                                                <span class="toggle-slider"></span>
                                            </label>
                                            <span class="toggle-label">Expulsar automaticamente após timeout</span>
                                        </div>
                                        <div id="autoKickConfig" class="sub-config" style="display: none;">
                                            <div class="input-group">
                                                <label for="verificationKickTime">Tempo para expulsão (minutos)</label>
                                                <input type="number" id="verificationKickTime" name="verification_kick_time" value="<%= config.verification_kick_time || 60 %>" min="30" max="1440">
                                                <small>Tempo após o qual usuários não verificados são expulsos</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Configurações de Notificação -->
                                    <div class="config-group">
                                        <h4 class="config-group-title">
                                            <i class="fas fa-bell"></i>
                                            Notificações
                                        </h4>
                                        <div class="toggle-group">
                                            <label class="toggle">
                                                <input type="checkbox" id="verification_dm_enabled" name="verification_dm_enabled" <%= config.verification_dm_enabled !== false ? 'checked' : '' %>>
                                                <span class="toggle-slider"></span>
                                            </label>
                                            <span class="toggle-label">Enviar DM após verificação</span>

                                        </div>

                                        <div class="toggle-group">
                                            <label class="toggle">
                                                <input type="checkbox" id="verification_log_enabled" name="verification_log_enabled" <%= config.verification_log_enabled !== false ? 'checked' : '' %>>
                                                <span class="toggle-slider"></span>
                                            </label>
                                            <span class="toggle-label">Log de verificações</span>

                                        </div>
                                        <div class="log-destination-info">
                                            <h5><i class="fas fa-map-marker-alt"></i> Onde os logs de verificação são enviados:</h5>
                                            <ul>
                                                <li><strong>Canal de destino:</strong> Configurado em "Logs > Canal de Logs Gerais" <span class="channel-indicator"><i class="fas fa-hashtag"></i>logs-gerais</span></li>
                                                <li><strong>Se não configurado:</strong> Nenhum log será enviado ❌</li>
                                            </ul>
                                            <div class="doc-section info-doc">
                                                <h4>📋 Informações registradas nos logs:</h4>
                                                <p>• <strong>Nome e ID do usuário</strong> que se verificou</p>
                                                <p>• <strong>Horário exato</strong> da verificação</p>
                                                <p>• <strong>Método usado</strong> (captcha, reação, manual, etc.)</p>
                                                <p>• <strong>Status</strong> (sucesso ✅ ou falha ❌)</p>
                                                <p>• <strong>Endereço IP</strong> (se verificação de IP estiver ativa)</p>
                                                <p>• <strong>Tentativas</strong> (quantas vezes tentou se verificar)</p>
                                            </div>
                                            <div class="doc-section warning-doc">
                                                <h4>⚠️ Importante:</h4>
                                                <p>Certifique-se de configurar o <strong>Canal de Logs Gerais</strong> na seção "Logs" para que os logs de verificação funcionem corretamente!</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Configurações de Segurança -->
                                    <div class="config-group">
                                        <h4 class="config-group-title">
                                            <i class="fas fa-shield-alt"></i>
                                            Segurança Avançada
                                        </h4>
                                        <div class="toggle-group">
                                            <label class="toggle">
                                                <input type="checkbox" id="verification_anti_bot" name="verification_anti_bot" <%= config.verification_anti_bot !== false ? 'checked' : '' %>>
                                                <span class="toggle-slider"></span>
                                            </label>
                                            <span class="toggle-label">Proteção anti-bot avançada</span>

                                        </div>


                                        <div class="toggle-group">
                                            <label class="toggle">
                                                <input type="checkbox" id="verification_ip_check" name="verification_ip_check" <%= config.verification_ip_check ? 'checked' : '' %>>
                                                <span class="toggle-slider"></span>
                                            </label>
                                            <span class="toggle-label">Verificação de IP suspeito</span>

                                        </div>


                                        <div class="input-group">
                                            <label for="verification_max_attempts">Tentativas Máximas</label>
                                            <input type="number" id="verification_max_attempts" name="verification_max_attempts" value="<%= config.verification_max_attempts || 3 %>" min="1" max="10">
                                            <small>Número máximo de tentativas de verificação</small>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <!-- Aba: Mensagens & Preview -->
                            <div class="tab-content" id="messages-tab">
                                <div class="config-section-grid">
                                    <!-- Mensagens Personalizadas -->
                                    <div class="config-group">
                                        <h4 class="config-group-title">
                                            <i class="fas fa-comment-alt"></i>
                                            Mensagens Personalizadas
                                        </h4>
                                        <div class="input-group">
                                            <label for="verificationRules">Regras de Verificação</label>
                                            <textarea id="verificationRules" name="verification_rules_text" rows="3" placeholder="Texto das regras que aparecerá na verificação..." onchange="updateRulesPreview()"><%= config.verification_rules_text || 'Leia as regras do servidor antes de se verificar.' %></textarea>
                                            <small>Texto que aparecerá na mensagem de verificação</small>
                                        </div>
                                        <div class="input-group">
                                            <label for="welcomeMessage">Mensagem de Boas-vindas</label>
                                            <textarea id="welcomeMessage" name="welcome_message" rows="2" placeholder="Mensagem enviada após verificação bem-sucedida..." onchange="updateWelcomePreview()"><%= config.welcome_message || 'Bem-vindo(a) ao servidor! Você foi verificado(a) com sucesso.' %></textarea>
                                            <small>Mensagem enviada por DM após verificação</small>
                                        </div>
                                    </div>

                                    <!-- Preview da Verificação -->
                                    <div class="config-group full-width">
                                        <h4 class="config-group-title">
                                            <i class="fas fa-eye"></i>
                                            Preview da Verificação
                                        </h4>
                                        <div id="verificationPreview" class="verification-preview-enhanced">
                                            <div class="preview-embed-enhanced">
                                                <div class="embed-header-enhanced">
                                                    <div class="embed-brand">
                                                        <i class="fas fa-shield-check"></i>
                                                        <span>Nodex | Verificação</span>
                                                    </div>
                                                    <div class="embed-status">
                                                        <span class="status-badge">Sistema Ativo</span>
                                                    </div>
                                                </div>
                                                <div class="embed-content-enhanced">
                                                    <div class="verification-info">
                                                        <h5>🛡️ Verificação de Membro</h5>
                                                        <p id="previewRules">Leia as regras do servidor antes de se verificar.</p>
                                                    </div>
                                                    <div id="previewMethod" class="preview-method-enhanced">
                                                        <div class="method-display">
                                                            <i class="fas fa-cog"></i>
                                                            <span>Selecione um método de verificação</span>
                                                        </div>
                                                    </div>
                                                    <div class="verification-details">
                                                        <div id="previewTimeout" class="detail-item">
                                                            <i class="fas fa-clock"></i>
                                                            <span>Tempo limite: 30 minutos</span>
                                                        </div>
                                                        <div class="detail-item">
                                                            <i class="fas fa-users"></i>
                                                            <span>Proteção ativa contra bots</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="embed-footer-enhanced">
                                                    <div class="footer-info">
                                                        <i class="fas fa-shield-alt"></i>
                                                        <span>Nodex | Moderação - Sistema de Verificação</span>
                                                    </div>
                                                    <div class="footer-timestamp">
                                                        <i class="fas fa-clock"></i>
                                                        <span id="previewTimestamp">Agora</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics -->
            <div id="analytics" class="config-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-line"></i> Analytics de Moderação</h2>
                    <p>Estatísticas detalhadas sobre moderação, segurança e atividade do servidor para você acompanhar tudo que acontece.</p>
                    <div class="doc-section">
                        <h4>📊 O que o Analytics faz:</h4>
                        <p>• <strong>Coleta dados</strong> sobre atividade do servidor</p>
                        <p>• <strong>Gera estatísticas</strong> de moderação e segurança</p>
                        <p>• <strong>Cria relatórios</strong> automáticos periódicos</p>
                        <p>• <strong>Monitora tendências</strong> - crescimento, problemas, etc.</p>
                        <p>• <strong>Ajuda na tomada de decisões</strong> sobre moderação</p>
                    </div>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-save-section btn-enhanced" data-section="analytics" style="position: relative; overflow: hidden;">
                            <i class="fas fa-save"></i>
                            Salvar Configurações de Analytics
                        </button>
                    </div>
                </div>

                <div class="config-grid">
                    <div class="config-card featured">
                        <div class="config-header">
                            <h3 class="config-title">📊 Sistema de Analytics</h3>
                            <p class="config-description">Coleta e análise de dados de moderação</p>
                        </div>
                        <div class="config-content">
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="analytics_enabled" name="analytics_enabled" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Analytics Ativo</span>
                            </div>

                            <div class="analytics-features">
                                <div class="analytics-feature">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Estatísticas de moderação</span>
                                </div>
                                <div class="analytics-feature">
                                    <i class="fas fa-robot"></i>
                                    <span>Detecções de IA</span>
                                </div>
                                <div class="analytics-feature">
                                    <i class="fas fa-ban"></i>
                                    <span>Tentativas de raid</span>
                                </div>
                                <div class="analytics-feature">
                                    <i class="fas fa-users"></i>
                                    <span>Atividade de usuários</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">📈 Relatórios Automáticos</h3>
                            <p class="config-description">Envio automático de relatórios</p>
                        </div>
                        <div class="config-content">
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="daily_reports" name="daily_reports">
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Relatórios Diários</span>
                            </div>

                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="weekly_reports" name="weekly_reports">
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Relatórios Semanais</span>
                            </div>

                            <div class="input-group">
                                <label for="reports_channel">Canal de Relatórios</label>
                                <select id="reports_channel" name="reports_channel">
                                    <option value="">Selecione um canal</option>
                                    <% channels.forEach(channel => { %>
                                        <option value="<%= channel.id %>">
                                            #<%= channel.name %>
                                        </option>
                                    <% }); %>
                                </select>
                                <small>Canal onde os relatórios serão enviados</small>
                            </div>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">🎯 Métricas Personalizadas</h3>
                            <p class="config-description">Configure quais dados coletar</p>
                        </div>
                        <div class="config-content">
                            <div class="metrics-toggles">
                                <div class="metric-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="track_message_count" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span>Contagem de mensagens</span>
                                </div>
                                <div class="metric-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="track_join_leave" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span>Entradas e saídas</span>
                                </div>
                                <div class="metric-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="track_voice_activity" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span>Atividade de voz</span>
                                </div>
                                <div class="metric-toggle">
                                    <label class="toggle">
                                        <input type="checkbox" name="track_moderation_actions" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span>Ações de moderação</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup & Restore -->
            <div id="backup" class="config-section">
                <div class="section-header">
                    <h2><i class="fas fa-download"></i> Backup & Restore</h2>
                    <p>Sistema de backup automático para proteger todas as configurações do seu servidor. Nunca perca suas configurações!</p>
                    <div class="doc-section">
                        <h4>💾 O que é salvo no backup:</h4>
                        <p>• <strong>Todas as configurações</strong> do dashboard</p>
                        <p>• <strong>Configurações de verificação</strong> - métodos, cargos, mensagens</p>
                        <p>• <strong>Configurações de IA</strong> - sensibilidade, punições</p>
                        <p>• <strong>Configurações anti-raid</strong> - limites, ações</p>
                        <p>• <strong>Canais de logs</strong> configurados</p>
                        <p>• <strong>Estados dos comandos</strong> - quais estão ativos</p>
                        <p>• <strong>Cargos especiais</strong> - moderação, verificação</p>
                    </div>
                    <div class="doc-section warning-doc">
                        <h4>⚠️ O que NÃO é salvo:</h4>
                        <p>• <strong>Mensagens do servidor</strong> - só as configurações</p>
                        <p>• <strong>Membros e cargos</strong> - só as configurações de cargos especiais</p>
                        <p>• <strong>Canais</strong> - só as referências aos canais configurados</p>
                    </div>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-save-section btn-enhanced" data-section="backup" style="position: relative; overflow: hidden;">
                            <i class="fas fa-save"></i>
                            Salvar Configurações de Backup
                        </button>
                    </div>
                </div>

                <div class="config-grid">
                    <div class="config-card featured">
                        <div class="config-header">
                            <h3 class="config-title">💾 Sistema de Backup</h3>
                            <p class="config-description">Backup automático de configurações</p>
                        </div>
                        <div class="config-content">
                            <div class="toggle-group">
                                <label class="toggle">
                                    <input type="checkbox" id="backup_enabled" name="backup_enabled" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Backup Automático Ativo</span>
                            </div>

                            <div class="input-group">
                                <label for="backup_frequency">Frequência de Backup</label>
                                <select id="backup_frequency" name="backup_frequency">
                                    <option value="daily">Diário</option>
                                    <option value="weekly" selected>Semanal</option>
                                    <option value="monthly">Mensal</option>
                                </select>
                                <small>Com que frequência fazer backup automático</small>
                            </div>

                            <div class="input-group">
                                <label for="backup_retention">Manter Backups</label>
                                <select id="backup_retention" name="backup_retention">
                                    <option value="7">7 dias</option>
                                    <option value="30" selected>30 dias</option>
                                    <option value="90">90 dias</option>
                                    <option value="365">1 ano</option>
                                </select>
                                <small>Por quanto tempo manter os backups</small>
                            </div>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">🔄 Backup Manual</h3>
                            <p class="config-description">Criar backup imediatamente</p>
                        </div>
                        <div class="config-content">
                            <div class="backup-actions">
                                <button type="button" class="btn btn-secondary" id="createBackup">
                                    <i class="fas fa-save"></i>
                                    Criar Backup Agora
                                </button>
                                <button type="button" class="btn btn-secondary" id="downloadBackup">
                                    <i class="fas fa-download"></i>
                                    Baixar Último Backup
                                </button>
                            </div>
                            <div class="backup-info">
                                <p><strong>Último backup:</strong> <span id="lastBackupDate">Nunca</span></p>
                                <p><strong>Tamanho:</strong> <span id="lastBackupSize">-</span></p>
                                <p><strong>Status:</strong> <span id="backupStatus">Aguardando...</span></p>
                            </div>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">📤 Restaurar Configurações</h3>
                            <p class="config-description">Restaurar de um backup anterior</p>
                        </div>
                        <div class="config-content">
                            <div class="input-group">
                                <label for="backup_file">Arquivo de Backup</label>
                                <input type="file" id="backup_file" name="backup_file" accept=".json,.zip">
                                <small>Selecione um arquivo de backup para restaurar</small>
                            </div>
                            <button type="button" class="btn btn-warning" id="restoreBackup">
                                <i class="fas fa-upload"></i>
                                Restaurar Backup
                            </button>
                            <div class="warning-message">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Atenção: Restaurar um backup substituirá todas as configurações atuais!</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </section>
    </main>

    <!-- Scripts -->
    <script src="/js/dashboard-advanced.js?v=2.1.3"></script>
    <!-- <script src="/js/dashboard-help.js"></script> Sistema de help desabilitado -->
    <script>
        // SISTEMA HÍBRIDO - NAVEGAÇÃO + SALVAMENTO GARANTIDO
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🛡️ Dashboard Nodex | Moderação carregado! VERSÃO HÍBRIDA');

            // Aguardar um pouco para garantir que tudo carregue
            setTimeout(() => {
                console.log('🔧 INICIALIZANDO SISTEMA HÍBRIDO...');

                // 1. NAVEGAÇÃO SIMPLES E ROBUSTA
                setupSimpleNavigation();

                // 2. SISTEMA DE SALVAMENTO GARANTIDO
                setupSaveButtons();

                // 3. INICIALIZAR ESTADOS BASEADOS NO BANCO DE DADOS
                initializeFormStates();

                console.log('✅ SISTEMA HÍBRIDO CONFIGURADO');

            }, 300);
        });

        // Navegação simples que sempre funciona
        function setupSimpleNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            const sections = document.querySelectorAll('.config-section');

            console.log(`🔧 Configurando navegação simples: ${navItems.length} itens, ${sections.length} seções`);

            function showSection(sectionId) {
                console.log(`🔄 Navegando para: ${sectionId}`);

                // Esconder todas as seções
                sections.forEach(section => {
                    section.classList.remove('active');
                    section.style.display = 'none';
                });

                // Remover active de todos os nav items
                navItems.forEach(item => item.classList.remove('active'));

                // Mostrar seção alvo
                const targetSection = document.getElementById(sectionId);
                const targetNavItem = document.querySelector(`[data-section="${sectionId}"]`);

                if (targetSection) {
                    targetSection.classList.add('active');
                    targetSection.style.display = 'block';
                    console.log(`✅ Seção mostrada: ${sectionId}`);
                }

                if (targetNavItem) {
                    targetNavItem.classList.add('active');
                    console.log(`✅ Nav item ativado: ${sectionId}`);
                }
            }

            // Adicionar eventos de clique
            navItems.forEach(item => {
                const sectionId = item.dataset.section;
                if (sectionId) {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        showSection(sectionId);
                    });
                }
            });

            // Mostrar seção inicial
            showSection('geral');
        }

        // Sistema de salvamento garantido com validação de dependências
        function setupSaveButtons() {
            const saveButtons = document.querySelectorAll('.btn-save-section');
            console.log(`🔧 Configurando ${saveButtons.length} botões de salvamento`);

            saveButtons.forEach(button => {
                const section = button.dataset.section;
                console.log(`🔧 Configurando botão para seção: ${section}`);

                button.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log(`🚀 SALVANDO SEÇÃO: ${section}`);

                    // Validar dependências antes de salvar
                    if (!validateSectionDependencies(section)) {
                        showToast('Configure a função principal antes das sub-funções!', 'error');
                        return;
                    }

                    // Atualizar botão
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Salvando...';
                    button.disabled = true;

                    try {
                        // Coletar dados da seção
                        const sectionElement = document.getElementById(section);
                        const inputs = sectionElement.querySelectorAll('input, select, textarea');
                        const data = { settings: {} };

                        inputs.forEach(input => {
                            if (input.name) {
                                if (input.type === 'checkbox') {
                                    data.settings[input.name] = input.checked;
                                } else if (input.type === 'radio' && input.checked) {
                                    data.settings[input.name] = input.value;
                                } else if (input.type !== 'radio') {
                                    data.settings[input.name] = input.value;
                                }
                            }
                        });

                        console.log(`🔧 Dados coletados para ${section}:`, data);

                        // Obter guild ID
                        const pathParts = window.location.pathname.split('/');
                        const guildId = pathParts[pathParts.length - 1];

                        // Enviar para API
                        const response = await fetch(`/api/guild/${guildId}/config/${section}`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(data)
                        });

                        const result = await response.json();

                        if (result.success) {
                            button.innerHTML = '<i class="fas fa-check"></i> Salvo!';
                            button.style.background = '#2ecc71';
                            showToast(`Configurações de ${section} salvas com sucesso!`, 'success');

                            // Atualizar dependências após salvar
                            updateSectionDependencies(section);

                            setTimeout(() => {
                                button.innerHTML = originalText;
                                button.style.background = '';
                                button.disabled = false;
                            }, 3000);
                        } else {
                            throw new Error(result.error || 'Erro desconhecido');
                        }

                    } catch (error) {
                        console.error(`❌ Erro ao salvar ${section}:`, error);
                        button.innerHTML = '<i class="fas fa-times"></i> Erro';
                        button.style.background = '#e74c3c';
                        showToast(`Erro ao salvar: ${error.message}`, 'error');

                        setTimeout(() => {
                            button.innerHTML = originalText;
                            button.style.background = '';
                            button.disabled = false;
                        }, 3000);
                    }
                });
            });

            // Configurar sistema de dependências
            setupDependencySystem();

            // Configurar sistema de abas para verificação
            setupVerificationTabs();

            // Configurar toggle principal de verificação
            setupVerificationMasterToggle();
        }

        // Sistema de dependências entre funções principais e sub-funções
        function setupDependencySystem() {
            console.log('🔧 Configurando sistema de dependências...');

            // Definir dependências de cada módulo
            const dependencies = {
                'moderacao': {
                    main: 'auto_mod_enabled',
                    subs: ['mod_log_channel_id', 'timeout_role_id', 'anti_spam_enabled', 'anti_links_enabled', 'anti_caps_enabled']
                },
                'ai': {
                    main: 'ai_moderation_enabled',
                    subs: ['ai_sensitivity', 'second_chance_enabled', 'ai_dm_user', 'ai_public_message', 'ai_timeout_duration']
                },
                'antiraid': {
                    main: 'anti_raid_enabled',
                    subs: ['max_joins_per_minute', 'min_account_age', 'quarantine_enabled', 'quarantine_role_id', 'quarantine_duration']
                },
                'logs': {
                    main: 'logs_enabled',
                    subs: ['general_log_channel', 'ai_log_channel', 'raid_log_channel']
                },
                'verificacao': {
                    main: 'verification_enabled',
                    subs: ['verification_channel_id', 'verified_role_id', 'unverified_role_id', 'verification_method', 'verification_timeout_minutes', 'verification_auto_kick', 'verification_dm_enabled', 'verification_log_enabled', 'verification_anti_bot', 'verification_ip_check', 'verification_max_attempts', 'verification_rules_text', 'welcome_message']
                },
                'analytics': {
                    main: 'analytics_enabled',
                    subs: ['daily_reports', 'weekly_reports', 'reports_channel', 'track_message_count']
                },
                'backup': {
                    main: 'backup_enabled',
                    subs: ['auto_backup', 'backup_interval', 'backup_channel_id', 'backup_retention']
                }
            };

            // Configurar eventos para cada módulo
            Object.keys(dependencies).forEach(section => {
                const config = dependencies[section];
                const mainToggle = document.querySelector(`input[name="${config.main}"]`);

                if (mainToggle) {
                    // Configurar estado inicial
                    updateSubFunctions(section, mainToggle.checked);

                    // Adicionar listener para mudanças
                    mainToggle.addEventListener('change', (e) => {
                        console.log(`🔄 Toggle principal ${config.main}: ${e.target.checked}`);
                        updateSubFunctions(section, e.target.checked);
                    });
                }
            });

            console.log('✅ Sistema de dependências configurado!');
        }

        // Atualizar estado das sub-funções baseado na função principal
        function updateSubFunctions(section, mainEnabled) {
            const dependencies = {
                'moderacao': ['mod_log_channel_id', 'timeout_role_id', 'anti_spam_enabled', 'anti_links_enabled', 'anti_caps_enabled'],
                'ai': ['ai_sensitivity', 'second_chance_enabled', 'ai_dm_user', 'ai_public_message', 'ai_timeout_duration'],
                'antiraid': ['max_joins_per_minute', 'min_account_age', 'quarantine_enabled', 'quarantine_role_id', 'quarantine_duration'],
                'logs': ['general_log_channel', 'ai_log_channel', 'raid_log_channel'],
                'verificacao': ['verification_channel_id', 'verification_role_id', 'verification_message', 'verification_type'],
                'analytics': ['analytics_channel_id', 'daily_reports', 'weekly_reports', 'member_tracking'],
                'backup': ['auto_backup', 'backup_interval', 'backup_channel_id', 'backup_retention']
            };

            const subFields = dependencies[section] || [];

            subFields.forEach(fieldName => {
                const field = document.querySelector(`input[name="${fieldName}"], select[name="${fieldName}"], textarea[name="${fieldName}"]`);
                const container = field?.closest('.form-group, .config-item, .toggle-group, .config-card');

                if (field && container) {
                    if (mainEnabled) {
                        // Habilitar sub-função
                        field.disabled = false;
                        container.style.opacity = '1';
                        container.style.pointerEvents = 'auto';
                        container.classList.remove('disabled');
                    } else {
                        // Desabilitar sub-função APENAS VISUALMENTE - NÃO LIMPAR VALORES
                        field.disabled = true;
                        container.style.opacity = '0.4';
                        container.style.pointerEvents = 'none';
                        container.classList.add('disabled');

                        // NÃO limpar valores - manter configurações salvas
                        // Os valores ficam salvos no banco, apenas desabilitados visualmente
                    }
                }
            });

            console.log(`🔧 Sub-funções de ${section} ${mainEnabled ? 'habilitadas' : 'desabilitadas'} (valores preservados)`);
        }

        // Validar dependências antes de salvar
        function validateSectionDependencies(section) {
            const dependencies = {
                'moderacao': 'auto_mod_enabled',
                'ai': 'ai_moderation_enabled',
                'antiraid': 'anti_raid_enabled',
                'logs': 'logs_enabled',
                'verificacao': 'verification_enabled',
                'analytics': 'analytics_enabled',
                'backup': 'backup_enabled'
            };

            const mainToggleName = dependencies[section];
            if (!mainToggleName) return true; // Seções sem dependências sempre válidas

            const mainToggle = document.querySelector(`input[name="${mainToggleName}"]`);
            if (!mainToggle) return true; // Se não encontrar o toggle, permitir

            // Se a função principal não estiver ativada, verificar se há sub-funções ativadas
            if (!mainToggle.checked) {
                const sectionElement = document.getElementById(section);
                const subInputs = sectionElement?.querySelectorAll('input[type="checkbox"]:not([name="' + mainToggleName + '"]):checked');

                if (subInputs && subInputs.length > 0) {
                    console.log(`❌ Validação falhou: sub-funções ativadas sem função principal em ${section}`);
                    return false;
                }
            }

            return true;
        }

        // Atualizar dependências após salvar
        function updateSectionDependencies(section) {
            const dependencies = {
                'moderacao': 'auto_mod_enabled',
                'ai': 'ai_moderation_enabled',
                'antiraid': 'anti_raid_enabled',
                'logs': 'logs_enabled',
                'verificacao': 'verification_enabled',
                'analytics': 'analytics_enabled',
                'backup': 'backup_enabled'
            };

            const mainToggleName = dependencies[section];
            if (mainToggleName) {
                const mainToggle = document.querySelector(`input[name="${mainToggleName}"]`);
                if (mainToggle) {
                    updateSubFunctions(section, mainToggle.checked);
                }
            }
        }

        // Sistema de abas para verificação
        function setupVerificationTabs() {
            console.log('🔧 Configurando sistema de abas de verificação...');

            const tabButtons = document.querySelectorAll('.verification-config-tabs .tab-btn');
            const tabContents = document.querySelectorAll('.verification-config-tabs .tab-content');

            if (tabButtons.length === 0) {
                console.log('⚠️ Nenhuma aba de verificação encontrada');
                return;
            }

            console.log(`🔧 Encontradas ${tabButtons.length} abas de verificação`);

            tabButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetTab = button.getAttribute('data-tab');
                    console.log(`🔄 Mudando para aba: ${targetTab}`);

                    // Remover classe active de todos os botões e conteúdos
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Adicionar classe active ao botão clicado
                    button.classList.add('active');

                    // Mostrar conteúdo da aba correspondente
                    const targetContent = document.getElementById(`${targetTab}-tab`);
                    if (targetContent) {
                        targetContent.classList.add('active');
                        console.log(`✅ Aba ${targetTab} ativada`);
                    } else {
                        console.error(`❌ Conteúdo da aba ${targetTab} não encontrado`);
                    }
                });
            });

            console.log('✅ Sistema de abas de verificação configurado!');
        }

        // Sistema de toggle principal de verificação
        function setupVerificationMasterToggle() {
            console.log('🔧 Configurando toggle principal de verificação...');

            const masterToggle = document.getElementById('verification_enabled');
            const statusIndicator = document.getElementById('verificationIndicator');
            const statusDot = statusIndicator?.querySelector('.status-dot');
            const statusText = statusIndicator?.querySelector('.status-text');

            if (!masterToggle) {
                console.log('⚠️ Toggle principal de verificação não encontrado');
                return;
            }

            masterToggle.addEventListener('change', (e) => {
                const isEnabled = e.target.checked;
                console.log(`🔄 Toggle de verificação: ${isEnabled ? 'ATIVADO' : 'DESATIVADO'}`);

                // Atualizar indicador visual
                if (statusDot && statusText) {
                    statusDot.className = `status-dot ${isEnabled ? 'active' : 'inactive'}`;
                    statusText.textContent = isEnabled ? 'Sistema Ativo' : 'Sistema Inativo';
                }

                // Atualizar sistema de dependências
                updateSubFunctions('verificacao', isEnabled);

                console.log(`✅ Status visual atualizado: ${isEnabled ? 'ATIVO' : 'INATIVO'}`);
            });

            console.log('✅ Toggle principal de verificação configurado!');
        }

        // Inicializar estados do formulário baseado nos dados do banco
        function initializeFormStates() {
            console.log('🔧 Inicializando estados do formulário...');

            // Inicializar estado do auto-kick
            const autoKickToggle = document.getElementById('verification_auto_kick');
            if (autoKickToggle) {
                console.log(`🔧 Auto-kick inicial: ${autoKickToggle.checked}`);
                toggleAutoKick(); // Aplicar estado inicial
            }

            // Inicializar preview de verificação
            updateVerificationPreview();
            updateTimeoutPreview();
            updateRulesPreview();

            console.log('✅ Estados do formulário inicializados');
        }

        // Funções específicas do módulo de verificação
        function updateVerificationPreview() {
            const method = document.getElementById('verificationMethod')?.value;
            const previewMethod = document.getElementById('previewMethod');

            if (!previewMethod) return;

            const methodTexts = {
                'reaction': '<i class="fas fa-mouse-pointer"></i> <span>Clique no botão "Verificar-me"</span>',
                'captcha_math': '<i class="fas fa-calculator"></i> <span>Resolva: 7 + 3 = ?</span>',
                'captcha_text': '<i class="fas fa-keyboard"></i> <span>Digite: NODEX</span>',
                'captcha_emoji': '<i class="fas fa-smile"></i> <span>Selecione: 🎯</span>',
                'manual': '<i class="fas fa-user-check"></i> <span>Aguarde aprovação manual</span>',
                'combined': '<i class="fas fa-tasks"></i> <span>Múltiplas etapas</span>'
            };

            previewMethod.innerHTML = `<div class="method-display">${methodTexts[method] || '<i class="fas fa-cog"></i> <span>Selecione um método</span>'}</div>`;

            // Mostrar/esconder configurações específicas
            document.querySelectorAll('.method-section').forEach(section => section.style.display = 'none');
            if (method === 'manual') {
                const manualConfig = document.getElementById('manualConfig');
                if (manualConfig) manualConfig.style.display = 'block';
            } else if (method === 'combined') {
                const combinedConfig = document.getElementById('combinedConfig');
                if (combinedConfig) combinedConfig.style.display = 'block';
            }

            console.log(`🔄 Preview atualizado para método: ${method}`);
        }

        function updateTimeoutPreview() {
            const timeout = document.getElementById('verificationTimeout')?.value || 30;
            const previewTimeout = document.getElementById('previewTimeout');

            if (previewTimeout) {
                previewTimeout.innerHTML = `<i class="fas fa-clock"></i> <span>Tempo limite: ${timeout} minutos</span>`;
            }

            console.log(`🔄 Timeout atualizado: ${timeout} minutos`);
        }

        function toggleAutoKick() {
            const autoKick = document.getElementById('verification_auto_kick')?.checked;
            const autoKickConfig = document.getElementById('autoKickConfig');

            if (autoKickConfig) {
                autoKickConfig.style.display = autoKick ? 'block' : 'none';
            }

            console.log(`🔄 Auto-kick ${autoKick ? 'ativado' : 'desativado'}`);
        }

        function updateRulesPreview() {
            const rules = document.getElementById('verificationRules')?.value || 'Leia as regras do servidor antes de se verificar.';
            const previewRules = document.getElementById('previewRules');

            if (previewRules) {
                previewRules.textContent = rules;
            }

            console.log('🔄 Preview das regras atualizado');
        }

        function updateWelcomePreview() {
            const welcome = document.getElementById('welcomeMessage')?.value || 'Bem-vindo(a) ao servidor! Você foi verificado(a) com sucesso.';
            console.log('🔄 Mensagem de boas-vindas atualizada:', welcome);
        }

        // Toast simples
        function showToast(message, type) {
            console.log(`🍞 TOAST [${type}]: ${message}`);

            // Criar toast element
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                animation: slideIn 0.3s ease-out;
                background: ${type === 'success' ? '#2ecc71' : '#e74c3c'};
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 4000);
        }

        // Carregar ícones SVG
        if (typeof SVGIcons !== 'undefined') {
            // Ícones principais
            const iconMappings = {
                'sidebar-shield': 'shield',
                'main-shield': 'shield',
                'settings-icon': 'settings',
                'language-icon': 'info',
                'time-icon': 'info',
                'color-icon': 'settings'
            };

            Object.entries(iconMappings).forEach(([elementId, iconName]) => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.innerHTML = SVGIcons.get(iconName);
                }
            });
        }
    </script>
</body>
</html>
