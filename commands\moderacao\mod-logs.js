/**
 * ========================================
 * PREMIUM MODERATION LOGS COMMAND
 * Advanced moderation history with premium styling
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const SVGIcons = require('../../utils/svgIcons');
const EmbedStyles = require('../../utils/EmbedStyles');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('mod-logs')
        .setDescription('Visualizar histórico de moderação')
        .addUserOption(option =>
            option.setName('usuário')
                .setDescription('Usuário para ver o histórico')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('ação')
                .setDescription('Filtrar por tipo de ação')
                .setRequired(false)
                .addChoices(
                    { name: 'Banimentos', value: 'ban' },
                    { name: 'Expulsões', value: 'kick' },
                    { name: 'Advertências', value: 'warn' },
                    { name: 'Mutes/Timeouts', value: 'timeout' },
                    { name: 'Limpeza de Mensagens', value: 'clear' },
                    { name: 'Todas as Ações', value: 'all' }
                )
        )
        .addIntegerOption(option =>
            option.setName('página')
                .setDescription('Página do histórico (padrão: 1)')
                .setRequired(false)
                .setMinValue(1)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .setDMPermission(false),

    async execute(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const targetUser = interaction.options.getUser('usuário');
        const actionFilter = interaction.options.getString('ação') || 'all';
        const page = interaction.options.getInteger('página') || 1;

        try {
            await interaction.deferReply();

            // Obter histórico de moderação
            let moderationHistory;
            if (targetUser) {
                moderationHistory = client.database.getModerationHistory(targetUser.id, guild.id);
            } else {
                moderationHistory = client.database.getAllModerationHistory(guild.id);
            }

            // Garantir que moderationHistory é um array
            if (!moderationHistory || !Array.isArray(moderationHistory)) {
                moderationHistory = [];
            }

            if (moderationHistory.length === 0) {
                const noHistoryEmbed = embedStyles.createInfoEmbed(
                    'Histórico Vazio',
                    targetUser ? 
                        `${embedStyles.format.bold('Nenhum histórico de moderação encontrado para este usuário.')}\n\n${embedStyles.icons.info}**Usuário:**${targetUser.tag}\n${embedStyles.format.italic('Este usuário não possui registros de moderação.')}` :
                        `${embedStyles.format.bold('Nenhum histórico de moderação encontrado para este servidor.')}\n\n${embedStyles.format.italic('Ainda não foram executadas ações de moderação registradas.')}`
                );
                return await interaction.editReply({ embeds: [noHistoryEmbed] });
            }

            // Filtrar por ação se especificado
            if (actionFilter !== 'all') {
                moderationHistory = moderationHistory.filter(entry => entry.action === actionFilter);
            }

            if (moderationHistory.length === 0) {
                const noFilteredEmbed = embedStyles.createInfoEmbed(
                    'Nenhum Resultado',
                    `${embedStyles.format.bold('Nenhum registro encontrado com os filtros aplicados.')}\n\n${embedStyles.icons.info}**Filtros:**\n•**Usuário:**${targetUser ? targetUser.tag : 'Todos'}\n•**Ação:**${actionFilter}\n\n${embedStyles.format.italic('Tente ajustar os filtros de busca.')}`
                );
                return await interaction.editReply({ embeds: [noFilteredEmbed] });
            }

            // Paginação
            const itemsPerPage = 10;
            const totalPages = Math.ceil(moderationHistory.length / itemsPerPage);
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageEntries = moderationHistory.slice(startIndex, endIndex);

            // Criar embed do histórico
            const historyEmbed = await this.createHistoryEmbed(embedStyles, guild, pageEntries, page, totalPages, targetUser, actionFilter, client);

            // Criar botões de navegação
            const navigationRow = this.createNavigationButtons(page, totalPages, targetUser?.id, actionFilter);

            await interaction.editReply({
                embeds: [historyEmbed],
                components: navigationRow ? [navigationRow] : []
            });

        } catch (error) {
            client.logger.error('Erro no comando mod-logs:', error);
            
            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema de Logs',
                `${embedStyles.format.bold('Ocorreu um erro ao buscar o histórico de moderação.')}\n\n${embedStyles.icons.info}**Detalhes:**\n• Verifique as permissões do bot\n• Tente novamente em alguns segundos\n• Contate o suporte se o problema persistir`
            );
            
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    async createHistoryEmbed(embedStyles, guild, entries, page, totalPages, targetUser, actionFilter, client) {
        const actionIcons = {
            ban: embedStyles.icons.ban,
            kick: embedStyles.icons.kick,
            warn: embedStyles.icons.warn,
            timeout: embedStyles.icons.timeout,
            clear: embedStyles.icons.clear,
            unmute: embedStyles.icons.success
        };

        const actionNames = {
            ban: 'Banimento',
            kick: 'Expulsão',
            warn: 'Advertência',
            timeout: 'Mute/Timeout',
            clear: 'Limpeza',
            unmute: 'Desmute'
        };

        const historyEmbed = {
            color: parseInt(embedStyles.colors.info.replace('#', ''), 16),
            title: `${embedStyles.icons.logs} ${embedStyles.format.bold('Histórico de Moderação Premium')}`,
            description: targetUser ? 
                `**Histórico completo de ${embedStyles.format.bold(targetUser.tag)}**\n\n${embedStyles.format.italic('Todas as ações de moderação registradas para este usuário')}` :
                `**Histórico completo do servidor ${embedStyles.format.bold(guild.name)}**\n\n${embedStyles.format.italic('Todas as ações de moderação registradas no servidor')}`,
            fields: [],
            timestamp: new Date().toISOString(),
            footer: {
                text: `Nodex | Moderação • Página ${page}/${totalPages} • ${entries.length} registros`,
                icon_url: guild.iconURL()
            },
            thumbnail: {
                url: targetUser ? targetUser.displayAvatarURL({ dynamic: true }) : guild.iconURL({ dynamic: true })
            }
        };

        // Adicionar informações de filtro
        historyEmbed.fields.push({
            name: `${embedStyles.icons.info}**Filtros Aplicados**`,
            value: `${embedStyles.format.bold('Usuário:')} ${targetUser ? targetUser.tag : 'Todos os usuários'}\n${embedStyles.format.bold('Ação:')} ${actionFilter === 'all' ? 'Todas as ações' : actionNames[actionFilter] || actionFilter}\n${embedStyles.format.bold('Total de Registros:')} ${entries.length}`,
            inline: false
        });

        // Adicionar entradas do histórico
        for (let i = 0; i < entries.length; i++) {
            const entry = entries[i];
            const entryNumber = (page - 1) * 10 + i + 1;
            
            // Obter informações do usuário e moderador
            let targetUserInfo = 'Usuário Desconhecido';
            let moderatorInfo = 'Moderador Desconhecido';
            
            try {
                const targetUserObj = await client.users.fetch(entry.user_id).catch(() => null);
                const moderatorObj = await client.users.fetch(entry.moderator_id).catch(() => null);
                
                targetUserInfo = targetUserObj ? `${targetUserObj.tag}\n${embedStyles.format.code(targetUserObj.id)}` : `ID: ${entry.user_id}`;
                moderatorInfo = moderatorObj ? `${moderatorObj.tag}\n${embedStyles.format.code(moderatorObj.id)}` : `ID: ${entry.moderator_id}`;
            } catch (error) {
                // Usar IDs como fallback
                targetUserInfo = `ID: ${entry.user_id}`;
                moderatorInfo = `ID: ${entry.moderator_id}`;
            }

            const actionIcon = actionIcons[entry.action] || embedStyles.icons.info;
            const actionName = actionNames[entry.action] || entry.action;
            
            const entryDate = new Date(entry.timestamp);
            const timeAgo = embedStyles.format.timestampRelative(entryDate);
            
            let entryValue = `${embedStyles.format.bold('Usuário:')} ${targetUserInfo}\n${embedStyles.format.bold('Moderador:')} ${moderatorInfo}\n${embedStyles.format.bold('Motivo:')} ${embedStyles.format.code(entry.reason || 'Não especificado')}\n${embedStyles.format.bold('Data:')} ${timeAgo}`;
            
            // Adicionar informações específicas da ação
            if (entry.duration) {
                entryValue += `\n${embedStyles.format.bold('Duração:')} ${embedStyles.formatDuration(entry.duration)}`;
            }
            
            if (entry.additional_data) {
                try {
                    const additionalData = JSON.parse(entry.additional_data);
                    if (additionalData.automatic) {
                        entryValue += `\n${embedStyles.format.bold('Tipo:')} Ação Automática`;
                    }
                    if (additionalData.dmSent !== undefined) {
                        entryValue += `\n${embedStyles.format.bold('DM:')} ${additionalData.dmSent ? 'Enviada' : 'Não enviada'}`;
                    }
                } catch (error) {
                    // Ignorar erro de parsing
                }
            }

            historyEmbed.fields.push({
                name: `${actionIcon}**${entryNumber}. ${actionName}**`,
                value: entryValue,
                inline: false
            });
        }

        return historyEmbed;
    },

    createNavigationButtons(currentPage, totalPages, userId, actionFilter) {
        if (totalPages <= 1) return null;

        const row = new ActionRowBuilder();

        // Botão Primeira Página
        if (currentPage > 1) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`modlogs_first_${userId || 'all'}_${actionFilter}_1`)
                    .setLabel('⏮️ Primeira')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(currentPage === 1)
            );
        }

        // Botão Página Anterior
        if (currentPage > 1) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`modlogs_prev_${userId || 'all'}_${actionFilter}_${currentPage - 1}`)
                    .setLabel('◀️ Anterior')
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(currentPage === 1)
            );
        }

        // Botão Próxima Página
        if (currentPage < totalPages) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`modlogs_next_${userId || 'all'}_${actionFilter}_${currentPage + 1}`)
                    .setLabel('Próxima')
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(currentPage === totalPages)
            );
        }

        // Botão Última Página
        if (currentPage < totalPages) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`modlogs_last_${userId || 'all'}_${actionFilter}_${totalPages}`)
                    .setLabel('Última')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(currentPage === totalPages)
            );
        }

        return row.components.length > 0 ? row : null;
    },

    cooldown: 5,
    category: 'moderacao',
    examples: [
        '/mod-logs',
        '/mod-logs usuário:@usuário',
        '/mod-logs ação:ban página:2',
        '/mod-logs usuário:@usuário ação:warn'
    ]
};
