<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <style>
        .backup-module {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .backup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid rgba(0, 255, 127, 0.2);
        }

        .backup-title {
            color: #00ff7f;
            font-size: 2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .backup-actions {
            display: flex;
            gap: 1rem;
        }

        .btn-backup {
            background: linear-gradient(135deg, #00ff7f, #00cc66);
            color: #000;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-backup:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 255, 127, 0.3);
        }

        .backup-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .backup-section {
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(0, 255, 127, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
        }

        .section-title {
            color: #fff;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .backup-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            color: #ccc;
            font-weight: 500;
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #ccc;
        }

        .form-checkbox input {
            accent-color: #00ff7f;
        }

        .backup-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .backup-item {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .backup-item:hover {
            border-color: rgba(0, 255, 127, 0.3);
            background: rgba(0, 255, 127, 0.05);
        }

        .backup-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .backup-details h4 {
            color: #fff;
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
        }

        .backup-meta {
            color: #888;
            font-size: 0.85rem;
        }

        .backup-size {
            color: #00ff7f;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .backup-actions-item {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .btn-restore {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-delete {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-small:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .loading {
            text-align: center;
            color: #888;
            padding: 2rem;
        }

        .no-backups {
            text-align: center;
            color: #888;
            padding: 2rem;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff7f, #00cc66);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .backup-grid {
                grid-template-columns: 1fr;
            }
            
            .backup-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
            
            .backup-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="backup-module">
        <div class="backup-header">
            <h1 class="backup-title">
                <i class="fas fa-save"></i>
                Sistema de Backup
            </h1>
            <div class="backup-actions">
                <button class="btn-backup" onclick="createBackup()">
                    <i class="fas fa-plus"></i>
                    Criar Backup
                </button>
                <button class="btn-backup" onclick="refreshBackups()">
                    <i class="fas fa-sync-alt"></i>
                    Atualizar
                </button>
            </div>
        </div>

        <div class="backup-grid">
            <!-- Criar Backup -->
            <div class="backup-section">
                <h2 class="section-title">
                    <i class="fas fa-plus-circle"></i>
                    Criar Novo Backup
                </h2>
                <form class="backup-form" id="backupForm">
                    <div class="form-group">
                        <label class="form-label">Componentes a incluir:</label>
                        <div class="form-checkbox">
                            <input type="checkbox" id="includeChannels" checked>
                            <label for="includeChannels">Canais e Categorias</label>
                        </div>
                        <div class="form-checkbox">
                            <input type="checkbox" id="includeRoles" checked>
                            <label for="includeRoles">Cargos e Permissões</label>
                        </div>
                        <div class="form-checkbox">
                            <input type="checkbox" id="includeEconomy" checked>
                            <label for="includeEconomy">Sistema de Economia</label>
                        </div>
                        <div class="form-checkbox">
                            <input type="checkbox" id="includeLevels" checked>
                            <label for="includeLevels">Sistema de Níveis</label>
                        </div>
                        <div class="form-checkbox">
                            <input type="checkbox" id="includeUserData" checked>
                            <label for="includeUserData">Dados de Usuários</label>
                        </div>
                    </div>
                    <button type="submit" class="btn-backup">
                        <i class="fas fa-save"></i>
                        Criar Backup Completo
                    </button>
                    <div class="progress-bar" id="backupProgress" style="display: none;">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </form>
            </div>

            <!-- Lista de Backups -->
            <div class="backup-section">
                <h2 class="section-title">
                    <i class="fas fa-list"></i>
                    Backups Disponíveis
                </h2>
                <div class="backup-list" id="backupList">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        Carregando backups...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const guildId = '<%= guild.id %>';
        let backups = [];

        // Carregar backups ao inicializar
        document.addEventListener('DOMContentLoaded', function() {
            loadBackups();
        });

        // Criar backup
        document.getElementById('backupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await createBackup();
        });

        async function createBackup() {
            const form = document.getElementById('backupForm');
            const progressBar = document.getElementById('backupProgress');
            const progressFill = document.getElementById('progressFill');
            
            const options = {
                includeChannels: document.getElementById('includeChannels').checked,
                includeRoles: document.getElementById('includeRoles').checked,
                includeEconomy: document.getElementById('includeEconomy').checked,
                includeLevels: document.getElementById('includeLevels').checked,
                includeUserData: document.getElementById('includeUserData').checked
            };

            try {
                // Mostrar barra de progresso
                progressBar.style.display = 'block';
                progressFill.style.width = '0%';
                
                // Simular progresso
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress > 90) progress = 90;
                    progressFill.style.width = progress + '%';
                }, 200);

                const response = await fetch(`/api/guild/${guildId}/backup/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ options })
                });

                const result = await response.json();
                
                clearInterval(progressInterval);
                progressFill.style.width = '100%';
                
                setTimeout(() => {
                    progressBar.style.display = 'none';
                    progressFill.style.width = '0%';
                }, 1000);

                if (result.success) {
                    alert('✅ Backup criado com sucesso!');
                    loadBackups(); // Recarregar lista
                } else {
                    alert('❌ Erro ao criar backup: ' + result.error);
                }
            } catch (error) {
                console.error('Erro ao criar backup:', error);
                alert('❌ Erro ao criar backup!');
                progressBar.style.display = 'none';
            }
        }

        async function loadBackups() {
            const backupList = document.getElementById('backupList');
            
            try {
                const response = await fetch(`/api/guild/${guildId}/backup/list`);
                const result = await response.json();
                
                if (result.success && result.backups.length > 0) {
                    backups = result.backups;
                    renderBackups();
                } else {
                    backupList.innerHTML = `
                        <div class="no-backups">
                            <i class="fas fa-info-circle"></i>
                            <p>Nenhum backup encontrado</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erro ao carregar backups:', error);
                backupList.innerHTML = `
                    <div class="no-backups">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Erro ao carregar backups</p>
                    </div>
                `;
            }
        }

        function renderBackups() {
            const backupList = document.getElementById('backupList');
            
            backupList.innerHTML = backups.map(backup => `
                <div class="backup-item">
                    <div class="backup-info">
                        <div class="backup-details">
                            <h4>${backup.type === 'full' ? '🔄 Backup Completo' : '📋 Backup Seletivo'}</h4>
                            <div class="backup-meta">
                                Criado em: ${new Date(backup.created_at).toLocaleString('pt-BR')}<br>
                                Por: ${backup.created_by}
                            </div>
                        </div>
                        <div class="backup-size">${backup.size_formatted || 'N/A'}</div>
                    </div>
                    <div class="backup-actions-item">
                        <button class="btn-small btn-restore" onclick="restoreBackup('${backup.id}')">
                            <i class="fas fa-undo"></i>
                            Restaurar
                        </button>
                        <button class="btn-small btn-delete" onclick="deleteBackup('${backup.id}')">
                            <i class="fas fa-trash"></i>
                            Excluir
                        </button>
                    </div>
                </div>
            `).join('');
        }

        async function restoreBackup(backupId) {
            if (!confirm('⚠️ ATENÇÃO: Restaurar um backup pode sobrescrever dados atuais. Deseja continuar?')) {
                return;
            }

            try {
                const response = await fetch(`/api/guild/${guildId}/backup/restore`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ backupId })
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('✅ Backup restaurado com sucesso!');
                } else {
                    alert('❌ Erro ao restaurar backup: ' + result.error);
                }
            } catch (error) {
                console.error('Erro ao restaurar backup:', error);
                alert('❌ Erro ao restaurar backup!');
            }
        }

        async function deleteBackup(backupId) {
            if (!confirm('⚠️ Tem certeza que deseja excluir este backup? Esta ação não pode ser desfeita.')) {
                return;
            }

            try {
                const response = await fetch(`/api/guild/${guildId}/backup/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ backupId })
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('✅ Backup excluído com sucesso!');
                    loadBackups(); // Recarregar lista
                } else {
                    alert('❌ Erro ao excluir backup: ' + result.error);
                }
            } catch (error) {
                console.error('Erro ao excluir backup:', error);
                alert('❌ Erro ao excluir backup!');
            }
        }

        function refreshBackups() {
            loadBackups();
        }
    </script>
</body>
</html>
