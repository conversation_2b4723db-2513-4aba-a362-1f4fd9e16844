/**
 * ========================================
 * COMANDO: CLEAR (LIMPAR MENSAGENS)
 * Remove mensagens do canal
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const EmbedStyles = require('../../utils/EmbedStyles');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('clear')
        .setNameLocalizations({
            'pt-BR': 'limpar'
        })
        .setDescription('Remove mensagens do canal')
        .setDescriptionLocalizations({
            'pt-BR': 'Remove uma quantidade específica de mensagens do canal'
        })
        .setDefaultMemberPermissions(8192) // MANAGE_MESSAGES
        .addIntegerOption(option =>
            option.setName('quantidade')
                .setDescription('Quantidade de mensagens para remover')
                .setDescriptionLocalizations({ 'pt-BR': 'Quantidade de mensagens para remover (1-100)' })
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(100)
        )
        .addUserOption(option =>
            option.setName('usuario')
                .setNameLocalizations({ 'pt-BR': 'usuário' })
                .setDescription('Remover apenas mensagens deste usuário')
                .setDescriptionLocalizations({ 'pt-BR': 'Remover apenas mensagens de um usuário específico' })
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('motivo')
                .setDescription('Motivo da limpeza')
                .setDescriptionLocalizations({ 'pt-BR': 'Motivo para a limpeza das mensagens' })
                .setRequired(false)
                .setMaxLength(500)
        ),
    
    category: 'moderacao',
    cooldown: 5,
    botPermissions: ['ManageMessages'],
    
    async execute(interaction) {
        const embedStyles = new EmbedStyles();

        try {
            const amount = interaction.options.getInteger('quantidade');
            const targetUser = interaction.options.getUser('usuario');
            const reason = interaction.options.getString('motivo') || 'Limpeza de mensagens';

            await interaction.deferReply({ ephemeral: true });

            // Buscar mensagens
            const messages = await interaction.channel.messages.fetch({ limit: 100 });
            
            let messagesToDelete = messages.first(amount);

            // Filtrar por usuário se especificado
            if (targetUser) {
                messagesToDelete = messages.filter(msg => msg.author.id === targetUser.id).first(amount);
            }

            // Filtrar mensagens muito antigas (mais de 14 dias)
            const twoWeeksAgo = Date.now() - (14 * 24 * 60 * 60 * 1000);
            messagesToDelete = messagesToDelete.filter(msg => msg.createdTimestamp > twoWeeksAgo);

            if (messagesToDelete.size === 0) {
                const noMessagesEmbed = embedStyles.createWarningEmbed(
                    'Nenhuma Mensagem Encontrada',
                    `${embedStyles.format.bold('Não foi possível encontrar mensagens para deletar!')}\n\n${embedStyles.icons.info} **Possíveis motivos:**\n• Mensagens com mais de 14 dias não podem ser deletadas\n• Nenhuma mensagem do usuário especificado\n• Canal vazio ou sem mensagens recentes\n\n${embedStyles.format.italic('Tente ajustar os parâmetros da busca.')}`
                );
                return await interaction.editReply({ embeds: [noMessagesEmbed] });
            }

            // Deletar mensagens
            const deleted = await interaction.channel.bulkDelete(messagesToDelete, true);

            // Log da ação
            await interaction.client.database.logModerationAction(
                interaction.guild.id,
                targetUser?.id || 'all',
                interaction.user.id,
                'clear',
                reason,
                null,
                interaction.channel.id,
                null,
                {
                    amount: deleted.size,
                    channel: interaction.channel.name,
                    targetUser: targetUser?.tag || 'Todos'
                }
            );

            // Embed premium de confirmação
            const successFields = [
                {
                    name: `${embedStyles.icons.channel} **Canal Limpo**`,
                    value: `${interaction.channel}\n${embedStyles.format.code(interaction.channel.id)}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.shield} **Moderador**`,
                    value: `${interaction.user.tag}\n${embedStyles.format.code(interaction.user.id)}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.analytics} **Estatísticas**`,
                    value: `${embedStyles.format.bold('Removidas:')} ${deleted.size} mensagem(s)\n${embedStyles.format.bold('Solicitadas:')} ${amount}\n${embedStyles.format.bold('Filtro:')} ${targetUser ? targetUser.tag : 'Todas'}`,
                    inline: true
                },
                {
                    name: `${embedStyles.icons.info} **Detalhes da Operação**`,
                    value: `${embedStyles.format.bold('Tipo:')} ${targetUser ? 'Limpeza Direcionada' : 'Limpeza Geral'}\n${embedStyles.format.bold('Período:')} Últimas 14 dias\n${embedStyles.format.bold('Status:')} ${embedStyles.icons.success} Concluída com sucesso`,
                    inline: false
                },
                {
                    name: `${embedStyles.icons.info} **Motivo da Limpeza**`,
                    value: embedStyles.format.code(reason),
                    inline: false
                }
            ];

            const successEmbed = embedStyles.createModerationEmbed('clear', null, interaction.user, reason, successFields);

            await interaction.editReply({ embeds: [successEmbed] });

            // Enviar log para canal de moderação se configurado
            const guildConfig = await interaction.client.database.getGuildConfig(interaction.guild.id);
            if (guildConfig?.mod_log_channel_id) {
                const logChannel = interaction.guild.channels.cache.get(guildConfig.mod_log_channel_id);
                if (logChannel) {
                    const logEmbed = new EmbedBuilder()
                        .setColor('#ffa500')
                        .setTitle('🧹 Limpeza de Mensagens')
                        .setDescription(`**${deleted.size}** mensagem(s) foram removidas`)
                        .addFields(
                            {
                                name: '📍 Canal',
                                value: interaction.channel.toString(),
                                inline: true
                            },
                            {
                                name: '👤 Moderador',
                                value: `${interaction.user.tag} (${interaction.user.id})`,
                                inline: true
                            },
                            {
                                name: '🎯 Alvo',
                                value: targetUser ? `${targetUser.tag} (${targetUser.id})` : 'Todos os usuários',
                                inline: true
                            },
                            {
                                name: '📝 Motivo',
                                value: reason,
                                inline: false
                            }
                        )
                        .setFooter({
                            text: 'Sistema de Logs • Nodex | Moderação',
                            iconURL: interaction.client.user.displayAvatarURL()
                        })
                        .setTimestamp();

                    await logChannel.send({ embeds: [logEmbed] }).catch(() => {});
                }
            }

        } catch (error) {
            console.error('Erro no comando clear:', error);
            
            const errorMessage = error.code === 50013 ? 
                '❌ Não tenho permissão para deletar mensagens!' :
                '❌ Erro ao deletar mensagens!';

            if (interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
};
