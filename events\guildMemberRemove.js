/**
 * ========================================
 * EVENTO: GUILD MEMBER REMOVE
 * Executado quando um membro sai do servidor
 * ========================================
 */

const { Events, EmbedBuilder } = require('discord.js');

module.exports = {
    name: Events.GuildMemberRemove,
    async execute(member, client) {
        const guild = member.guild;
        const user = member.user;

        try {
            // Obter configurações do servidor do cache
            let guildConfig = client.guildConfigs?.get(guild.id);
            if (!guildConfig) {
                console.log(`📤 [MEMBER REMOVE] Configurações não encontradas para ${guild.name}`);
                return;
            }

            // Registrar saída no banco de dados
            await registerMemberLeave(member, client);

            // Log da saída
            await logMemberLeave(member, client, guildConfig);

            // Atualizar estatísticas do servidor
            await updateServerStats(member, client);

        } catch (error) {
            client.logger.error('Erro no evento guildMemberRemove:', error, {
                guild: guild.id,
                user: user.id,
                userTag: user.tag
            });
        }
    }
};

/**
 * Registra a saída do membro no banco de dados
 */
async function registerMemberLeave(member, client) {
    try {
        const guild = member.guild;
        const user = member.user;

        // Log da ação
        if (client.database) {
            client.database.logModerationAction(
                guild.id, user.id, null, 'member_leave', 
                'Usuário saiu do servidor', null, null, null,
                {
                    memberSince: member.joinedTimestamp,
                    roles: member.roles.cache.map(r => r.id),
                    username: user.username
                }
            );
        }

        client.logger.debug(`Saída registrada: ${user.tag} do servidor ${guild.name}`);

    } catch (error) {
        client.logger.error('Erro ao registrar saída do membro:', error);
    }
}

/**
 * Registra a saída no canal de logs
 */
async function logMemberLeave(member, client, guildConfig) {
    try {
        const guild = member.guild;
        const user = member.user;

        // Verificar se logs estão habilitados
        if (!guildConfig?.log_enabled) {
            console.log(`📤 [MEMBER REMOVE] Logs desabilitados para ${guild.name}`);
            return;
        }

        // Verificar se canal de logs está configurado
        const logChannelId = guildConfig?.log_channel_id || guildConfig?.log_channel;
        if (!logChannelId) {
            console.log(`📤 [MEMBER REMOVE] Canal de logs não configurado para ${guild.name}`);
            return;
        }

        const logChannel = guild.channels.cache.get(logChannelId);
        if (!logChannel || !logChannel.isTextBased()) {
            console.log(`📤 [MEMBER REMOVE] Canal de logs não encontrado: ${logChannelId}`);
            return;
        }
        
        console.log(`📤 [MEMBER REMOVE] Enviando log para #${logChannel.name} em ${guild.name}`);

        // Verificar permissões
        const permissions = logChannel.permissionsFor(guild.members.me);
        if (!permissions.has(['SendMessages', 'EmbedLinks'])) {
            return;
        }

        // Calcular tempo no servidor
        const timeInServer = member.joinedTimestamp ? Date.now() - member.joinedTimestamp : null;
        const daysInServer = timeInServer ? Math.floor(timeInServer / (1000 * 60 * 60 * 24)) : null;

        // Criar embed de log
        const logEmbed = new EmbedBuilder()
            .setColor('#e74c3c')
            .setTitle('📤 Membro Saiu')
            .setDescription(`${user} saiu do servidor`)
            .addFields(
                { 
                    name: '👤 Usuário', 
                    value: `${user.tag} (${user.id})`, 
                    inline: true 
                },
                { 
                    name: '📅 Entrou em', 
                    value: member.joinedTimestamp 
                        ? `<t:${Math.floor(member.joinedTimestamp / 1000)}:F>`
                        : 'Desconhecido', 
                    inline: true 
                },
                { 
                    name: '⏱️ Tempo no Servidor', 
                    value: daysInServer !== null 
                        ? `${daysInServer} dia(s)`
                        : 'Desconhecido', 
                    inline: true 
                },
                { 
                    name: '📊 Membros Restantes', 
                    value: `${guild.memberCount}`, 
                    inline: true 
                },
                { 
                    name: '⏰ Horário', 
                    value: `<t:${Math.floor(Date.now() / 1000)}:F>`, 
                    inline: true 
                }
            )
            .setThumbnail(user.displayAvatarURL({ size: 128 }))
            .setTimestamp()
            .setFooter({ text: 'Nodex | Moderação - Sistema de Logs' });

        // Adicionar cargos se houver
        if (member.roles.cache.size > 1) { // > 1 porque @everyone sempre está presente
            const roles = member.roles.cache
                .filter(role => role.id !== guild.id) // Remover @everyone
                .map(role => role.toString())
                .slice(0, 10); // Limitar a 10 cargos

            if (roles.length > 0) {
                logEmbed.addFields({
                    name: '🏷️ Cargos',
                    value: roles.join(', ') + (member.roles.cache.size > 11 ? '...' : ''),
                    inline: false
                });
            }
        }

        await logChannel.send({ embeds: [logEmbed] });

        client.logger.debug(`Log de saída enviado para ${user.tag}`);

    } catch (error) {
        client.logger.error('Erro ao enviar log de saída:', error);
    }
}

/**
 * Atualiza estatísticas do servidor
 */
async function updateServerStats(member, client) {
    try {
        const guild = member.guild;

        // Atualizar estatísticas no banco (se houver tabela de estatísticas)
        client.logger.debug(`Estatísticas atualizadas: ${guild.name} agora tem ${guild.memberCount} membros`);

    } catch (error) {
        client.logger.error('Erro ao atualizar estatísticas do servidor:', error);
    }
}
