/**
 * Script de teste para verificar se o cargo de não verificado está funcionando
 */

const DatabaseManager = require('./database/DatabaseManager');

async function testUnverifiedRole() {
    console.log('🧪 [TEST] Testando sistema de cargo de não verificado...\n');

    // Inicializar banco de dados
    const dbManager = new DatabaseManager();
    dbManager.initialize();
    const db = dbManager.db;

    const testGuildId = '1381755403326455838';

    try {
        // Teste 1: Verificar configuração atual
        console.log('📋 [TEST 1] Verificando configuração de verificação...');
        const config = db.prepare(`
            SELECT * FROM verification_config WHERE guild_id = ?
        `).get(testGuildId);
        
        if (config) {
            console.log('✅ Configuração encontrada:');
            console.log(`  - Habilitado: ${config.enabled ? 'SIM' : 'NÃO'}`);
            console.log(`  - Canal ID: ${config.channel_id}`);
            console.log(`  - Cargo Verificado ID: ${config.verified_role_id}`);
            console.log(`  - Cargo NÃO Verificado ID: ${config.unverified_role_id}`);
            console.log(`  - Método: ${config.method}`);
        } else {
            console.log('❌ Nenhuma configuração encontrada');
            return;
        }

        // Teste 2: Verificar se os IDs dos cargos são válidos
        console.log('\n📋 [TEST 2] Verificando validade dos IDs dos cargos...');
        
        if (!config.unverified_role_id || config.unverified_role_id === 'null' || config.unverified_role_id === 'undefined') {
            console.log('❌ Cargo de não verificado não está configurado ou tem valor inválido');
            console.log('💡 Dica: Configure um cargo válido no dashboard');
        } else {
            console.log(`✅ Cargo de não verificado configurado: ${config.unverified_role_id}`);
        }

        if (!config.verified_role_id || config.verified_role_id === 'null' || config.verified_role_id === 'undefined') {
            console.log('❌ Cargo de verificado não está configurado ou tem valor inválido');
        } else {
            console.log(`✅ Cargo de verificado configurado: ${config.verified_role_id}`);
        }

        // Teste 3: Simular configuração correta
        console.log('\n📋 [TEST 3] Testando configuração com cargo válido...');
        
        // Exemplo de cargo "Membros" comum em servidores
        const testUnverifiedRoleId = '1382425258975563997'; // Substitua pelo ID real do cargo
        
        db.prepare(`
            UPDATE verification_config 
            SET unverified_role_id = ?
            WHERE guild_id = ?
        `).run(testUnverifiedRoleId, testGuildId);
        
        console.log(`✅ Configuração atualizada com cargo de teste: ${testUnverifiedRoleId}`);

        // Teste 4: Verificar logs de aplicação de cargo
        console.log('\n📋 [TEST 4] Verificando logs de verificação...');
        
        const recentLogs = db.prepare(`
            SELECT * FROM verification_logs 
            WHERE guild_id = ? 
            ORDER BY timestamp DESC 
            LIMIT 5
        `).all(testGuildId);

        if (recentLogs.length > 0) {
            console.log(`✅ Encontrados ${recentLogs.length} logs recentes:`);
            recentLogs.forEach((log, index) => {
                console.log(`  ${index + 1}. ${log.action} - ${log.method} - ${log.timestamp}`);
            });
        } else {
            console.log('ℹ️ Nenhum log de verificação encontrado');
        }

        // Teste 5: Verificar membros não verificados
        console.log('\n📋 [TEST 5] Verificando membros não verificados...');
        
        const unverifiedMembers = db.prepare(`
            SELECT * FROM member_verification 
            WHERE guild_id = ? AND verified = 0
            ORDER BY last_attempt DESC
            LIMIT 5
        `).all(testGuildId);

        if (unverifiedMembers.length > 0) {
            console.log(`✅ Encontrados ${unverifiedMembers.length} membros não verificados:`);
            unverifiedMembers.forEach((member, index) => {
                console.log(`  ${index + 1}. User ID: ${member.user_id} - Tentativas: ${member.attempts}`);
            });
        } else {
            console.log('ℹ️ Nenhum membro não verificado encontrado');
        }

        console.log('\n🎯 [DIAGNÓSTICO] Possíveis problemas e soluções:');
        console.log('1. ❌ Cargo não configurado:');
        console.log('   💡 Solução: Configure um cargo válido no dashboard');
        console.log('2. ❌ Bot sem permissão:');
        console.log('   💡 Solução: Verifique se o bot tem permissão "Gerenciar Cargos"');
        console.log('3. ❌ Cargo acima do bot:');
        console.log('   💡 Solução: Mova o cargo do bot acima do cargo de não verificado');
        console.log('4. ❌ ID do cargo inválido:');
        console.log('   💡 Solução: Verifique se o cargo ainda existe no servidor');

        console.log('\n🔧 [INSTRUÇÕES] Para testar:');
        console.log('1. Configure um cargo de "não verificado" no dashboard');
        console.log('2. Faça um usuário entrar no servidor');
        console.log('3. Verifique os logs do bot para ver se o cargo foi aplicado');
        console.log('4. Se não funcionar, verifique as permissões do bot');

        console.log('\n🎉 [TEST] Teste de cargo de não verificado concluído!');

    } catch (error) {
        console.error('❌ [TEST] Erro durante o teste:', error);
    } finally {
        db.close();
    }
}

// Executar teste se chamado diretamente
if (require.main === module) {
    testUnverifiedRole();
}

module.exports = { testUnverifiedRole };
